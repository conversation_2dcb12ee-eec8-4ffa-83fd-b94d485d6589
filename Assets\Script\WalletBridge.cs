using UnityEngine;
using System.Runtime.InteropServices;

public class WalletBridge : MonoBehaviour
{
#if UNITY_WEBGL && !UNITY_EDITOR
    [DllImport("__Internal")]
    private static extern void RequestWalletConnect();
#endif

    public void ConnectWallet()
    {
#if UNITY_WEBGL && !UNITY_EDITOR
        RequestWalletConnect();
#else
        Debug.Log("ConnectWallet: Only available in WebGL build");
#endif
    }

    public void OnWalletConnected(string address)
    {
        Debug.Log("Wallet connected: " + address);
        // Add logic to handle connecting to the server
    }
}
