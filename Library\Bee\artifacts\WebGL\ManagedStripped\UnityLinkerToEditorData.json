{"report": {"modules": [{"name": "Animation", "dependencies": [{"name": "Animator", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "AnimatorController", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "AnimatorOverrideController", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "RuntimeAnimatorController", "scenes": [], "dependencyType": 1, "icon": null}]}, {"name": "Audio", "dependencies": [{"name": "AudioBehaviour", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioClip", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "AudioListener", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "SampleClip", "scenes": [], "dependencyType": 1, "icon": null}]}, {"name": "Core", "dependencies": [{"name": "Behaviour", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "BuildSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Camera", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "Component", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Cubemap", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "CubemapArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "DelayedCallManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "EditorExtension", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 1, "icon": null}, {"name": "GameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GameObject", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "GlobalGameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GraphicsSettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "InputManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "LevelGameManager", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 1, "icon": null}, {"name": "Light", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "LightProbes", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "LightingSettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "LightmapSettings", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "LowerResBlitTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Material", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "MonoBehaviour", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "MonoManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "MonoScript", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 1, "icon": null}, {"name": "NamedObject", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 1, "icon": null}, {"name": "Object", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PlayerSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PreloadData", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "QualitySettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "RectTransform", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "ReflectionProbe", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "RenderSettings", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "RenderTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "ResourceManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RuntimeInitializeOnLoadManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Shader", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "ShaderNameRegistry", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "SortingGroup", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Sprite", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "SpriteAtlas", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "TagManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "TextAsset", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2D", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2DArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Texture3D", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "TimeManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Transform", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by WebGL Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.webgl"}]}, {"name": "IMGUI", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "InputLegacy", "dependencies": [{"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "JSONSerialize", "dependencies": [{"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "RuntimeInitializeOnLoadManagerInitializer", "dependencies": [{"name": "RuntimeInitializeOnLoadManagerInitializer is always required", "scenes": [], "dependencyType": 3, "icon": "class/DefaultAsset"}]}, {"name": "SharedInternals", "dependencies": [{"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by Core Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.core"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by WebGL Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.webgl"}]}, {"name": "TextCoreFontEngine", "dependencies": [{"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "TextCoreTextEngine", "dependencies": [{"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "TextRendering", "dependencies": [{"name": "Font", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "UI", "dependencies": [{"name": "<PERSON><PERSON>", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "CanvasGroup", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/ConnectWallet.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "UIElements", "dependencies": []}, {"name": "WebGL", "dependencies": [{"name": "WebGL is always required", "scenes": [], "dependencyType": 3, "icon": "class/DefaultAsset"}]}]}}