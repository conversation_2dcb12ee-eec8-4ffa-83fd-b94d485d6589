{ "pid": 43648, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON><PERSON><PERSON>" } },
{ "pid": 43648, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 43648, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289989270479, "dur": 2675, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289989311127, "dur": 3131, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289989699267, "dur": 31308, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289989906070, "dur": 116977, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289990355699, "dur": 94104, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289990510506, "dur": 86739, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289991270226, "dur": 142891, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289991728981, "dur": 187309, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 12884901888, "ts": 1754289992607881, "dur": 163950, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 43648, "tid": 1, "ts": 1754289993050473, "dur": 3128, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 43648, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289989549495, "dur": 7878, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289989699269, "dur": 14843, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289989791610, "dur": 36204, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289989906072, "dur": 41738, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289990144399, "dur": 88407, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289990472920, "dur": 19851, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289990613324, "dur": 16940, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289991114360, "dur": 9751, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289991270228, "dur": 20525, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289991543803, "dur": 20296, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289991728983, "dur": 24038, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289992113127, "dur": 51062, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 8589934592, "ts": 1754289992833325, "dur": 55357, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 1, "ts": 1754289993053613, "dur": 12, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 43648, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289989344969, "dur": 6703, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289989669703, "dur": 11725, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289989753984, "dur": 14732, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289989850981, "dur": 14273, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289990051977, "dur": 38020, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289990268776, "dur": 25435, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289990355701, "dur": 31249, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289990510508, "dur": 20424, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289990831263, "dur": 2851, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289990887918, "dur": 3524, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289990913045, "dur": 3233, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289991183225, "dur": 18632, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289991453641, "dur": 21697, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289991639533, "dur": 21389, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289991949753, "dur": 23413, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289992287873, "dur": 20578, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 4294967296, "ts": 1754289992607884, "dur": 21978, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 43648, "tid": 1, "ts": 1754289993053627, "dur": 134, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 43648, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 43648, "tid": 1, "ts": 1754289989087677, "dur": 3940195, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 43648, "tid": 1, "ts": 1754289989089277, "dur": 48876, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989093712, "dur": 23657, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989138155, "dur": 11583, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989172007, "dur": 26271, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989200351, "dur": 102029, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989302427, "dur": 50207, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989352659, "dur": 37713, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989390385, "dur": 137385, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989527780, "dur": 4595, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989532380, "dur": 26341, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989558733, "dur": 1303, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989560044, "dur": 10738, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989570787, "dur": 464, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989571253, "dur": 2636, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989573892, "dur": 3486, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989577382, "dur": 952, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989578336, "dur": 899, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989579248, "dur": 5253, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989584512, "dur": 7061, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989591580, "dur": 2104, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989593689, "dur": 1420, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989595118, "dur": 1423, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989596545, "dur": 671, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989597222, "dur": 945, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989598171, "dur": 6164, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989604339, "dur": 2698, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989607038, "dur": 130, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989607169, "dur": 4010, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989611182, "dur": 1750, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989612934, "dur": 813, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989613761, "dur": 31697, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989645467, "dur": 8541, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989654018, "dur": 227678, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989881715, "dur": 415, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989882137, "dur": 693, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989882836, "dur": 244, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989883085, "dur": 873, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989883963, "dur": 641, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989884609, "dur": 3984, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989888595, "dur": 218, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289989888816, "dur": 213976, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990102824, "dur": 14277, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990117114, "dur": 221304, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990338437, "dur": 293, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990338737, "dur": 301203, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990639955, "dur": 23901, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990663882, "dur": 3082, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990666974, "dur": 18496, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990685483, "dur": 27028, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289990712525, "dur": 1508368, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992220906, "dur": 714, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992221626, "dur": 14290, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992235939, "dur": 76176, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992312128, "dur": 4682, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992316818, "dur": 4212, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992321038, "dur": 2468, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992323510, "dur": 2952, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992326467, "dur": 3082, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992329554, "dur": 161, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992329720, "dur": 379, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992330103, "dur": 541, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992330651, "dur": 659512, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289992990178, "dur": 19689, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289993009878, "dur": 208, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289993013032, "dur": 14666, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289993027875, "dur": 14622, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289993053762, "dur": 74, "ph": "X", "name": "", "args": {} },
{ "pid": 43648, "tid": 1, "ts": 1754289993049990, "dur": 4214, "ph": "X", "name": "Write chrome-trace events", "args": {} },
