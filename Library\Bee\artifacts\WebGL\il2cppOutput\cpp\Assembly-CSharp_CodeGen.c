﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void WalletBridge_RequestWalletConnect_mAEB8B998DAAB7B13908EE6CCAFABAC054D8D6EC1 (void);
extern void WalletBridge_ConnectWallet_m5C141351E53343D870277F091FF74C6E880D47F3 (void);
extern void WalletBridge_OnWalletConnected_m67FB7666615679F299886DB032B11BA104BB3EED (void);
extern void WalletBridge__ctor_m9E443BA79CC3C3E478F1580E0EFCE5CF736A38A6 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1 (void);
static Il2CppMethodPointer s_methodPointers[6] = 
{
	WalletBridge_RequestWalletConnect_mAEB8B998DAAB7B13908EE6CCAFABAC054D8D6EC1,
	WalletBridge_ConnectWallet_m5C141351E53343D870277F091FF74C6E880D47F3,
	WalletBridge_OnWalletConnected_m67FB7666615679F299886DB032B11BA104BB3EED,
	WalletBridge__ctor_m9E443BA79CC3C3E478F1580E0EFCE5CF736A38A6,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1,
};
static const int32_t s_InvokerIndices[6] = 
{
	6254,
	4370,
	3557,
	4370,
	6258,
	4370,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	6,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
