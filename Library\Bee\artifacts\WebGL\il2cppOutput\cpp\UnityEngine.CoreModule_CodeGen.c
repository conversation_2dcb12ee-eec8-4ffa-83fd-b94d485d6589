﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_m93D23E0241AEA98DE88285ECFB378AAD361CDC83 (void);
extern void IsUnmanagedAttribute__ctor_m0C60E16DF38A964480B9B8C9A714838FED66E804 (void);
extern void MathfInternal__cctor_m405C235849BE19E6F82F403A518E0E2958F043AE (void);
extern void TypeInferenceRuleAttribute__ctor_m356F2A18ACB338DE48EADD04E97C971C554EDDE0 (void);
extern void TypeInferenceRuleAttribute__ctor_mECD854F0BC27EC215F4BD1C6F0CECA9BEC090E0D (void);
extern void TypeInferenceRuleAttribute_ToString_m009A152A07FC88BF49F1C72FC615F2FB7350F2BD (void);
extern void GenericStack__ctor_mD21753D674298C09F3684F19DD42680323055586 (void);
extern void JobHandle_Complete_mDCED35A125AAB37EDDAB2E31C805B4904B614A4A (void);
extern void JobHandle_ScheduleBatchedJobs_mCA0E146397E30E31FB57C96DAA1820451886DACA (void);
extern void JobHandle_ScheduleBatchedJobsAndComplete_m2F3EDE00C3539E9FD3F0BDB88A83934295D46DF7 (void);
extern void JobHandle_CombineDependencies_m5B482F42E4C9CAC22FF24C1BF76F3AFB870DBB3E (void);
extern void JobHandle_CombineDependencies_m97D94C038D7FAF721612B7BBE2147B05FCFF5C40 (void);
extern void JobHandle_CombineDependenciesInternalPtr_m5C891AA874B6FE7E553F177DBBD7E5C106C9B7D9 (void);
extern void JobHandle_Equals_mD29DF760383603EABB2E626975DE8EB2F2A6234D (void);
extern void JobHandle_CombineDependenciesInternalPtr_Injected_m73A10C581A7FDBCCB1E71C1EC2F882F94B6640E1 (void);
extern void JobProducerTypeAttribute__ctor_m562A2FC62E2DF7109DD703C0270B0B372607C534 (void);
extern void JobsUtility_GetWorkStealingRange_mEFCE7247B469F69E0590111B435E0010BFB8F73A (void);
extern void JobsUtility_ScheduleParallelFor_mEF775B0FF09686D0525DDFA9ED95A72EEDEC995C (void);
extern void JobsUtility_CreateJobReflectionData_m1A2233D7CF938F4C9ECE371DF271C8EC20D12664 (void);
extern void JobsUtility_CreateJobReflectionData_m17265DED7C2DBB0B2130896E2B8AC4CF5BD7FCE7 (void);
extern void JobsUtility_InvokePanicFunction_mDD3D5F9B1F0A26924CC82DB52E0561D32DC8E255 (void);
extern void JobsUtility_ScheduleParallelFor_Injected_mFB3DF4AB62DD13FB4CE2311E7441DCE239129A64 (void);
extern void JobScheduleParameters__ctor_m5FFED3B28A1FA2C3EC7D1C50A7D7E788C411CE04 (void);
extern void PanicFunction___ctor_m17297514778BEA5F35E562880A6C317CF34856DF (void);
extern void PanicFunction__Invoke_m1AF64BA1DC405B9451341076B6E099CBB173A12A (void);
extern void Il2CppEagerStaticClassConstructionAttribute__ctor_m124F17545F01CC678CE74870FC9EE5D4891EE935 (void);
extern void IgnoredByDeepProfilerAttribute__ctor_mE21E2304501A1CE16AE545436B1FF557CE51C1DA (void);
extern void ProfilerCategory__ctor_m59B0D65E2CE7D723F30A4FAA5796A1CBE105B298 (void);
extern void ProfilerCategory_get_Name_mFED02A355294B8B0365E03D12BC1299E37442894 (void);
extern void ProfilerCategory_ToString_m091164890366F89EFBFC0FF811B897C234B67541 (void);
extern void ProfilerCategory_get_Scripts_m84B0F774438E512DFAF8FD21E0FBE76F00419AFE (void);
extern void ProfilerCategory_op_Implicit_m441AE38B56781EE2D3F0865F65C81A77BEC6D76B (void);
extern void ProfilerMarker__ctor_mDD68B0A8B71E0301F592AF8891560150E55699C8 (void);
extern void ProfilerMarker__ctor_m5958260A54C3A7F358A71AACDF47BA28178A5AB7 (void);
extern void ProfilerMarker_Auto_m133FA724EB95D16187B37D2C8A501D7E989B1F8D (void);
extern void AutoScope__ctor_m7F63A273E382CB6328736B6E7F321DDFA40EA9E3 (void);
extern void AutoScope_Dispose_mED763F3F51261EF8FB79DB32CD06E0A3F6C40481 (void);
extern void DebugScreenCapture_set_RawImageDataReference_m935F402BCD29599C153DF8B982FAAE26FC1F9F24 (void);
extern void DebugScreenCapture_set_ImageFormat_mEB839CF83D4271BEBDE907158D8F2FBC9CB88FFE (void);
extern void DebugScreenCapture_set_Width_m4C018D3EECDEDCA9EED150FE159218A403210451 (void);
extern void DebugScreenCapture_set_Height_m079B049644D69F95F746269B437D8DEBEC3701E7 (void);
extern void ProfilerUnsafeUtility_CreateCategory__Unmanaged_mA424CE7A67002B0B92C43C6B1D151FC1FF858A56 (void);
extern void ProfilerUnsafeUtility_GetCategoryDescription_m7C69231967E0B9EDD5D41B362A352897373C3391 (void);
extern void ProfilerUnsafeUtility_CreateMarker_mC5E1AAB8CC1F0342065DF85BA3334445ED754E64 (void);
extern void ProfilerUnsafeUtility_CreateMarker__Unmanaged_mB19C1DFE1BC2155D3744E5A79DE73418234DDA22 (void);
extern void ProfilerUnsafeUtility_SetMarkerMetadata__Unmanaged_m3D9F5F66E63969EF79D5F7548DB4464CEE9181C2 (void);
extern void ProfilerUnsafeUtility_BeginSample_mB5106F4E7ECEF54906545665ED23928D14F5FCA7 (void);
extern void ProfilerUnsafeUtility_EndSample_mFDB4EFB160A9CB817D2F8ED21B88693616B27409 (void);
extern void ProfilerUnsafeUtility_CreateCounterValue__Unmanaged_m0CF807211082E8E242EFD66417B15AC600C86C05 (void);
extern void ProfilerUnsafeUtility_Utf8ToString_mB64501855C1C01B54BFEDFDE0D2CAEAD2A5353E8 (void);
extern void ProfilerUnsafeUtility_GetCategoryDescription_Injected_m1971DD6E5FB42E96C147FE3B55870F2947E050AC (void);
extern void MemorySnapshotMetadata_get_Description_mF22499F79675403674B8B45F78C23E7BEC2BC88D (void);
extern void MemorySnapshotMetadata_set_Description_m00757DCA7E64343633755932EC6F15A1B61A4621 (void);
extern void MemorySnapshotMetadata_get_Data_mB61CE35A8C57F83EE5582E10A0B306CBC0BA3624 (void);
extern void MemorySnapshotMetadata__ctor_mE8ED135D7BD5CBE5D2B849B160B3D7EBBE5B468B (void);
extern void MemoryProfiler_PrepareMetadata_m329170F1BBB5E061A086FC61FC03EF452094F6CF (void);
extern void MemoryProfiler_WriteIntToByteArray_m788E8C96D88066FF17DA6A86FF28EBD14CF69C7A (void);
extern void MemoryProfiler_WriteStringToByteArray_mCC3816A5BEA5C0C7D672D17DFC3985B33C0FAEC7 (void);
extern void MemoryProfiler_FinalizeSnapshot_mDB37521587585D8FF80D9E32764F2BB33BBB1FAD (void);
extern void MemoryProfiler_SaveScreenshotToDisk_m864CDEF0A856BC92783884E8D5F92ADEE88816D5 (void);
extern void NativeArrayDispose_Dispose_mC90E863B89568E988E0F8FB8E79596A72C5EE061 (void);
extern void NativeArrayDisposeJob_Execute_m2F3CAEB5BFACF52C44D2A2485554D88018650A7F (void);
extern void NativeArrayDisposeJob_RegisterNativeArrayDisposeJobReflectionData_mDDD42E5EAFDCF8373611DFF7CEDB64712DA55895 (void);
extern void SharedStatic_GetOrCreateSharedStaticInternal_mA2EDCD03A23E5C9D8AC312E73F81232E126DA304 (void);
extern void BurstRuntime_HashStringWithFNV1A64_m3E38919BF51D34D084240B8B6EFB6A753411A335 (void);
extern void NativeContainerAttribute__ctor_m9249B57B7D6F9E1D425EC32392FB975B8568AD9B (void);
extern void NativeContainerSupportsMinMaxWriteRestrictionAttribute__ctor_m340DBE8A3B007A450B4643E7350C29D92514B782 (void);
extern void NativeContainerSupportsDeallocateOnJobCompletionAttribute__ctor_mBE27310D46174944DF46D01B8AE4BBE69DA94729 (void);
extern void NativeContainerSupportsDeferredConvertListToArray__ctor_m40F59B7627032F45D2ED023BA9525EEF40660C27 (void);
extern void WriteAccessRequiredAttribute__ctor_mB36E4CF6313543DEB0DFA64575446C5ABB6F4C8B (void);
extern void NativeDisableUnsafePtrRestrictionAttribute__ctor_m8E1BE43B9791FD4E5790060D6506003D35F081CC (void);
extern void NativeDisableContainerSafetyRestrictionAttribute__ctor_mA6D239ECE04E6D05432C3ACB98ED6CA92C4371A1 (void);
extern void UnsafeUtility_MallocTracked_m618762A86F170FB10656114217D3D125D60CE297 (void);
extern void UnsafeUtility_FreeTracked_mB96B3C035F2CD1517BF8C29218CBD7B710B3279A (void);
extern void UnsafeUtility_MemCpy_m5CEA91ACDADC522E584AE3A2AB2B0B74393A9177 (void);
extern void UnsafeUtility_MemCpyStride_mD7836B76B4F1E7F30DFC859D4E6D7242AFD27C90 (void);
extern void UnsafeUtility_MemSet_m4CD74CD43260EB2962A46F57E0D93DD5C332FC2B (void);
extern void UnsafeUtility_MemClear_m6C4377117084A11A667A567BC2F5E606A632A7C1 (void);
extern void BurstDiscardAttribute__ctor_m0AC3131F7C5B377DCA604CD7BB8AC4AA4E161033 (void);
extern void BurstCompilerService_GetOrCreateSharedMemory_m1293EB3119CBEE41DBCC0E3B2235601BD927BFE6 (void);
extern void NotNullAttribute__ctor_mBD764326957BF5036FB7217B794F6B48B73A6B51 (void);
extern void PureAttribute__ctor_mEAFD7538811204E5F43B816F28B63FA197A6A09A (void);
extern void SortingLayer_GetLayerValueFromID_mEB8A5234102CD7B2C6158661A931EA120A38707C (void);
extern void AnimationCurve_Internal_Destroy_m240B298D0A13EEC1652955C4BDCDBE9B7B2EE296 (void);
extern void AnimationCurve_Internal_Create_m638BB04F32C3013A7CA810879E46FD9F32E9FE05 (void);
extern void AnimationCurve_Internal_Equals_mEBA61732FE57654C4E3AF5B317918D1641E36050 (void);
extern void AnimationCurve_Finalize_m803AC16166EE497C4DFA996B15692D91F4D04C3C (void);
extern void AnimationCurve_GetHashCode_m1AEBC4C469357E1FA2541CC4B8D2B39C284CF8F6 (void);
extern void AnimationCurve__ctor_mEABC98C03805713354D61E50D9340766BD5B717E (void);
extern void AnimationCurve__ctor_m0D976567166F92383307DC8EB8D7082CD34E226F (void);
extern void AnimationCurve_Equals_mE47717A57755581C546986799C9DBC64D98928A9 (void);
extern void AnimationCurve_Equals_mC44657401804A22DCA648BD8495FC4E8A2F088A3 (void);
extern void Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34 (void);
extern void Application_get_isFocused_mFEEC52E355AA7AAA6B7250520CA544D80BE77524 (void);
extern void Application_get_isBatchMode_mDE2DA11B2DAC8D8239BACD75A56A6EE6BAA4DC36 (void);
extern void Application_OpenURL_mE748FA4D503715DEE12BCA4BDD8A5305AE41DB89 (void);
extern void Application_get_platform_m59EF7D6155D18891B24767F83F388160B1FF2138 (void);
extern void Application_CallLowMemory_m89AE742136DDE1754212C8B3092481F3DFA41CC3 (void);
extern void Application_HasLogCallback_mEF8E25750ECCAAB596ACBF7CF75B2BD43B3157CC (void);
extern void Application_CallLogCallback_m8E71361EED127C3D753168559078020878A0592C (void);
extern void Application_Internal_ApplicationWantsToQuit_mB7CD318CF9B29D34E26D2E25F460957D41D26854 (void);
extern void Application_Internal_ApplicationInit_mE781BDF7FB93F32B339F1B52300088309CC21C79 (void);
extern void Application_Internal_ApplicationQuit_m4E0E8DE66A2E435A1A4EFF21CBCD2132895AFF84 (void);
extern void Application_Internal_ApplicationUnload_mBACA14D6291E2FD79766019FBA0A48EBE16AFFAB (void);
extern void Application_InvokeOnBeforeRender_mBD6251514058981E187B0D695C02CB00B983B963 (void);
extern void Application_InvokeFocusChanged_m85307C7E83A7D2314E44CF0B505D1158B4A1D58A (void);
extern void Application_InvokeDeepLinkActivated_m9484D7A6C4CB0DD73B83D5F442FB5620CE9F1136 (void);
extern void Application_get_isEditor_mEAC51E3ACE6DCE438087FB14BD75A3C219D354D0 (void);
extern void Application__cctor_m5B1DED32A96960B4E37EA614575B6366BCDFD549 (void);
extern void LowMemoryCallback__ctor_m3B5E06F22A115B898C51A5C4B20166E2A92E0375 (void);
extern void LowMemoryCallback_Invoke_m3B2AE4EC5556D1D98237EF965477487AE4C7D708 (void);
extern void MemoryUsageChangedCallback__ctor_m01FF11979DFBEDA8CC5051D939338C23C5BF929C (void);
extern void MemoryUsageChangedCallback_Invoke_m3C0E629B4D924D71D84DED6B9C78BD62263F9F1F (void);
extern void LogCallback__ctor_m327A4C69691F8A4B01D405858E48B8A7D9D2A79D (void);
extern void LogCallback_Invoke_m88EACBF170351AE6FC8E6F5154CD09179D67BB47 (void);
extern void ApplicationMemoryUsageChange_set_memoryUsage_m331F962287453AC69EEE1222C0F11D222F7B2957 (void);
extern void ApplicationMemoryUsageChange__ctor_mD1B7299FCDDF14B479AF66AFDDFC2D710AC6A3C0 (void);
extern void BootConfigData_WrapBootConfigData_m47132663C7FB94C3CA9627E5955C1848165AEF72 (void);
extern void BootConfigData__ctor_m67B06923C40A5363192040A89BB92479B4B74A9A (void);
extern void Camera__ctor_m6D8568D99066C871656AC9A712A43D96A413BEBA (void);
extern void Camera_get_nearClipPlane_m5E8FAF84326E3192CB036BD29DCCDAF6A9861013 (void);
extern void Camera_get_farClipPlane_m1D7128B85B5DB866F75FBE8CEBA48335716B67BD (void);
extern void Camera_get_depth_mDF67FFF8ED61750467DFC4C6D8F236850AD1BB1D (void);
extern void Camera_get_cullingMask_m6F5AFF8FB522F876D99E839BF77D8F27F26A1EF8 (void);
extern void Camera_get_eventMask_mEBACB61FFA0C8AAF3495454BABA50EE70655290A (void);
extern void Camera_get_clearFlags_mA74F538C124B391EF03C46A50CA7FF7B505B7602 (void);
extern void Camera_get_pixelRect_m5F40F5C324EB252261F66962411EE08CC4BE39E7 (void);
extern void Camera_get_targetTexture_mC856D7FF8351476068D04E245E4F08F5C56A55BD (void);
extern void Camera_get_targetDisplay_m204A169C94EEABDB491FA5A77CC684146B10DF80 (void);
extern void Camera_WorldToScreenPoint_m6612AF37FFBBACC568877D4AA3AD5F11C76D9657 (void);
extern void Camera_WorldToScreenPoint_m26B4C8945C3B5731F1CC5944CFD96BF17126BAA3 (void);
extern void Camera_ScreenToViewportPoint_m8907015773080F63D1034CEDEDEA4AF14FB2F3C5 (void);
extern void Camera_ScreenPointToRay_mA8EEC93B0731C859D2FF29D7DECFB806E3D9F0CC (void);
extern void Camera_ScreenPointToRay_mA27CE345E80542143237233D503A71392594AA9B (void);
extern void Camera_ScreenPointToRay_m2887B9A49880B7AB670C57D66B67D6A6689FE315 (void);
extern void Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF (void);
extern void Camera_get_current_m25217A02CB09E3BD50E3E0327879E870AD58C6C3 (void);
extern void Camera_GetAllCamerasCount_m349F020DACEA1A160FB99917EFAED041316D4CCB (void);
extern void Camera_GetAllCamerasImpl_m9267137E1EBF23B51A2A5842C0DA9A333142E29C (void);
extern void Camera_get_allCamerasCount_m7B9CAA9E8F2BC5587942A4CCBF4D6CA1FFD01BDC (void);
extern void Camera_GetAllCameras_m55D302710121EEBC17D2F6EE4AE975C37ECC53F4 (void);
extern void Camera_SetupCurrent_m564B3A5A985E2DF0C7CE924FD93CD9545002C515 (void);
extern void Camera_FireOnPreCull_mE55D48DC9F94241A79F59C53FCEC0E6B81FB2CA6 (void);
extern void Camera_FireOnPreRender_m543170A0D796CFF44B97DAC68C2208F061143ADA (void);
extern void Camera_FireOnPostRender_m5C39B4A52A27EE6D9FC15A44518B60CEEDCA6EF5 (void);
extern void Camera_get_pixelRect_Injected_mA85B24A9EDBD279189D560BD46B611E69C8EEDA3 (void);
extern void Camera_WorldToScreenPoint_Injected_m3D83428A7AC8033EB4D357DF55BFA2ED652DEB64 (void);
extern void Camera_ScreenToViewportPoint_Injected_m08D0DAE62A0BAF0443871DAF87FE4B3F18CC45C2 (void);
extern void Camera_ScreenPointToRay_Injected_m9B7E1A86A79D7578D03345360E86AED3C8D09C59 (void);
extern void CameraCallback__ctor_mB48D13F30E749B551E4692E4F2D762C375F62B41 (void);
extern void CameraCallback_Invoke_m67E54F44AA3C83F0D9EF47A5FBC6AF43B0B26362 (void);
extern void CullingGroup_SendEvents_m90EC6607039ADA3126DD4650881B3E38BA30D4C1 (void);
extern void StateChanged__ctor_m1D704B739C7C0F0D91873D24D10A92942826F0C9 (void);
extern void StateChanged_Invoke_m050DE641598E0C558A8CC865EAD679194581182C (void);
extern void ReflectionProbe_CallReflectionProbeEvent_m10D7A2D70D6814FD80DAEC78E09268D7ADA98C7E (void);
extern void ReflectionProbe_CallSetDefaultReflection_m7CF493CA345676C58B735C0AE506A248037BE89B (void);
extern void ReflectionProbe__cctor_mD074E46A055B388464E1D0B487885ED8F1887A4A (void);
extern void DebugLogHandler_Internal_Log_m20852F18A88BB18425BA07260545E3968F7EA76C (void);
extern void DebugLogHandler_Internal_LogException_mC3895F1E5B79BD8151B3F94462F5516A68AB26AF (void);
extern void DebugLogHandler_LogFormat_m216B169EF9B669F2ED4C59F6B9F326D4EBBDF821 (void);
extern void DebugLogHandler_LogFormat_m21A399AC8FD346A226DADB661E8009DCA79D0DE8 (void);
extern void DebugLogHandler_LogException_mF66A663A86BF5D412BC9A4B3F92DA5CF61049F08 (void);
extern void DebugLogHandler__ctor_m1078DDBE735090C0215BFDF2409A376F10F7A070 (void);
extern void Debug_get_unityLogger_m4FDE4D41C187123244FE13124DA636BB50C9C1E1 (void);
extern void Debug_ExtractStackTraceNoAlloc_mEA19F1BD13F74CCE53CED8FA2C07380772C58FB6 (void);
extern void Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB (void);
extern void Debug_Log_m06155ED25645EBBC06B4C8F05235EF41B1489C7E (void);
extern void Debug_LogFormat_m37A43E151078921783B62F980955A0EAFFA4CBA6 (void);
extern void Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2 (void);
extern void Debug_LogError_m94F967AB31244EACE68C3BE1DD85B69ED3334C0E (void);
extern void Debug_LogErrorFormat_m96690322C941D23A125E5769C9803606859A707C (void);
extern void Debug_LogErrorFormat_m13C2B705E2899F85833797AA48E8416052FFAD5A (void);
extern void Debug_LogException_mAB3F4DC7297ED8FBB49DAA718B70E59A6B0171B0 (void);
extern void Debug_LogException_mD4CF3A9C64D8D4BA0570D529E705D134A9A5E498 (void);
extern void Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9 (void);
extern void Debug_LogWarning_m23033D7E2F0F298BE465B7F3A63CDF40A4EB70EB (void);
extern void Debug_LogWarningFormat_mD8224DEBCB6050F4E2BF55151F0C6A29B87DEFBC (void);
extern void Debug_LogWarningFormat_mEC3E8902D2EF0D6CDC7D2643D75DF8A7A6F84713 (void);
extern void Debug_Assert_m6E778CACD0F440E2DEA9ACDD9330A22DAF16E96D (void);
extern void Debug_Assert_mA460392021AC0A8210C9081E3C1C9652DBF32BF6 (void);
extern void Debug_LogAssertion_m71A412A496EAB476FFF6253618C706B2F1E4AFF0 (void);
extern void Debug_LogAssertionFormat_mBA7469D6C6033C5D45F9B7F2578BFDE7EF1DB578 (void);
extern void Debug_CallOverridenDebugHandler_mB78E42B7792823F50830A325BB1CF50C45FEAA8B (void);
extern void Debug_IsLoggingEnabled_mAB371F62AA458AC5D6CF25ACD58A1C951F3EFD01 (void);
extern void Debug__cctor_m9D586A54F5AF4804DA5FEC7549187109B05F7831 (void);
extern void ExpressionEvaluator_EvaluateDouble_m71E6969B279D8C6CBA9F791C72C7060723485285 (void);
extern void ExpressionEvaluator_InfixToRPN_m9FBA3B21BB31EE4FA9E386D4BEBB209F6639E8E5 (void);
extern void ExpressionEvaluator_NeedToPop_m072F0CF46B080F707C0A006F2B799AF147A10926 (void);
extern void ExpressionEvaluator_ExpressionToTokens_m747EBEA2A4BDBA0F16E4284CFB0A926E6A4E350D (void);
extern void ExpressionEvaluator_IsCommand_m284D94B1E0B6A7D150CD0E10685295DE3EBFFC51 (void);
extern void ExpressionEvaluator_IsVariable_m911973EE168593DAF621784B2269EEEBCFCC7FAC (void);
extern void ExpressionEvaluator_IsDelayedFunction_mDFF1026B946A73CBBE83D9702C5D5CC5B22A693C (void);
extern void ExpressionEvaluator_IsOperator_m2E59B892CAD05E19661608528FC1726A089678E8 (void);
extern void ExpressionEvaluator_TokenToOperator_mE2F2E3298DF6B1EFE3208CEE9D0C0FFB611EECD5 (void);
extern void ExpressionEvaluator_PreFormatExpression_mFB5CD012219ADB8F4285CC17959B55C373F4D2B4 (void);
extern void ExpressionEvaluator_FixUnaryOperators_m6E35555535A294B25FFB7D3AD8D5497CB66E3429 (void);
extern void ExpressionEvaluator_EvaluateOp_m23DBADDB2BEDC645C36EB6FE80931BBD7D9B2094 (void);
extern void ExpressionEvaluator__cctor_mF48F33D70CD19BFAE74ACFF4212A1333E7260EE5 (void);
extern void Expression__ctor_mCA43087EA66CD3F2E0E8F34CB1A3C65918DCB401 (void);
extern void Expression_Equals_mE905BB68355581FA84E9B62547AA78B07FB39B48 (void);
extern void Expression_GetHashCode_m032D8211AB706936EFE29A6F0116CC8DB419FA28 (void);
extern void Expression_ToString_mDE463408848472DB7DD0931BE220E89DF3A435C3 (void);
extern void PcgRandom__ctor_m60661E44F818E77DABECCC669F8802633503DF1E (void);
extern void PcgRandom_GetUInt_mE5C9062173864BB7154CA25573D2835FA70589CA (void);
extern void PcgRandom_RotateRight_mE8B5A561CCF8FAA9A6D56E27DF9BD691121EBB61 (void);
extern void PcgRandom_XshRr_mDF8F5B670486970BB8896219DD6887AFC2806EDF (void);
extern void PcgRandom_Step_m0C0C0E3BED534CFC75FC9DF507A708043B1278B0 (void);
extern void Operator__ctor_m110EBC47E0EBC2AC1D40E3737BA9A164222B9C71 (void);
extern void U3CU3Ec__cctor_m587D04CDB69A94B5029BB4AAD5DB0993E512B44A (void);
extern void U3CU3Ec__ctor_mE7C64602A710F6CEF0B7768C9A2371250224A9F6 (void);
extern void U3CU3Ec_U3CExpressionToTokensU3Eb__14_0_mED0A7EEADBF72B4F54461A0D1E6C939772258CAC (void);
extern void Bounds__ctor_mAF7B238B9FBF90C495E5D7951760085A93119C5A (void);
extern void Bounds_GetHashCode_m59C79B529D33866FE45FEFC0C69FBD3B4AC7E172 (void);
extern void Bounds_Equals_m93E0B9D24C73E57A6FABB9D312101D48183C88CC (void);
extern void Bounds_Equals_m615135524315743D29633C33B6C8B16B754266DB (void);
extern void Bounds_get_center_m5B05F81CB835EB6DD8628FDA24B638F477984DC3 (void);
extern void Bounds_set_center_m891869DD5B1BEEE2D17907BBFB7EB79AAE44884B (void);
extern void Bounds_get_size_m0699A53A55A78B3201D7270D6F338DFA91B6FAD4 (void);
extern void Bounds_set_size_m950CFB68CDD1BF409E770509A38B958E1AE68128 (void);
extern void Bounds_get_extents_mFE6DC407FCE2341BE2C750CB554055D211281D25 (void);
extern void Bounds_set_extents_m09496358547B86A93EFE7BE6371E7A6FE937C46F (void);
extern void Bounds_get_min_m465AC9BBE1DE5D8E8AD95AC19B9899068FEEBB13 (void);
extern void Bounds_get_max_m6446F2AB97C1E57CA89467B9DE52D4EB61F1CB09 (void);
extern void Bounds_op_Equality_m5D72DDC8A0C2493FAFD00E88BC3B21D600CB5B33 (void);
extern void Bounds_op_Inequality_mBE4883EFEDADB40B7243DC1F19BD01B5908CD2E0 (void);
extern void Bounds_SetMinMax_mB5F7DDF18EDB7F3F25FA6D2B36824F28978C540F (void);
extern void Bounds_Encapsulate_m1FCA57C58536ADB67B85A703470C6F5BFB837C2F (void);
extern void Bounds_ToString_m1BCCCC8C6455A77DE5C964968C33305EF7A4A0D2 (void);
extern void Bounds_ToString_m085531A8E800327829FCD48DEA671A4A0B8D21CA (void);
extern void BoundsInt_get_position_m0A58811AA258865B63CCFEDD693E278367411B4B (void);
extern void BoundsInt_set_position_m72954A6270A27FCC62B2B32290CE32D5784A837E (void);
extern void BoundsInt_get_size_mE7C4A0C3BF45CEA7A28ABF98E2C15CB69EF3A32C (void);
extern void BoundsInt_set_size_m518DA559D9E67DE136B3CCB37470E147FA088CE1 (void);
extern void BoundsInt__ctor_m93F7EDF326B3BA01465FA229F6CEED0ED48D32FF (void);
extern void BoundsInt_ToString_mACB0BAF0A766690D30CA39FF52EA783583653B3F (void);
extern void BoundsInt_ToString_m0505A1F9CB063D0F588D7212D695894D5B1460D8 (void);
extern void BoundsInt_Equals_m4C99DB2D3AD7DD9E0A75562F0AE24A14AE63587D (void);
extern void BoundsInt_Equals_m143E0673DA604FDEBBF40115D50BE078E343F1E6 (void);
extern void BoundsInt_GetHashCode_m9740EA5B8C8E9B4DD47D9D6E619D61F5B99115CC (void);
extern void Plane_get_normal_mA161D94E6F7327BC111007C880B76E1731729EFB (void);
extern void Plane__ctor_m2BFB65EBFF51123791878684ECC375B99FAD10A2 (void);
extern void Plane_Raycast_mC6D25A732413A2694A75CB0F2F9E75DEDDA117F0 (void);
extern void Plane_ToString_mF0A98DAF2E4FA36A98B68F015A4DE507D8BB3B5A (void);
extern void Plane_ToString_mE12B74C757E52A84BE921DF2E758A36E97A11DDA (void);
extern void Ray__ctor_mE298992FD10A3894C38373198385F345C58BD64C (void);
extern void Ray_get_origin_m97604A8F180316A410DCD77B7D74D04522FA1BA6 (void);
extern void Ray_get_direction_m21C2D22D3BD4A683BD4DC191AB22DD05F5EC2086 (void);
extern void Ray_GetPoint_mAF4E1D38026156E6434EF2BED2420ED5236392AF (void);
extern void Ray_ToString_m06274331D92120539B4C6E0D3747EE620DB468E5 (void);
extern void Ray_ToString_mA76F7B86876505F674F3E20C18C8258103622C10 (void);
extern void Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23 (void);
extern void Rect__ctor_m503705FE0E4E413041E3CE7F09270489F401C675 (void);
extern void Rect__ctor_m5665723DD0443E990EA203A54451B2BB324D8224 (void);
extern void Rect_get_zero_m5341D8B63DEF1F4C308A685EEC8CFEA12A396C8D (void);
extern void Rect_MinMaxRect_m540D2DD8C255D276AD4AE06D9CCA2A667EFA39E5 (void);
extern void Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB (void);
extern void Rect_set_x_mAB91AB71898A20762BC66FD0723C4C739C4C3406 (void);
extern void Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49 (void);
extern void Rect_set_y_mDE91F4B98A6E8623EFB1250FF6526D5DB5855629 (void);
extern void Rect_get_position_m9B7E583E67443B6F4280A676E644BB0B9E7C4E38 (void);
extern void Rect_set_position_m9CD8AA25A83A7A893429C0ED56C36641202C3F05 (void);
extern void Rect_get_center_mAA9A2E1F058B2C9F58E13CC4822F789F42975E5C (void);
extern void Rect_get_min_mD0D1BABF9C955D2D9CCA86E257B0783ACDEE69AC (void);
extern void Rect_set_min_m6557D7D73C6F115CA7C92E38C88EA9E95FC89253 (void);
extern void Rect_get_max_m60149158D9A01113214BB417AA48CEF774899167 (void);
extern void Rect_set_max_mAD2D6D5DC1F5A6E69A0A0BD7E34C209F91C381F0 (void);
extern void Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9 (void);
extern void Rect_set_width_m93B6217CF3EFF89F9B0C81F34D7345DE90B93E5A (void);
extern void Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8 (void);
extern void Rect_set_height_mD00038E6E06637137A5626CA8CD421924005BF03 (void);
extern void Rect_get_size_mFB990FFC0FE0152179C8C74A59E4AC258CB44267 (void);
extern void Rect_set_size_m346E4F7077E5A1C0F4E21966232CD726CB9E6BAA (void);
extern void Rect_get_xMin_mE89C40702926D016A633399E20DB9501E251630D (void);
extern void Rect_set_xMin_mA873FCFAF9EABA46A026B73CA045192DF1946F19 (void);
extern void Rect_get_yMin_mB19848FB25DE61EDF958F7A22CFDD86DE103062F (void);
extern void Rect_set_yMin_m9F780E509B9215A9E5826178CF664BD0E486D4EE (void);
extern void Rect_get_xMax_m2339C7D2FCDA98A9B007F815F6E2059BA6BE425F (void);
extern void Rect_set_xMax_m97C28D468455A6D19325D0D862E80A093240D49D (void);
extern void Rect_get_yMax_mBC37BEE1CD632AADD8B9EAF9FE3BA143F79CAF8E (void);
extern void Rect_set_yMax_mCF452040E0068A4B3CB15994C0B4B6AD4D78E04B (void);
extern void Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B (void);
extern void Rect_Contains_mB1160CD465F3E9616AA4EED72AFFD611BD8D2B6B (void);
extern void Rect_OrderMinMax_mCF0D63521F398D682E285F8795781C1D2A3418D9 (void);
extern void Rect_Overlaps_m5A540A24DAD3327006A3A2E209CC17992173B572 (void);
extern void Rect_Overlaps_m3F0BA2C8BB81491978B21EB21C8A6D3BBED02E41 (void);
extern void Rect_op_Inequality_mB5D7316EB50B1DDA9324F4BE6741DFF6A673137D (void);
extern void Rect_op_Equality_mF2A038255CAF5F1E86079B9EE0FC96DE54307C1F (void);
extern void Rect_GetHashCode_m8E55539476EA6B7A6E0CEC5F980227CD15B778F1 (void);
extern void Rect_Equals_mD7EB2046512E4A46524A7ED929F1C38A32C408F8 (void);
extern void Rect_Equals_mE725DE506D3F1DB92F58B876BDA42AACD4D991B5 (void);
extern void Rect_ToString_m7BF74645745862DA4751965D0899F94376F77F10 (void);
extern void Rect_ToString_mA9EB8EC6A2E940240E0D0ECFA103B9B7EFB3D532 (void);
extern void RectInt_get_x_mA1E7EF6DEAD2E900D7D56B7A3957C05081EBA9CA (void);
extern void RectInt_set_x_m2D2F3A87E9899A29444DBDD0BB11CB19F13AA075 (void);
extern void RectInt_get_y_m440422264E6FCAA91E01F81486A78037AC29D878 (void);
extern void RectInt_set_y_m45D5C1D817698266BED66D41A705956A1571858D (void);
extern void RectInt_get_width_m6B7B2FB764EAE83B7F63E7F77FA33973606761A7 (void);
extern void RectInt_set_width_mCD96AA9D096114147F8411A340CE4AD3476DCD4C (void);
extern void RectInt_get_height_mE25FB938714942D7A3BA0B3C21BC5CB913D5709C (void);
extern void RectInt_set_height_m823A353A80B8B5180AEDF968A6E85B9D9B75C1C6 (void);
extern void RectInt_get_xMin_mA5FB5AF1133380E080CF750D21327DE27EADEE1B (void);
extern void RectInt_get_yMin_m6914C2254158DF797E20E381626DC08A2700147B (void);
extern void RectInt_get_xMax_mBA05CE52847E3D3CB8295055706B3E0D4350E9F0 (void);
extern void RectInt_get_yMax_mAE5D758A1241F7722F8FB9B46861583F76C8FE44 (void);
extern void RectInt__ctor_m6E8B3A6C7EE11257A6B438E36274116FE39B5B42 (void);
extern void RectInt_Overlaps_m9E82E1C8BFDA3297221E5FDC8B8773AD3F50C4EE (void);
extern void RectInt_ToString_m7EC8BB4830459B8CF5BF3032E9A526A6EE18D386 (void);
extern void RectInt_ToString_m7EAE8CA8D77B7D6DDD46B61D670C71046006B92F (void);
extern void RectInt_Equals_mE9EA164664CA30C1C099EFB658D691F55A793B96 (void);
extern void RectOffset__ctor_m7DB8E8EDDB814824299F9EF661DAFA32854F0CC9 (void);
extern void RectOffset__ctor_m0711AF5DF27B8F3EC6CAF54755CDE46B76C00DBE (void);
extern void RectOffset_Finalize_mF43E63CF652D658B255D033A099CAED2C4262FBC (void);
extern void RectOffset_ToString_m8FAEA1A73C37F5827D0444EB4A8C5BA0781506F8 (void);
extern void RectOffset_ToString_mEE066023D51F743F0370E3547E7145FAE86B00BC (void);
extern void RectOffset_Destroy_mFFB78BB790E3BAFE464C29DB5F396FB2FA8E6E70 (void);
extern void RectOffset_InternalCreate_m6570FA60917650C43BBA7229F56215B92EC69873 (void);
extern void RectOffset_InternalDestroy_mFFF37ED69D9FDAE656F347FD3E6E36F7BEB0ACC1 (void);
extern void RectOffset_get_left_mA406D7AFF76E48507EF143CDB1D157C4D5430D90 (void);
extern void RectOffset_get_right_m07C826B0BC79B0CBC01F5FF489D456C553F047BF (void);
extern void RectOffset_get_top_m82E49FB93A5BD417131136F5A7DBA0F251F10263 (void);
extern void RectOffset_get_bottom_mDF9C1EC125F94245D5532C34FCFB65BE0F2A9D0B (void);
extern void RectOffset_get_horizontal_m5C1795C027E4987A8532DC27D88FB3B9FFEC1352 (void);
extern void RectOffset_get_vertical_m43E46D9F313BB617044184A65350E6498A0709F0 (void);
extern void LightingSettings_LightingSettingsDontStripMe_mA06509B760BA1A7DDB4CC3CE584DC2C3E1FE0237 (void);
extern void BeforeRenderHelper_Invoke_mDBB6C0ECACC207FD3A4D354177C93AC24B2184B5 (void);
extern void BeforeRenderHelper__cctor_m1BA6EA4EC2C3720A5966A677530FC51FE683EF44 (void);
extern void CustomRenderTextureManager_InvokeOnTextureLoaded_Internal_m18ECD151BFA0492A839269B69A1CDCA444D0FF1F (void);
extern void CustomRenderTextureManager_InvokeOnTextureUnloaded_Internal_m86253D77C4A01969A81716A470F3D67B079D58DC (void);
extern void Display__ctor_mD4B0D1F494D3472058E47A940600FAC93D68D1DF (void);
extern void Display__ctor_m0F78D1B697F3D82FFD274A6AA716E96FA6A4B156 (void);
extern void Display_get_renderingWidth_mD23656E7A8270AF79B0EF4124959B3E18BEDF0C7 (void);
extern void Display_get_renderingHeight_m0627691B7B2D7037A856597E43BFDF1F0CC2B0B8 (void);
extern void Display_get_systemWidth_m515BF32963F049A9710F11CE8F6445BAA63D7155 (void);
extern void Display_get_systemHeight_mC20ADD124FBEF94796F736684A3AF4D0AA569FC7 (void);
extern void Display_RelativeMouseAt_m39BFC645EB81B36E705F1E9D3116EFC3995B9FE7 (void);
extern void Display_get_main_m6EB21BF1B5F545173BEECDB8F1F547FD74B4895C (void);
extern void Display_RecreateDisplayList_m8FB39A95A6DAE41CD0194BECB71B80B79A2E6B88 (void);
extern void Display_FireDisplaysUpdated_mF99580245BC4B28765E06B57611C52A0497E9994 (void);
extern void Display_GetSystemExtImpl_m56DF5AA95B2433409A37AB7BECFBC1538A5C112D (void);
extern void Display_GetRenderingExtImpl_m4AF3F04825972A502967DFDE02E2B723CA689E46 (void);
extern void Display_RelativeMouseAtImpl_m6950C34B75D0FD275E9ACC264EC41823081EEFC8 (void);
extern void Display__cctor_mC3998EEB2068A145697B8B6E65DF16BA7FED52CB (void);
extern void DisplaysUpdatedDelegate__ctor_mCF8A6ABE7CCC32908022E0AB13ED3BF1EBD2719D (void);
extern void DisplaysUpdatedDelegate_Invoke_m4E0A3493E2C3A9CE0D5B5B3FD40A8389BFBA1853 (void);
extern void RefreshRate_get_value_m7F8BB0D20DAB1EF882F1FC97E0C7618FCD319561 (void);
extern void RefreshRate_Equals_m16184432DA438F6BAF730078987262C7DE97913C (void);
extern void RefreshRate_CompareTo_mD74AD821FF0DA633F9719E2B824C84E77E443D66 (void);
extern void RefreshRate_ToString_m59B00D8F20B6DAB001CD394F23653AC693DF8047 (void);
extern void Screen_get_width_mF608FF3252213E7EFA1F0D2F744C28110E9E5AC9 (void);
extern void Screen_get_height_m01A3102DE71EE1FBEA51D09D6B0261CF864FE8F9 (void);
extern void Screen_get_dpi_mEEDAA2189F84A47BD69D62A611E031D5C59CFE8E (void);
extern void Screen_get_fullScreen_m9B3B4F9FB4B6FD6E1AEF626736C4462A2FD7407C (void);
extern void Graphics_Internal_GetMaxDrawMeshInstanceCount_m3CA9D7C4AD2A389C9F4C95B0FF04B6903B5A9048 (void);
extern void Graphics_Internal_SetNullRT_m9D3FB09C1C0176C01B5B9D8299FB29FCC68550A9 (void);
extern void Graphics_Internal_SetRTSimple_m8A08CC6791B24AD4ED3262FC14F65F71FBDE7C01 (void);
extern void Graphics_SetRenderTargetImpl_mFA652AAB6B11A0B3740395807051662E51FD9F97 (void);
extern void Graphics_SetRenderTargetImpl_m20519EB34D9C8900A5BFD438C2B97C69724EFE0B (void);
extern void Graphics_SetRenderTarget_mCD697A459B0374E91E854C7DB850D4DE9B3523C2 (void);
extern void Graphics_SetRenderTarget_m995C0F14B97C5BF46CCF2E7EF410C1CC05C46409 (void);
extern void Graphics__cctor_m37525F50A7B79CDD75A1DFA2AEFFA998BCBDD94C (void);
extern void Graphics_Internal_SetRTSimple_Injected_m049F9654EADC509446BE61023C735FE7BDEA590E (void);
extern void GL_Vertex3_mEA9925548F447556F7899E69604B787EE57F6978 (void);
extern void GL_TexCoord3_mD95B6C3D77105453E0A01B1A29DFDDDF14BB7111 (void);
extern void GL_TexCoord2_mBD9A9E6D397F8669FAE40DA950AB1FD7D674D1FB (void);
extern void GL_ImmediateColor_m8311AC8E6A292EE6247F6533785F4DC0CB1FA242 (void);
extern void GL_Color_mE2D27D8FCA9B5E07ECC15574BCBCBA16E6E0CB3E (void);
extern void GL_SetViewMatrix_m7916323155AF8AD717E539A8E0D91391755E9278 (void);
extern void GL_set_modelview_mCAE007CC5BE38316397954370950EB43991FBBD4 (void);
extern void GL_PushMatrix_mB505DD9B224528266FCADC716A16343838105A09 (void);
extern void GL_PopMatrix_mCE0D33302104D1168B6382136039E979E8C02855 (void);
extern void GL_LoadOrtho_mE86AB2DBBC5C2BA67E7B743A2352E61C372CEADC (void);
extern void GL_LoadProjectionMatrix_m4310EBD83BF1858124306A227C18FF52152718BD (void);
extern void GL_GLLoadPixelMatrixScript_m3B24502667E2873BC00326BC5EA7CB1974E1B4CC (void);
extern void GL_LoadPixelMatrix_mF1C5A4508C5F110512C116A5DDE7AB0483FE961A (void);
extern void GL_Begin_m17A70A7A3C161D8A127C11BDC5FC393392AB70C7 (void);
extern void GL_End_m6CE9D562B738075125F901B1D5254520EC30AB36 (void);
extern void GL_GLClear_m889A7C7A3406665D8631A4946DD478CE1DADE1C4 (void);
extern void GL_Clear_m49FFE8774C00488FD65A2638F7D02E67325923FA (void);
extern void GL_Viewport_mA93DBAE1018A273684A07C8B8B444EFFBF2B38B7 (void);
extern void GL_SetViewMatrix_Injected_m13A8BCB6F2D98B7172E2FF190A6F2E7FE3AB4029 (void);
extern void GL_LoadProjectionMatrix_Injected_mFB43E70DB0AA3CF3FD0B212495D125844579816C (void);
extern void GL_GLClear_Injected_m27F584AF9138B297159C619DEEEB427E722D2DED (void);
extern void GL_Viewport_Injected_m18D18FFCCD4FDD941820797CFBE7270ECFF27D4E (void);
extern void LightProbes_Internal_CallLightProbesUpdatedFunction_mD73BBA534592554972ECBC7D7EE1542A4ECEE6EA (void);
extern void LightProbes_Internal_CallTetrahedralizationCompletedFunction_m491992F2C5429BCA63C2076F29BBEE7B10BA54CB (void);
extern void LightProbes_Internal_CallNeedsRetetrahedralizationFunction_m72321DC24BB3DDFFABCEC02F72A5BE5826B829FE (void);
extern void Resolution_ToString_m058CE120CC83F314D0C8D4A706F9AA068BC9CF34 (void);
extern void QualitySettings_OnActiveQualityLevelChanged_m4E490A1A4A4BD3E8838E134EB3E6E3D6AB9955E6 (void);
extern void QualitySettings_get_activeColorSpace_m4F47784E7B0FE0A5497C8BAB9CA86BD576FB92F9 (void);
extern void MaterialPropertyBlock_SetTextureImpl_m4F16E63E86A344A8FB595BBD7132641593427F95 (void);
extern void MaterialPropertyBlock_SetVectorArrayImpl_mFED50BE8138077759CB79FF448A7AD9B4F27D981 (void);
extern void MaterialPropertyBlock_CreateImpl_mB5C499DC3ACC3B8AEB29249CC5896877810467EA (void);
extern void MaterialPropertyBlock_DestroyImpl_m169142814063295DE7C2FE69CD3580B2F952776E (void);
extern void MaterialPropertyBlock_Clear_m18CD90F6F18294A59C408FFCCC8F6F5EE204E6D4 (void);
extern void MaterialPropertyBlock_Clear_m83CE1CC476A80F162FC89DBF6C2C78659B6E1253 (void);
extern void MaterialPropertyBlock_SetVectorArray_m92BE44069E96A65D975F989B9C2703819B4D9709 (void);
extern void MaterialPropertyBlock__ctor_m14C3432585F7BB65028BCD64A0FD6607A1B490FB (void);
extern void MaterialPropertyBlock_Finalize_m39FFB2D5E0348431D63EE94E263032CA6C7040BF (void);
extern void MaterialPropertyBlock_Dispose_m4D2F96068928FBC127E3A48B45DB5F3C0022B3E3 (void);
extern void MaterialPropertyBlock_SetTexture_m39F531D3F35D6C5B661A7B4F07DD7B8ACC22627F (void);
extern void MaterialPropertyBlock_SetVectorArray_m5C0A3017A7EA9EE5F01295E346EA72D70A8BD682 (void);
extern void Renderer_GetMaterial_m890C1B7FAA74CFC4B362EE3E8E61F6778D0EA189 (void);
extern void Renderer_GetSharedMaterial_mD825E40750BD40B66D0A9FE4C283040E516FF192 (void);
extern void Renderer_SetMaterial_mD7F173BF5941C840EB5A24FEF8B7BC3BAFAF7CCA (void);
extern void Renderer_get_enabled_mFDDF363859AEC88105A925FA7EA341C077B09B54 (void);
extern void Renderer_set_enabled_m015E6D7B825528A31182F267234CC6A925F71DA8 (void);
extern void Renderer_set_shadowCastingMode_mB0CD3D153E43044388AEFF7AEFDA93E80EB36D11 (void);
extern void Renderer_set_receiveShadows_mABEB4C72E96E65117B7FFFD4180247565D0C1A09 (void);
extern void Renderer_get_sortingLayerID_m3D7AE74F1B87099810CF969CB4520C85F9AE5F92 (void);
extern void Renderer_set_sortingLayerID_m289E44FD06B6692C7B2ADD1189FE4FC013180C49 (void);
extern void Renderer_get_sortingOrder_m4CE7ADEEC8E2F28CC1D10B1D4091A10F8F1583FA (void);
extern void Renderer_set_sortingOrder_m4C67F002AD68CA0D55D20D6B78CDED3DB24467DA (void);
extern void Renderer_get_sortingGroupID_m1DEFE39027236FF31AB603993BBC13C072E52118 (void);
extern void Renderer_get_sortingGroupOrder_m40F058B3AFC92CCEF2475E0BD9A96464154E999E (void);
extern void Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656 (void);
extern void Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81 (void);
extern void Renderer_set_sharedMaterial_m5E842F9A06CFB7B77656EB319881CB4B3E8E4288 (void);
extern void Renderer__ctor_m8B4EE9696B155A1B0A2CF13EBFC363CE175B9271 (void);
extern void Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5 (void);
extern void Shader_TagToID_m5B5214F0AABE47C7465EEA717C78568AE1251FE9 (void);
extern void Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA (void);
extern void Shader__ctor_m2BD5A93728781AEF4DCA08F3BCC93C0A62B864C1 (void);
extern void Material_CreateWithShader_m3AFA4A4172FE7A3BD861AA015F4DA8BF21E7EB3B (void);
extern void Material_CreateWithMaterial_m11AFA8AF4CF07621B8F872D231F52287D510E49B (void);
extern void Material_CreateWithString_m95D6FADE4506835842E09A7E26513252F560C87A (void);
extern void Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF (void);
extern void Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C (void);
extern void Material__ctor_mF1676C2EE42E6BCE74AC3C90E207A35E515D1FD8 (void);
extern void Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983 (void);
extern void Material_set_shader_mBD3A0D9AB14DE2F7CD5F2775E9AD58E15424C171 (void);
extern void Material_get_mainTexture_mC6C6B860B44321F0342AEFA0DD7702384334F37D (void);
extern void Material_GetFirstPropertyNameIdByAttribute_mCBB72749633F7B8A879D33F0B34DB4F54F8C2439 (void);
extern void Material_HasProperty_m52E2D3BC3049B8B228149E023CD73C34B05A5222 (void);
extern void Material_HasProperty_mC09A83B44E368A217F606DD4954FA080CC03EC6C (void);
extern void Material_set_renderQueue_mFBB72A781DCCF0D4B85670B597788EC2D02D1C14 (void);
extern void Material_EnableKeyword_mE8523EF6CF694284DF976D47ADEDE9363A1174AC (void);
extern void Material_DisableKeyword_mC123927EBF2F2A19220A4456C8EA19F2BA416E8C (void);
extern void Material_get_passCount_m7BA071AFFA34DC3E49B56A16CB8B098566BDE765 (void);
extern void Material_GetTagImpl_m0A15070E738DE3834BEDD9D1634C498B5B744C2B (void);
extern void Material_GetTag_m0B37070270E231B88808DB1B3F9EF4C1851627D4 (void);
extern void Material_SetPass_mBB03542DFF4FAEADFCED332009F9D61B6DED75FE (void);
extern void Material_CopyPropertiesFromMaterial_m4148227E6A0B8E66315D8115F656B7F8BEAE915B (void);
extern void Material_GetShaderKeywords_mEEE6AA1A327A7D63B8CD31306B70AAED6F7B7456 (void);
extern void Material_SetShaderKeywords_mB0751FC936E7546A20DD6531B52E3B0EAF5BB3A8 (void);
extern void Material_get_shaderKeywords_m11982F09EED6BB0A892342E1A72AEA470C44B105 (void);
extern void Material_set_shaderKeywords_mD650CF82B2DBB75F001E373E2E1ACA30876F3AB8 (void);
extern void Material_ComputeCRC_m5F743B7178F5E474A8FE15E5C0B1A6527E6E3A77 (void);
extern void Material_SetFloatImpl_m78678CE6EBB67BA3E9D58AD37CE3BDA3789F43AB (void);
extern void Material_SetColorImpl_mC2AF99ED12166A5219C0E8B79D0D17C0FCFC0E3D (void);
extern void Material_SetMatrixImpl_m634CD43159467FE20FF3860B5F9848101A9B0CE3 (void);
extern void Material_SetTextureImpl_mC5772481EBD0F0A488C564B424446514DF34CBD9 (void);
extern void Material_GetFloatImpl_m39798817949E201E1CF85DBBD416C746F3D64794 (void);
extern void Material_GetColorImpl_m44455DBA8C6391B5638D11934B8E4CC0713B4EFF (void);
extern void Material_GetTextureImpl_m0363E7A333009D2FBEEBA51FC5D6E219563FF288 (void);
extern void Material_SetFloat_m879CF81D740BAE6F23C9822400679F4D16365836 (void);
extern void Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8 (void);
extern void Material_SetColor_m573C88F2FB1B5A978C53A197B414F9E9C6AC5B9A (void);
extern void Material_SetVector_m44CD02D4555E2AF391C30700F0AEC36BA04CFEA7 (void);
extern void Material_SetMatrix_m668579C6402F88BFEF769D39E484BAD4CE6B0067 (void);
extern void Material_SetTexture_mA9F8461850AAB88F992E9C6FA6F24C2E050B83FD (void);
extern void Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A (void);
extern void Material_GetFloat_m52462F4AEDE20758BFB592B11DE83A79D2774932 (void);
extern void Material_GetColor_mCCC62F29234C5D2D9B19EE2D7DA46A4573AFA765 (void);
extern void Material_GetVector_mFE0366FDCB87331910BCE5E234030C20A25484A2 (void);
extern void Material_GetTexture_mBA07A377A7DDD29CD53E6F687DE6D49A42C0C719 (void);
extern void Material_GetTexture_mE5D02B13E7AF35ABAE4FFC49E69FAF8F36F91191 (void);
extern void Material_SetColorImpl_Injected_m7AF196BB00E62E5B47067FD643740884C692694A (void);
extern void Material_SetMatrixImpl_Injected_m982E0183155834D0018A8A0174DBE0536F56D49E (void);
extern void Material_GetColorImpl_Injected_mC524E0342AAF07A17E22CDBA0FAA0BAC0689CED4 (void);
extern void Light_get_type_m0D12CD1E54E010DC401F7371731D593DEF62D1C7 (void);
extern void Light_get_spotAngle_m28B2CD7ADE25422693E7B1FA23E8615E9D7098FC (void);
extern void Light_get_color_mE7EB8F11BF394877B50A2F335627441889ADE536 (void);
extern void Light_get_colorTemperature_mA5B7C9A5B315B27625764B8CE7EF5ADC06060B08 (void);
extern void Light_get_useColorTemperature_mD76967684F904F6068B58EE78BD65001D8AFF3EF (void);
extern void Light_get_intensity_m8FA28D515853068A93FA68B2148809BBEE4E710F (void);
extern void Light_get_bounceIntensity_m535008F539A0EF22BBB831113EC34F20D6331FAE (void);
extern void Light_get_range_m4156F07BA6CD289DA47080B590D632721D975A22 (void);
extern void Light_get_bakingOutput_mF383DB97CFD32D65DA468329E18DD2DD61521CED (void);
extern void Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5 (void);
extern void Light_get_cookieSize_m1BB417985207915659198F63CF825A23A8ED30B0 (void);
extern void Light_get_cookie_m44A0C4B92F6CD6F2F8536A91C51B77FEEF59715E (void);
extern void Light_get_color_Injected_m7B1E67B800788DF5DAF6C3114CBACA3B029A900A (void);
extern void Light_get_bakingOutput_Injected_m587C60162C878716DF9310258756C2F587E35185 (void);
extern void MeshFilter_DontStripMeshFilter_m91485EC5EAB0DFED85197CF48DB2DD5C872695AC (void);
extern void MeshFilter_set_sharedMesh_m946F7E3F583761982642BDA4753784AF1DF6E16F (void);
extern void MeshFilter__ctor_m77A77552ED64DE8DBE9DD89910941E83CC61862A (void);
extern void MeshRenderer_DontStripMeshRenderer_m6AA14C7B8A41C6BBCDE33338AAB96239048192A1 (void);
extern void Mesh_Internal_Create_m484E627641C5E449D1B755F473DE5F6CD116FC45 (void);
extern void Mesh__ctor_m5A9AECEDDAFFD84811ED8928012BDE97A9CEBD00 (void);
extern void Mesh_GetIndicesImpl_m2A93DF68D761B4F7559A40617C41B543535B8739 (void);
extern void Mesh_SetIndicesImpl_m8C51773B1B4F164E4A9EAE3A8A90D73319595B89 (void);
extern void Mesh_PrintErrorCantAccessChannel_mD80AC1870BC51714BE0C97B15D3E3D4EC42AC85E (void);
extern void Mesh_HasVertexAttribute_m6FA9574E4DEE19158535F93281791AD6FB550595 (void);
extern void Mesh_SetArrayForChannelImpl_mB6E87CFC14E1C7F12A921A2EB90004DC0108BE3C (void);
extern void Mesh_GetAllocArrayFromChannelImpl_m30C7972CA5A67CD01EBBDDE2FD8F48CE2F8F86F6 (void);
extern void Mesh_GetArrayFromChannelImpl_mBCA166B62E25425D987638F2B4876613D113E66E (void);
extern void Mesh_get_canAccess_m97F255BBB8C009D643920E2D095B6DB8868E3572 (void);
extern void Mesh_get_vertexCount_mB7BE0340AAF272933068D830C8E711FC8978E12C (void);
extern void Mesh_get_subMeshCount_mC0141293D0F339D8D721CCA2612B32E6FD7E3F8B (void);
extern void Mesh_get_bounds_m9E3A46226824676703990270A722A89A4860A100 (void);
extern void Mesh_set_bounds_m2E526E9B61ACA77D644C22A9D8EB49583012B54E (void);
extern void Mesh_ClearImpl_m671C073131284D65405DE7480536EE16A2815A62 (void);
extern void Mesh_RecalculateBoundsImpl_m33B65339F18DE70FDB0914F466281CDC39CAE5C2 (void);
extern void Mesh_MarkDynamicImpl_m989606245279A69A710361DC0443BE39960DF857 (void);
extern void Mesh_GetUVChannel_m52936A342F9C96EB7F0214A2F310018E1A25A689 (void);
extern void Mesh_DefaultDimensionForChannel_mCE2BFE4516CB37EA677E8671997D986FF654C130 (void);
extern void Mesh_SetSizedArrayForChannel_mBF8A0B7A6E58552C4142CD22FF31E7A0E125EC93 (void);
extern void Mesh_get_vertices_mA3577F1B08EDDD54E26AEB3F8FFE4EC247D2ABB9 (void);
extern void Mesh_set_vertices_m5BB814D89E9ACA00DBF19F7D8E22CB73AC73FE5C (void);
extern void Mesh_get_normals_m2B6B159B799E6E235EA651FCAB2E18EE5B18ED62 (void);
extern void Mesh_set_normals_m85D73193C49211BE9FA135FF72D5749B16A4760B (void);
extern void Mesh_get_tangents_mEA6C7BF6A2B9A8BD0E9A62963E048F973878299E (void);
extern void Mesh_set_tangents_mF547B7E4F9C70FB7CD6168139180A70AD306169B (void);
extern void Mesh_set_uv_m6ED9C50E0DA8166DD48AC40FD6C828B9AD2E9617 (void);
extern void Mesh_set_uv2_m37B442C04EBB029C0AD9545C54F95D9BDAD8E9B5 (void);
extern void Mesh_get_colors32_mA230CB5687CDCCEA5527BD5A0640E9535CB640A2 (void);
extern void Mesh_set_colors32_m0E4462B7A1D613E6FB15DD7584BCE5491C17820F (void);
extern void Mesh_SetVertices_mCE01139EEEC3D10B4A6CEA1749FD84DABC53599E (void);
extern void Mesh_SetVertices_m193FDA5D9CB31FDFEEEC4CF17C0524A255E74E2F (void);
extern void Mesh_SetVertices_m13AB485BDB6B8F740338B20075A3E1510A6F96FA (void);
extern void Mesh_SetNormals_mB4072A4D2600768FD62BC9CAAFF8C43955A4A365 (void);
extern void Mesh_SetNormals_m7EB9E43C0287E99F2140901EB9CE5A75728B1C29 (void);
extern void Mesh_SetNormals_m07608D2B5CA012B8B3502868EF6D4B4DF859DE51 (void);
extern void Mesh_SetTangents_m0FAEBB1D9ACF3607640669AD70AD6BFACA9D3CBB (void);
extern void Mesh_SetTangents_mAE30C8D38845CEC4002FE0938607CDE0D2358FBE (void);
extern void Mesh_SetTangents_m87066124EB0091440267546DB9A27EC89BF6E21B (void);
extern void Mesh_SetColors_m4D4920186213CCC1DA28CD3C7BDBD13F0D00541F (void);
extern void Mesh_SetColors_mEF64AD954EACB5F5EFFD8DA6AEA7D80CD1233F37 (void);
extern void Mesh_SetColors_m07FAA75430500D2B2384A4A8185311411C504DEA (void);
extern void Mesh_SetUVs_mA0EA40129409D956FF22FCF7E01E27382879E255 (void);
extern void Mesh_SetUVs_m69E3F4333E212B3CAA0EDFFF14A2835897079D35 (void);
extern void Mesh_SetUVs_m25164DB799B915ECE8AE94B4A1F844CE936844B9 (void);
extern void Mesh_GetUVs_m3FCD854132DA16719481B8D97DF335A0C7244344 (void);
extern void Mesh_PrintErrorCantAccessIndices_mC2A8F7BBED09D4D6ABC265AFCC2979795C1ABF0C (void);
extern void Mesh_CheckCanAccessSubmesh_m216B541BBC526DB8F0ADBC9210E440D6DF875283 (void);
extern void Mesh_CheckCanAccessSubmeshTriangles_m96B4AFC9B20344F39C936698D23FBA1DACCBA020 (void);
extern void Mesh_CheckCanAccessSubmeshIndices_mB6659E6FDE4EFDECB233C194E4AC066F5FF60533 (void);
extern void Mesh_set_triangles_m124405320579A8D92711BB5A124644963A26F60B (void);
extern void Mesh_GetIndices_m112B85EF32EE3C446947CE6CBC1AF3D50FC18179 (void);
extern void Mesh_GetIndices_mFF08708714AB105E1097F1C2065D2A1ACAC88183 (void);
extern void Mesh_CheckIndicesArrayRange_m0B8030BE6248E1E411D92E3255777E6E530527F4 (void);
extern void Mesh_SetTrianglesImpl_m0B0B6FAC63A88C640C3AA9DD5F1748BE6B2A2EC2 (void);
extern void Mesh_SetTriangles_mD495DA0B00DB0E60A2B7B500D644F4470C1D61DB (void);
extern void Mesh_SetTriangles_mD3AB650CCB405159EFDB9EC36AB21FF20213BFFB (void);
extern void Mesh_SetTriangles_m0E33B0C08C74A076A4B79F031DA60629B6CD86BA (void);
extern void Mesh_Clear_m0F95397EA143D31AD0B4D332E8C6FA25A7957BC0 (void);
extern void Mesh_RecalculateBounds_mA9B293F57C6CD298AE2D2DB19061FC23B05AB90B (void);
extern void Mesh_RecalculateBounds_mCCC67392C58860F92A9674D0816BCA2D1730F077 (void);
extern void Mesh_MarkDynamic_m718089940F240AFE625D6DC9DA4E6F20229CC322 (void);
extern void Mesh_get_bounds_Injected_mE40C051E4181E7F70DB928CEA13A3A31DC9C29F7 (void);
extern void Mesh_set_bounds_Injected_m13D417C394E27CE4A14DF253F8DD435677FC2835 (void);
extern void Texture__ctor_mC0C7974BEBD867CEB281409FEA15A78CD91B19CC (void);
extern void Texture_GetDataWidth_m9D5166D12895E4E905ED072D611DF6A834C232BC (void);
extern void Texture_GetDataHeight_m37C3DE842FB101FD509904A0D76593F970B34995 (void);
extern void Texture_get_width_m9569A27BA478BC380EBF83A53429F0E66E429108 (void);
extern void Texture_set_width_m309DEA6AA9203B160624755C5D9EA75AB08AD6AE (void);
extern void Texture_get_height_mE95D093681C11FD77191F51843F6615605A87258 (void);
extern void Texture_set_height_m2A62EC27DC1F0AE6A952B2B65FF7BF68710CDF36 (void);
extern void Texture_get_isReadable_m6268EDAFF304287D8754EC0F115377722A316139 (void);
extern void Texture_get_wrapMode_m1DE1C2813B72EDCCCEB396CFC91989358E8C3AD0 (void);
extern void Texture_get_filterMode_mFEF0AEA29E8468450EF85533965DCEBE66D02A45 (void);
extern void Texture_set_filterMode_mE423E58C0C16D059EA62BA87AD70F44AEA50CCC9 (void);
extern void Texture_get_texelSize_m05CA60DE53EF7CD5D2CBFA68B69B764E4D463359 (void);
extern void Texture_Internal_GetActiveTextureColorSpace_m2F9F2D07316E0B52679BA943369AFBF7C292949B (void);
extern void Texture_get_activeTextureColorSpace_m286856BA92961FD58FE181C8DDC417EC0572643C (void);
extern void Texture_GetTextureColorSpace_m94DFBCD1BFD13B49E0FDCFB545D41E7E1C99084C (void);
extern void Texture_GetTextureColorSpace_m7536B009835ED0098E1185F3BB2E5EA33558C1C0 (void);
extern void Texture_ValidateFormat_m5E00A267F1E4805EFCE6F6ACE707518221589596 (void);
extern void Texture_ValidateFormat_m1BC113E81713CBAF60BCA4D022ACC0C6B239E740 (void);
extern void Texture_CreateNonReadableException_m29786CD930E89C281564A9B341FD4088FBC8C94F (void);
extern void Texture_CreateNativeArrayLengthOverflowException_m508BE4928B3768ED618EF1FA10E022F6FF12F870 (void);
extern void Texture__cctor_mDE84772DB367950D96076B69BCF675ECC5819CE8 (void);
extern void Texture_get_texelSize_Injected_m9AD407D56C4BDA37E028DAEDC0D787332BCFDD18 (void);
extern void Texture2D_get_format_mE39DD922F83CA1097383309278BB6F20636A7D9D (void);
extern void Texture2D_get_whiteTexture_m3A243ED388F9EF0EC515015A6E0E50FD261D2FA1 (void);
extern void Texture2D_Internal_CreateImpl_mFF0B15BB9C5620AEDC0B4172790F6C2328511E06 (void);
extern void Texture2D_Internal_Create_m1CA68A3DB0A6340F32D58CFA3801D37547F0B52A (void);
extern void Texture2D_get_isReadable_m61A2BCE2B901DF11A910F5F8E93A72D4FD091B84 (void);
extern void Texture2D_ApplyImpl_mF4AA09D491BDA81E4A3B3BA21157436CFEC39DAA (void);
extern void Texture2D_ReinitializeImpl_m4C524C33B85EDD5FD09CAF2FA2F922FFA8BE366C (void);
extern void Texture2D_SetPixelImpl_m9B08AB57ACD44485ECC3728BD3DE39F5E80A64AA (void);
extern void Texture2D_GetPixelBilinearImpl_m1BD6DEFD90A950A3A1A235ED641F4B20F5433C8B (void);
extern void Texture2D_ReinitializeWithTextureFormatImpl_mB571C523D62065D09C7EBCFDC258A0195A7905AE (void);
extern void Texture2D_GetWritableImageData_m8E26026A332040F8713E5A2A13C5545797159A5E (void);
extern void Texture2D_GetRawImageDataSize_mBD92C0F2A09A023841785BBB642C2B7696EE7CBE (void);
extern void Texture2D_SetAllPixels32_m3B35E8F24EAE2368148524647EA47BC731A7A07A (void);
extern void Texture2D_ValidateFormat_mD70A9FEEADE89325F05E3650404D8815EE9871F4 (void);
extern void Texture2D__ctor_mD20B6FF794FE6E7AD5991DDA34777A415F47CA0E (void);
extern void Texture2D__ctor_mB1445796B2A76B3D867B2603205F513AF494B9F3 (void);
extern void Texture2D__ctor_mECF60A9EC0638EC353C02C8E99B6B465D23BE917 (void);
extern void Texture2D_SetPixel_m2CCFC5F729135D59DC4A697C2605A3FC5C8574DB (void);
extern void Texture2D_GetPixelBilinear_m6AE4AF4FD181C478DF0F2C5C329F22A263ABFF5C (void);
extern void Texture2D_Apply_m36EE27E6F1BF7FB8C70A1D749DC4EE249810AA3A (void);
extern void Texture2D_Apply_mA014182C9EE0BBF6EEE3B286854F29E50EB972DC (void);
extern void Texture2D_Reinitialize_m9AB4169DA359C18BB4102F8E00C4321B53714E6B (void);
extern void Texture2D_Reinitialize_mE7FBFD2EEF3BE3135269959DBBE253A2B79A82DF (void);
extern void Texture2D_Resize_m6A784927A609BAE045AFCF77886AEEE72D19FB90 (void);
extern void Texture2D_Resize_m80C2555F96D9952F9C3B57C53B231A64F3DBA605 (void);
extern void Texture2D_SetPixels32_m436DA034F0483F6527B7C4B74744A02ABE2EA276 (void);
extern void Texture2D_SetPixels32_m169F9873A21FB8DECA24DC1B8F06FB23D13BEE2A (void);
extern void Texture2D_SetPixelImpl_Injected_m6B877CD888C30B122813AB3FDB853E336DBCBDE0 (void);
extern void Texture2D_GetPixelBilinearImpl_Injected_m609C33C58A1944B31C61309F71B459DA3AD99F07 (void);
extern void Cubemap_Internal_CreateImpl_m475DC72A8FADCE671347FF2167512030FD9FAD73 (void);
extern void Cubemap_Internal_Create_m2E5D8A83C1A13C5309CC1504367206A4A10E68CB (void);
extern void Cubemap_get_isReadable_m66D87D825AE04E10C23B74E93E0111E87AF7478C (void);
extern void Cubemap_ValidateFormat_m60822B130F11C0593CF08E9C6145277C30EF0394 (void);
extern void Cubemap_ValidateFormat_m73C8D32EC70CA5CFB8AF951C1E4D8C92A757ACDD (void);
extern void Cubemap__ctor_m3359CE12114733CB661B308AB0F73F65B14F4758 (void);
extern void Cubemap__ctor_m3649B5EC5B2A7238E7F35A68BCAAFDDF19B96A18 (void);
extern void Cubemap__ctor_m3821285A2DC0E20113E2BEAE3F16C9AF925FE14B (void);
extern void Cubemap__ctor_m395695939CC8AE11ABC2EBEE0731AA09B9780DB3 (void);
extern void Cubemap__ctor_mCA2E50856972E2CABE59E804ACF8B5C393E81AEF (void);
extern void Cubemap__ctor_m7FAD821CFD73DA99987C1FEBA363BB2FBF84C034 (void);
extern void Cubemap__ctor_m24E373BED44132D82BB86CB3EB668E652D2E3DD1 (void);
extern void Cubemap__ctor_m4C0C7AD2F162CF6D98856430F3C2A41CE57123E5 (void);
extern void Cubemap__ctor_m991258D254D93EC429FBCD9A82F394C7757963A5 (void);
extern void Cubemap_ValidateIsNotCrunched_m8DE2FA7581B5DD6221A458125693A060769EAFE9 (void);
extern void Texture3D_get_isReadable_mC8D0F22C0C3C9524FE31A0E3A70A27233AAF1031 (void);
extern void Texture3D_Internal_CreateImpl_m1821CEA836681AC07E9AFEB4D9B70660E9959B6C (void);
extern void Texture3D_Internal_Create_m931AE6E3EBC7E03D2747E9F3B227B778D6A91644 (void);
extern void Texture3D__ctor_mBF5766666A0CAAE44719627620B2BA7F99A14054 (void);
extern void Texture3D__ctor_m09A62E979935144611CA0FA433C5655764D848AC (void);
extern void Texture3D__ctor_mA853CA41A85C285281FB928BC926B0798987B17F (void);
extern void Texture3D__ctor_m2814019477DFC8BAD31E7C3CEEEBB693DC26A67E (void);
extern void Texture3D__ctor_mFAEE4FD4E1E5A8093603E8C08F3909DDEDD41EFB (void);
extern void Texture3D__ctor_mD7476DE7FCF3F8811FD94C81CD51AD5A8AF2CD34 (void);
extern void Texture3D__ctor_m8899763E7E513380681AEA91563525E89A3EF37A (void);
extern void Texture3D__ctor_m0026373ADF87B100336F82579802E7819A5DB0F2 (void);
extern void Texture3D__ctor_m578EC77B0E1A9EF9E92003BA516E227DB85C699C (void);
extern void Texture3D__ctor_mD27F46A8B13B79866370D54112CB569B104886A2 (void);
extern void Texture3D_ValidateIsNotCrunched_m7B73D943590E60158407EC4F8C940061D3850A56 (void);
extern void Texture2DArray_get_isReadable_m1C8E2BAFBA65C894DAC0F30C9FC685173B879C32 (void);
extern void Texture2DArray_Internal_CreateImpl_m4EE7049C7DBB008FE670114F5B54B81A4443E41F (void);
extern void Texture2DArray_Internal_Create_m11CD6B09AB104B2042FAE24CC112B67A0A3CFDD5 (void);
extern void Texture2DArray_ValidateFormat_mBE783ADB196C4C046E69C370387E0CAFB5B26A25 (void);
extern void Texture2DArray_ValidateFormat_m2155230AF829C3BD0B76D886B9D83A74F9994921 (void);
extern void Texture2DArray__ctor_mE60F3475E1987C78E2055BFDB60394D9CBDEA395 (void);
extern void Texture2DArray__ctor_m052C0D528905EC9347CCE98B4043E83BAB094218 (void);
extern void Texture2DArray__ctor_m465591352202E3D5CAF529F8CB094FB2E52C6EA0 (void);
extern void Texture2DArray__ctor_m6EA4D33D0E4804BD374D86BDE37A5F7F860200C4 (void);
extern void Texture2DArray__ctor_m35E6845C6F246D79286ACF218449854B8430B5AC (void);
extern void Texture2DArray__ctor_m7C3429ECA397399F19C750F842573B920C6CAF78 (void);
extern void Texture2DArray__ctor_mC1501E822F10F3BDECDFA39EF8EE1CEB5EA84998 (void);
extern void Texture2DArray__ctor_m815CEB6ACC6C586A3D696AFA09D78C5B1957F9C0 (void);
extern void Texture2DArray__ctor_m02B575835C1D88384829865AAC5A30228AB9C12C (void);
extern void Texture2DArray_ValidateIsNotCrunched_m66FF8B3CB9A2A6695B90FD47E64F17672E7E825D (void);
extern void CubemapArray_get_isReadable_m316414972F31FD8FA45F78EB2A41585E886CA868 (void);
extern void CubemapArray_Internal_CreateImpl_m6E7C2AA04A147BB82B16156FC0A464EE4DC6B406 (void);
extern void CubemapArray_Internal_Create_m522E033D18C28E019CA8B49B6A5F898FE5D2965C (void);
extern void CubemapArray__ctor_m540A30E402A99397CC4A65A38954283C69FB9B75 (void);
extern void CubemapArray__ctor_mC6CA963CA8814B26F0711EA68E22D9D3669B2F82 (void);
extern void CubemapArray__ctor_mD255863D4BD2CC18AF0065CAB121609A82019676 (void);
extern void CubemapArray__ctor_m0C70134AD214861C2B196A31E5F5156B56CEBF7D (void);
extern void CubemapArray__ctor_m5BA9CE57CA5F1023131EFB9B946095B797D47273 (void);
extern void CubemapArray__ctor_m1DB13F0A5997B95E8E30369997CEF9268496881F (void);
extern void CubemapArray__ctor_m80BD460BEB50BAF7787C64B85A147ABF05AEB09A (void);
extern void CubemapArray__ctor_m8F5B690A043EB48E7996148A50F243BF5FE3D65E (void);
extern void CubemapArray__ctor_mF817A910C4140CA704BEA27D6111D1BAFD07E7F1 (void);
extern void CubemapArray_ValidateIsNotCrunched_m10B18C4C73CF14C45CC0C9774DE108E7D106DB37 (void);
extern void RenderTexture_get_width_m1B8A7D17591897EFAA8C841795A9BD15EA5C7B31 (void);
extern void RenderTexture_set_width_mDCDF28DB4E7CC398E5EA31B7A8B39B0D34D7FAB1 (void);
extern void RenderTexture_get_height_m9695AE75401A1D89227C6827843F4ADF4A3EAD74 (void);
extern void RenderTexture_set_height_m04F8A4678F13855D357AA3458097DF1FD6D5BDEE (void);
extern void RenderTexture_SetColorFormat_mB68B8EC61ACF16D78BC3198ABBEDFC9921A39B9D (void);
extern void RenderTexture_set_graphicsFormat_m4CFDFF4CEC81B5350AE94D466585C8186E06B733 (void);
extern void RenderTexture_set_depthStencilFormat_mCEED5DA45F9F19CD67D960D896400E85B1211855 (void);
extern void RenderTexture_GetActive_m01B8BCBAC96889D938D87000815544470E7EE40B (void);
extern void RenderTexture_SetActive_m54E8330D7137E080D7E2EF2FD495BC906126AEB2 (void);
extern void RenderTexture_get_active_mA4434B3E79DEF2C01CAE0A53061598B16443C9E7 (void);
extern void RenderTexture_set_active_m5EE8E2327EF9B306C1425014CC34C41A8384E7AB (void);
extern void RenderTexture_GetColorBuffer_mDCF981FDA18342C7D239932B7105CA0DB1798E4A (void);
extern void RenderTexture_GetDepthBuffer_mA3CA39776C8174D199AD26E868B3EB720C6B801B (void);
extern void RenderTexture_SetMipMapCount_m037EE54855938389E781C6CFA5A398641BC7CB83 (void);
extern void RenderTexture_get_colorBuffer_mE043AF01C1B2FB73BDC9C82D78528A367089CDE0 (void);
extern void RenderTexture_get_depthBuffer_mBBDFA14B3AC2AE4796795E89A0BCA59D54B859D5 (void);
extern void RenderTexture_SetSRGBReadWrite_mBE116D34F4B919AC7E48814ABC9D623A9FE518C0 (void);
extern void RenderTexture_Internal_Create_m9468E1F6533E5E80D6CDD6356472423FC6349BFE (void);
extern void RenderTexture_SetRenderTextureDescriptor_m498C0757E95407077AF11B034319DF7AC904BA18 (void);
extern void RenderTexture_GetDescriptor_mB180407D5E1215BF1651A913AC146D25EAE9C6BC (void);
extern void RenderTexture_GetTemporary_Internal_m33027F4E37F8D03F8B8FB931D0A498096F590F63 (void);
extern void RenderTexture_ReleaseTemporary_mEEF2C1990196FF06FDD0DC190928AD3A023EBDD2 (void);
extern void RenderTexture__ctor_m731283139A246174F3F33B37991F9BFFBD29293D (void);
extern void RenderTexture__ctor_m1CBDB7F13C2CE49A31EE654081F01C4F874EA5E3 (void);
extern void RenderTexture__ctor_mF6FCD7FA9976CC646BDF8715157EA198A992E75B (void);
extern void RenderTexture__ctor_m69A0AF5C6CCFFFB58D9F5A0C975D0272CA66684B (void);
extern void RenderTexture__ctor_m0C81127DE754F64FDD3E80E94BE11054B2791F98 (void);
extern void RenderTexture__ctor_m583FCACDD5FCA4102329911331B6DC51660795F0 (void);
extern void RenderTexture__ctor_m0F1316F315E35B4E305FE929604E8F489189C39D (void);
extern void RenderTexture__ctor_mD60FB2D8D9560774F2E21BAC0A0061CB17904EA3 (void);
extern void RenderTexture__ctor_m68A1B9CAA1BE0B597C5F4895C296E21502D0C962 (void);
extern void RenderTexture__ctor_m53215A8EDDE262932758186108347685F6A512C4 (void);
extern void RenderTexture__ctor_m45EACC89DDF408948889586516B3CA7AA8B73BFA (void);
extern void RenderTexture__ctor_m7E177DB2DEF7CD2EEB812EEB262081E923BEF2AC (void);
extern void RenderTexture_Initialize_m924B8245CB23DF4FECCF5A68B158A393DC0401D0 (void);
extern void RenderTexture_GetDepthStencilFormatLegacy_mC4C41354858D1CE6275439361C1763854E42E2C5 (void);
extern void RenderTexture_GetDepthStencilFormatLegacy_m7903427680451A0E045B90B765E8B365988F5A4B (void);
extern void RenderTexture_GetDepthStencilFormatLegacy_mEA344F7BCAAFD3415A95032EBB1F5AED8C0926A1 (void);
extern void RenderTexture_GetDepthStencilFormatLegacy_m5062F40BEAE6F266A5B9109335DFDC6DA9B59417 (void);
extern void RenderTexture_get_descriptor_m2FABD5CF6CCF410D1311FCBC7C9D9ECDEE9C7CC2 (void);
extern void RenderTexture_ValidateRenderTextureDesc_m9A67530FAD20FE54836E62B6B45AF9A4375C3340 (void);
extern void RenderTexture_GetDefaultColorFormat_m3E5847AB5B7C4EF634A571F032A74BB4F1E1AF71 (void);
extern void RenderTexture_GetDefaultDepthStencilFormat_mB411FD8C581A112B308352312EF07BDF7FECD08E (void);
extern void RenderTexture_GetCompatibleFormat_mF23017BDD92678EBD0B8B861BA6163B04EF55DBB (void);
extern void RenderTexture_GetTemporary_mA8C827B80D3C07D0B8CDF7F5270FB5D3E53DD235 (void);
extern void RenderTexture_GetTemporaryImpl_m865E3CCF5F6D0125C51E8CC6B2D86811A4F7F71A (void);
extern void RenderTexture_GetTemporary_mED96CF4304FE2CAFCC1ECBC08F6AC01EAE26B07B (void);
extern void RenderTexture_GetTemporary_m8B161095886EFC9B592F153D6C1D933C067DF809 (void);
extern void RenderTexture_GetTemporary_m82011491839499F6F3263CD9982B13292142C51D (void);
extern void RenderTexture_GetTemporary_m925A4E25416A9FF58E9CC0CA67BBEB3A971DDFC3 (void);
extern void RenderTexture_GetTemporary_mA6619EA324AAE80B6892107C6968092F6F1B4C45 (void);
extern void RenderTexture_GetTemporary_mDAD0D2A673F07BEC3B1A9555863E24A479E9BB11 (void);
extern void RenderTexture_GetColorBuffer_Injected_m1AC1564038AB3F39ECFDB8C98AC573F4803BEE68 (void);
extern void RenderTexture_GetDepthBuffer_Injected_m5EA9252E3F0DFAEDE76303614E7683CC549B05F4 (void);
extern void RenderTexture_SetRenderTextureDescriptor_Injected_m92390C2D26840E69E7B8F3DF5DA44FCB078F363F (void);
extern void RenderTexture_GetDescriptor_Injected_m37E30C68CAB7F283BDD1FA2F580066C4BA80A085 (void);
extern void RenderTexture_GetTemporary_Internal_Injected_m5CF82A63F62919ACA725691BCA5B4412B906F454 (void);
extern void RenderTextureDescriptor_get_width_mB159E4EB08B23B19CCCFADB465864361FB840BFF (void);
extern void RenderTextureDescriptor_set_width_m3B2494007BFE3AD4D14403407C9B24F5045E7E10 (void);
extern void RenderTextureDescriptor_get_height_m1006F9AA45029715C552C8A8C2F102F63D3A91EC (void);
extern void RenderTextureDescriptor_set_height_m1FE41111472DAA9B5E80FFAF3445004D72A3CFA5 (void);
extern void RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22 (void);
extern void RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4 (void);
extern void RenderTextureDescriptor_get_volumeDepth_m528818299E101F1B285B08BE12FAC2F9A871BA36 (void);
extern void RenderTextureDescriptor_set_volumeDepth_mEF9610D1C14182417A01B7243DEE6B559A13B34D (void);
extern void RenderTextureDescriptor_set_mipCount_mDCC85ED7D97BD64A290A21DB91BC5CB1C4BA95EF (void);
extern void RenderTextureDescriptor_get_graphicsFormat_m50F25A4F179EA318C8D3B0D8685F9C5F59F7DEC0 (void);
extern void RenderTextureDescriptor_set_graphicsFormat_m037DA25F9A8B956D830C7B7E5C6E258DC1133A13 (void);
extern void RenderTextureDescriptor_get_depthStencilFormat_m360929BE5BD10E9C3D8C936AA6B44B1D11C119CB (void);
extern void RenderTextureDescriptor_set_depthStencilFormat_m7EC335006743B59AAD1895AD06B2DD74333F9A03 (void);
extern void RenderTextureDescriptor_get_depthBufferBits_mC095E36F9803B2E68E258C03E48ACD0B0E678953 (void);
extern void RenderTextureDescriptor_set_depthBufferBits_mA3710C0D6E485BA6465B328CD8B1954F0E4C5819 (void);
extern void RenderTextureDescriptor_get_dimension_mA23ABB2CA03249DCE3A21F5123524A825C33E31B (void);
extern void RenderTextureDescriptor_set_dimension_mCE9A4A08454BB2D9DFE3E505EC336FD480078F39 (void);
extern void RenderTextureDescriptor_set_shadowSamplingMode_m4B4CE918DFFF1CC5E3AF981456E186F15FC5DB93 (void);
extern void RenderTextureDescriptor_set_vrUsage_m994CB3D4B250A70BE005D9FDFD24D868E07A52F0 (void);
extern void RenderTextureDescriptor_set_memoryless_m9ECE149930C0E2629A5CD9DA1CD0EA2A01FFE1B2 (void);
extern void RenderTextureDescriptor__ctor_m8B0D32DC550540B5546891C2F6300F384D6FE692 (void);
extern void RenderTextureDescriptor__ctor_m8F8897C63F614AEA4348A95293C911C1293DA3A4 (void);
extern void RenderTextureDescriptor_SetOrClearRenderTextureCreationFlag_m4C08C7A3F715426EBECA2B983361908D097C6424 (void);
extern void RenderTextureDescriptor_set_createdFromScript_mEE28DED1D3C20DA025A0C44E1C2A531685194F23 (void);
extern void RenderTextureDescriptor_set_useDynamicScale_m9335866C8754D51235D391E84F8972C4C518844A (void);
extern void Hash128__ctor_m0B61E717B3FF7D7BBD8FF12C8C8327C18A2AAAF3 (void);
extern void Hash128_CompareTo_mFFE3648A372A7F3202897B266E14A6E2521BFE0C (void);
extern void Hash128_ToString_m35513B62830C8CE59346AF87AC2B587FA1570DCE (void);
extern void Hash128_Parse_mA918076817FF18F2172F1427AFA472C0A8321E47 (void);
extern void Hash128_Hash128ToStringImpl_m351D7595F82E3287DDE4D5D97245140D98849A42 (void);
extern void Hash128_Equals_m28FADCC2F9A565AF152A53BCEEF88F798716B104 (void);
extern void Hash128_Equals_mF6BED87E0744D6DFFF8026614BDA8F4E6712063D (void);
extern void Hash128_GetHashCode_m22816EE33CD973D11CD1917DEF7A0E0EC229E1D8 (void);
extern void Hash128_CompareTo_m1D249BA0DD01AF06E5B96B186E8A5F181BB30758 (void);
extern void Hash128_op_Equality_m870F4E857699627E5235BBD0698022E023C8C8F5 (void);
extern void Hash128_op_LessThan_mD2B90720E6C871528BB20F19FED34883A5BC8A43 (void);
extern void Hash128_op_GreaterThan_m7AAFA151BF17A7A54856E529F8B358F566508237 (void);
extern void Hash128_Parse_Injected_m000B782285D88C832BA0130E5BC3E52BC60D79FA (void);
extern void Hash128_Hash128ToStringImpl_Injected_m3235EBF9B6966B9720D13EAEC3AFDC2824AFF332 (void);
extern void Cursor_SetCursor_m00781B5A9FDF84759E2D8869F08E6120062FCE86 (void);
extern void Cursor_get_lockState_m9AD145AFB215DFEDA0074AC1F129EF02CCE5B632 (void);
extern void Cursor_SetCursor_Injected_mCAFC0AE81FA6AE6C9575CA82E64AC48A94883CC8 (void);
extern void Logger__ctor_m3155E21A68AA616431A260A3FCBB4B074DF6FAA2 (void);
extern void Logger_get_logHandler_m4FAA2028695BD9FBA134E836AD52480984E82215 (void);
extern void Logger_set_logHandler_mD2A80ADC7D4155C91B68EAE1FDBD7ECBF6DB49D3 (void);
extern void Logger_get_logEnabled_m0A868820FAADBF477B46963F5050AFCBBC46AF0B (void);
extern void Logger_set_logEnabled_m3FBDDB7D1C5D9854C0F32ED6F7AB0348F286F52E (void);
extern void Logger_get_filterLogType_mCE9711AF596B77EAD158590247FA5FC855F54A54 (void);
extern void Logger_set_filterLogType_m8EF119582F4975FE3D297EE71F9D3E1CFEA7AB74 (void);
extern void Logger_IsLogTypeAllowed_mFE76B00210BF4431747A69A28A15EE2BF1A0D586 (void);
extern void Logger_GetString_m2965E4936E7B1C763CE7A3FF6AACE9590DA7D7BE (void);
extern void Logger_Log_mEA3D39763D610E92491AA479BA653ECFEE3E9E5C (void);
extern void Logger_Log_mF8C7E8A8CC31E04732044D73D2CB551D7CCB8995 (void);
extern void Logger_LogError_m4612980842D15256036F4EB16BADF13FD49F38F5 (void);
extern void Logger_LogException_m591AF39F0886DA44666068EDBBD3CCF07623CFBB (void);
extern void Logger_LogFormat_m5A31966B8AA13AC1FFEC1DED42F56FA966459093 (void);
extern void Logger_LogFormat_m776A546E755F914039AB8591E23D08510308DB4C (void);
extern void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C (void);
extern void Color__ctor_mCD6889CDE39F18704CD6EA8E2EFBFA48BA3E13B0 (void);
extern void Color_ToString_m0018DE2184B3377CCA6FBD72D5D47886DC669147 (void);
extern void Color_ToString_m70AEF3634C556F6AA01FC3236226C3D27C277229 (void);
extern void Color_GetHashCode_m2981EEA1DEFE55254945D7D03BE64D4F56BA58D0 (void);
extern void Color_Equals_m24E409BF329F25774C6577F18EFD3DE1253684D6 (void);
extern void Color_Equals_mD297CAFFEBE9352C940873862FDF9A28F1F02435 (void);
extern void Color_op_Multiply_mD0296202733CB2D5342FB7C82B48AEDA63036758 (void);
extern void Color_op_Multiply_m379B20A820266ACF82A21425B9CAE8DCD773CFBB (void);
extern void Color_op_Equality_mB2BDC39B0B367BA15AA8DF22F8CB0D02D20BDC71 (void);
extern void Color_op_Inequality_mF1C733BA10E60B086AB950A71143678AE76C4D92 (void);
extern void Color_Lerp_mE79F87889843ECDC188E4CB5B5E1F1B2256E5EBE (void);
extern void Color_LerpUnclamped_m91027D026E64424B71959149B942F706FC16B1A2 (void);
extern void Color_RGBMultiplied_m4B3BAE4310EA98451D608E0300331012AFFF1B01 (void);
extern void Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D (void);
extern void Color_get_green_mEB001F2CD8C68C6BBAEF9101990B779D3AA2A6EF (void);
extern void Color_get_blue_mF04A26CE61D6DA3C0D8B1C4720901B1028C7AB87 (void);
extern void Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6 (void);
extern void Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737 (void);
extern void Color_get_yellow_m66637FA14383E8D74F24AE256B577CE1D55D469F (void);
extern void Color_get_clear_m02E023A7D396B973288B3915F6F24FBF7E0DC81D (void);
extern void Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C (void);
extern void Color_get_maxColorComponent_m97D2940D48767ACC21D76F8CCEAD6898B722529C (void);
extern void Color_op_Implicit_m9B3228DAFA8DC57A75DE00CBBF13ED4F1E7B01FF (void);
extern void Color_op_Implicit_mA8CF4745D766F4F610E1BE0A1ED2F4E5FE5D734C (void);
extern void Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E (void);
extern void Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70 (void);
extern void Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E (void);
extern void Color32_ToString_mB1EFBF981F901A76ABF2FEA19EB290A2D8CAFC32 (void);
extern void Color32_ToString_m263D34787852D176627FC2B910DFE9CABAF26696 (void);
extern void Gradient_Init_m5BA62E19CAFB02B0EC3FEEF2247F0D7D79DB4A95 (void);
extern void Gradient_Cleanup_m852D91204DE7A67197754BC5EEF35BBC340795E9 (void);
extern void Gradient_Internal_Equals_m8499EFC381172FFC827366BE219C9CDA8A444D0C (void);
extern void Gradient__ctor_m5EC470BB063D4831774C7CDA5D471EBEB5CE7B54 (void);
extern void Gradient_Finalize_m69475357E7933E7692476BB6B8952E06EC4F23CC (void);
extern void Gradient_Equals_m3CA73EEE2426924D75D835A69F00B9EB50D44294 (void);
extern void Gradient_Equals_m77211B56445AB40DEF32AE890BD6B13E2410FB98 (void);
extern void Gradient_GetHashCode_m031DD1B99829405A1654F69EC63CFF929139C94C (void);
extern void Matrix4x4_GetLossyScale_m3C19D2C6746BB211C8CB02478A60EB2D71D10FC7 (void);
extern void Matrix4x4_get_lossyScale_mFB3D4CF6408D710D607CA1D2AF94B2E4E0B57EB7 (void);
extern void Matrix4x4_TRS_mCC04FD47347234B451ACC6CCD2CE6D02E1E0E1E3 (void);
extern void Matrix4x4_Inverse3DAffine_m7A7796EE699A2228A709611D11364541DE768AE6 (void);
extern void Matrix4x4_Inverse_mFB2503F5D5FE76E7C56249700ED2E43DDA0F1939 (void);
extern void Matrix4x4_get_inverse_m4F4A881CD789281EA90EB68CFD39F36C8A81E6BD (void);
extern void Matrix4x4__ctor_m6523044D700F15EC6BCD183633A329EE56AA8C99 (void);
extern void Matrix4x4_GetHashCode_m313B1AF4FEA792BB7E4D1D239EBE3257F825914D (void);
extern void Matrix4x4_Equals_m35CFC5F304BB40EFFE011B92AA87B77CD8FF8F74 (void);
extern void Matrix4x4_Equals_mDB0C4CCC58BE3E108F1A40BE8DBDCD62E284CC51 (void);
extern void Matrix4x4_op_Multiply_m75E91775655DCA8DFC8EDE0AB787285BB3935162 (void);
extern void Matrix4x4_GetColumn_m5CE079D7A69DE70E3144BADD20A1651C73A8D118 (void);
extern void Matrix4x4_GetRow_m59C6981300C6F6927BEA17C5D095B2AD29629E9F (void);
extern void Matrix4x4_MultiplyPoint_m20E910B65693559BFDE99382472D8DD02C862E7E (void);
extern void Matrix4x4_MultiplyPoint3x4_mACCBD70AFA82C63DA88555780B7B6B01281AB814 (void);
extern void Matrix4x4_Rotate_m015442530DFF5651458BBFDFB3CBC9180FC09D9E (void);
extern void Matrix4x4_get_identity_m6568A73831F3E2D587420D20FF423959D7D8AB56 (void);
extern void Matrix4x4_ToString_mB310BE20B7CDE8AEA91D84FCA4E75BCACF7DFC86 (void);
extern void Matrix4x4_ToString_mB8E1EFF14A37605ABA321708CC36021FFBCF84CF (void);
extern void Matrix4x4__cctor_mD8409F5527865C5B73AC1195893BD110330FDB92 (void);
extern void Matrix4x4_GetLossyScale_Injected_mABC36BB3229BE212643B44F6B090C86CBF65D607 (void);
extern void Matrix4x4_TRS_Injected_m5EF976A6FBB27DD3E44C0B51575EBEEF802EB6F1 (void);
extern void Matrix4x4_Inverse3DAffine_Injected_mA108B0432274BE970746A528E27849AA6BD3EF05 (void);
extern void Matrix4x4_Inverse_Injected_m666E1049EA37568CCC0C3636194F8A815E30DF66 (void);
extern void Vector3_Lerp_m3A906D0530A94FAABB94F0F905E84D99BE85C3F8 (void);
extern void Vector3_LerpUnclamped_m4109A459C1DB823310A10B8B1E80CB6877418347 (void);
extern void Vector3_get_Item_m163510BFC2F7BFAD1B601DC9F3606B799CF199F2 (void);
extern void Vector3_set_Item_m79136861DEC5862CE7EC20AB3B0EF10A3957CEC3 (void);
extern void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0 (void);
extern void Vector3__ctor_m5F87930F9B0828E5652E2D9D01ED907C01122C86 (void);
extern void Vector3_Cross_mF93A280558BCE756D13B6CC5DCD7DE8A43148987 (void);
extern void Vector3_GetHashCode_mB08429DC931A85BD29CE11B9ABC77DE7E0E46327 (void);
extern void Vector3_Equals_mB4BE43D5986864F5C22B919F2957E0309F10E3B4 (void);
extern void Vector3_Equals_mEDEAF86793D229455BBF9BA5B30DDF438D6CABC1 (void);
extern void Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F (void);
extern void Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07 (void);
extern void Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52 (void);
extern void Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B (void);
extern void Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8 (void);
extern void Vector3_Min_m1CAC3499F14EA87366C0C3C1F501B4FB2863CDB4 (void);
extern void Vector3_Max_m9B6D8FEE7F4CE32C0AAB682606FFBA59E1F37C74 (void);
extern void Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39 (void);
extern void Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02 (void);
extern void Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A (void);
extern void Vector3_get_back_mCA5A84170E8DE5CE38C0551B4CCAD647BF215E57 (void);
extern void Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA (void);
extern void Vector3_get_down_mF62B2AE7C5AC31EAC9CB62797C7190C90A7A8599 (void);
extern void Vector3_get_left_m8C1116485A9E689760AEE1142F5977852278B7E1 (void);
extern void Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0 (void);
extern void Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067 (void);
extern void Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828 (void);
extern void Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915 (void);
extern void Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353 (void);
extern void Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41 (void);
extern void Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB (void);
extern void Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479 (void);
extern void Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF (void);
extern void Vector3_ToString_m6C24B9F0382D25D75B05C606E127CD14660574EE (void);
extern void Vector3_ToString_mA8DA39B6324392BB93203A4D4CB85AF87231CB62 (void);
extern void Vector3__cctor_mBE529AB9D272468D923A964C5F2CE611422EADA8 (void);
extern void Quaternion_Inverse_mD9C060AC626A7B406F4984AC98F8358DC89EF512 (void);
extern void Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E (void);
extern void Quaternion_AngleAxis_mF37022977B297E63AA70D69EA1C4C922FF22CC80 (void);
extern void Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8 (void);
extern void Quaternion_get_identity_m7E701AE095ED10FD5EA0B50ABCFDE2EEFF2173A5 (void);
extern void Quaternion_op_Multiply_mE1EBA73F9173432B50F8F17CE8190C5A7986FB8C (void);
extern void Quaternion_IsEqualUsingDot_m9C672201C918C2D1E739F559DBE4406F95997CBD (void);
extern void Quaternion_op_Equality_mE6F6B56FCED8478552BE02BBAF18C70B969217F9 (void);
extern void Quaternion_op_Inequality_m4EC1EF263D0E42432A301F85CB52028D2973F5DA (void);
extern void Quaternion_Dot_mF9D3BE33940A47979DADA7E81650AEB356D5D12B (void);
extern void Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90 (void);
extern void Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73 (void);
extern void Quaternion_GetHashCode_m5F55C34C98E437376595E722BE4EB8A70434F049 (void);
extern void Quaternion_Equals_mCF93B00BA4FCDDE6100918451343DB9A0583A0A0 (void);
extern void Quaternion_Equals_m25B95D8412B79CC7F8B34062BFAE662BD99578BE (void);
extern void Quaternion_ToString_mC5BD5DEF60FCA4A38924462A5C4440ECFCF934C4 (void);
extern void Quaternion_ToString_m9B592D577B3FDB892CA53ABF3457BC2EDE45DF8C (void);
extern void Quaternion__cctor_m0C8018E54211DA5CFB4CF107C12EE466951F6575 (void);
extern void Quaternion_Inverse_Injected_m9BFD9E0A669FDB8227D7736F1B6E5795A97E4563 (void);
extern void Quaternion_Internal_FromEulerRad_Injected_m2C2420C918664E16478C6055D5ED421B15D812B8 (void);
extern void Quaternion_AngleAxis_Injected_mA80506B6DBE241FF55113EA65838923C64BDFAC5 (void);
extern void Mathf_IsPowerOfTwo_m58172AEBE272F53FD34CC10641057847181E960A (void);
extern void Mathf_NextPowerOfTwo_mA1CE7F3EEF9B0B07AB2D586C030ED236D578F485 (void);
extern void Mathf_GammaToLinearSpace_mEF9E26BAD322E55448B286ABDCDF4A2CC236547F (void);
extern void Mathf_CorrelatedColorTemperatureToRGB_m595A3D1E887CD42FE21CD2893D8E377B3F44153C (void);
extern void Mathf_Sin_m8498BAF996D13BEEE734F6D1B1BE4D7853BBF2C0 (void);
extern void Mathf_Cos_mB9E0E085EE4433B820DC752E68B395A7C59014AA (void);
extern void Mathf_Tan_m02B11ED38704291D448322E57D396F9FAB4EE5B7 (void);
extern void Mathf_Acos_m18B78367C77DC32AA04D2623FB860CF7DF587093 (void);
extern void Mathf_Atan_m92C7BFD0B9C392FA72C8940EC4EA3F927907ABF5 (void);
extern void Mathf_Atan2_m329EF9B5D191198E9FC08769FB0E1D27632192E3 (void);
extern void Mathf_Sqrt_m42E8E54D522D4FA070A76AD1ED62BFFEEEB3317A (void);
extern void Mathf_Abs_m4778EE107161FAC49E51E7BEF3F4A4FB2F02A715 (void);
extern void Mathf_Abs_mD945EDDEA0D62D21BFDBAB7B1C0F18DFF1CEC905 (void);
extern void Mathf_Min_m747CA71A9483CDB394B13BD0AD048EE17E48FFE4 (void);
extern void Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB (void);
extern void Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051 (void);
extern void Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8 (void);
extern void Mathf_Pow_m54654EC32D45E0679E06F367125EE3EE3BD086F1 (void);
extern void Mathf_Log_m116F062EEBF1C53EC8D18C9B1748E999EF9424EF (void);
extern void Mathf_Log10_m807D2F7C24B7A9BBB8C4E5633F1E8FDACFB267C2 (void);
extern void Mathf_Ceil_mDEE98B3A54FB2F82B3783DB30ECB31E2015C57D9 (void);
extern void Mathf_Floor_mF5A5487B496B3DE8196ADABC733E45A895F422AA (void);
extern void Mathf_Round_m5C834461627B464609DF863457BB4ED2CD5FEA9E (void);
extern void Mathf_CeilToInt_mF2BF9F4261B3431DC20E10A46CFEEED103C48963 (void);
extern void Mathf_FloorToInt_m2A39AE881CAEE6B6A4B3BFEF9CA1ED40625F5AB7 (void);
extern void Mathf_RoundToInt_m60F8B66CF27F1FA75AA219342BD184B75771EB4B (void);
extern void Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A (void);
extern void Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF (void);
extern void Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B (void);
extern void Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14 (void);
extern void Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5 (void);
extern void Mathf_LerpUnclamped_mF35042F9D25A221BDD4B3FD6251224610AFC1F35 (void);
extern void Mathf_Approximately_m1DADD012A8FC82E11FB282501AE2EBBF9A77150B (void);
extern void Mathf_SmoothDamp_mBE7ABB6B59D198BE8ABE42942452CC7B813A5248 (void);
extern void Mathf_Repeat_m6F1560A163481BB311D685294E1B463C3E4EB3BA (void);
extern void Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D (void);
extern void Mathf_ClampToFloat_mFE6AEF79E95B21E93763BB1D4163CCBDCCB91D4D (void);
extern void Mathf_ClampToInt_mCFCFE5DEB4C6009EFF3CDDA8E934076588AA2C9E (void);
extern void Mathf_ClampToUInt_mC2D0AB113A813EA9B5C121399F18EB732DE7945C (void);
extern void Mathf_GetNumberOfDecimalsForMinimumDifference_m631B3AB68CB1A03827527AF355982ED7786F090C (void);
extern void Mathf_RoundBasedOnMinimumDifference_m5BE4283F3981DD32F74EFD8FFE4320FC21156DF9 (void);
extern void Mathf_DiscardLeastSignificantDecimal_mA70D60612195263655D9791342C227B2888458AE (void);
extern void Mathf__cctor_m096B4CC2F751E87D1018F79D32E28182BDC5E652 (void);
extern void Mathf_CorrelatedColorTemperatureToRGB_Injected_mE0B92B9F5833D54E73BCF44C715B98F4CAB74020 (void);
extern void Vector2_get_Item_m18BC65BB0512B16A1F9C8BE4B83A3E7BBAD7064D (void);
extern void Vector2_set_Item_mEF309880B9B3B370B542AABEB32256EEC589DD03 (void);
extern void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548 (void);
extern void Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C (void);
extern void Vector2_LerpUnclamped_mB0B50875D4509E21FF43F4D87610333D55E6A44F (void);
extern void Vector2_Scale_m8D61A8D2272549F1EF41256F7E8A206C6500EA6C (void);
extern void Vector2_ToString_mB47B29ECB21FA3A4ACEABEFA18077A5A6BBCCB27 (void);
extern void Vector2_ToString_mC10F098442E56919947154402A77EDE28DC9B7BE (void);
extern void Vector2_GetHashCode_mED8670C0E122B7ED0DAB4C3381ADFF04B75E0B03 (void);
extern void Vector2_Equals_mA4E81D6FCE503DBD502BA499708344410F60DA4E (void);
extern void Vector2_Equals_mDF84D5ED14E018609C6A9C9BAE016C1B33BCFF4C (void);
extern void Vector2_Dot_mC1E68FDB4FB462A279A303C043B8FD0AC11C8458 (void);
extern void Vector2_get_magnitude_m5C59B4056420AEFDB291AD0914A3F675330A75CE (void);
extern void Vector2_get_sqrMagnitude_mA16336720C14EEF8BA9B55AE33B98C9EE2082BDC (void);
extern void Vector2_Min_m2D222BC18ACD4F965981EC93451DDD1D7ADFDDA0 (void);
extern void Vector2_Max_m1E906743ECA6478A2EDCCFECD9D509898F66152B (void);
extern void Vector2_op_Addition_m8136742CE6EE33BA4EB81C5F584678455917D2AE (void);
extern void Vector2_op_Subtraction_m44475FCDAD2DA2F98D78A6625EC2DCDFE8803837 (void);
extern void Vector2_op_Multiply_m6FFFFF6A9E069A4FBCA7E098D88AE39C2B80D442 (void);
extern void Vector2_op_Division_m707994C12D356E65E453CEE8F767E652B60911BF (void);
extern void Vector2_op_UnaryNegation_mBA9FC53A2194EE3CC067A12D11879F695B34D6F9 (void);
extern void Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE (void);
extern void Vector2_op_Multiply_mC53581E703768BA2512A7C65283657C331994353 (void);
extern void Vector2_op_Division_m57A2DCD71E0CE7420851D705D1951F9238902AAB (void);
extern void Vector2_op_Equality_m6F2E069A50E787D131261E5CB25FC9E03F95B5E1 (void);
extern void Vector2_op_Inequality_mBEA93B5A0E954FEFB863DC61CB209119980EC713 (void);
extern void Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96 (void);
extern void Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7 (void);
extern void Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C (void);
extern void Vector2_get_one_m9097EB8DC23C26118A591AF16702796C3EF51DFB (void);
extern void Vector2_get_up_m41067879408BB378593EF7406AF2525F176F0ABF (void);
extern void Vector2_get_down_m7672D87B9C778FB2AEF7BB79758142D01166B493 (void);
extern void Vector2_get_left_m851D1A435131CE336F60115E19FC8C21480284BC (void);
extern void Vector2_get_right_m99043ED6B3D5AEA5033313FE3DA9571F39D1B280 (void);
extern void Vector2_get_negativeInfinity_mE3346BA420245D8529F57AAE8A28A3BB03C214C0 (void);
extern void Vector2__cctor_m985790D0B5F5F80A0E3461F04F05BAB8CBAD0105 (void);
extern void Vector2Int_get_x_mA2CACB1B6E6B5AD0CCC32B2CD2EDCE3ECEB50576 (void);
extern void Vector2Int_set_x_m291ECF246536852F0B8EE049C4A3768E4999CDC8 (void);
extern void Vector2Int_get_y_m48454163ECF0B463FB5A16A0C4FC4B14DB0768B3 (void);
extern void Vector2Int_set_y_mF81881204EEE272BA409728C7EBFDE3A979DDF6A (void);
extern void Vector2Int__ctor_mC20D1312133EB8CB63EC11067088B043660F11CE (void);
extern void Vector2Int_op_Implicit_m5B9FB268943E6CAB6E40E13D30BA49A9AC7D2059 (void);
extern void Vector2Int_FloorToInt_m11F1E02A791A7C690228999EA6E9F84FB0DF56FD (void);
extern void Vector2Int_op_Equality_mD80F6ED22EA1200C4F408440D02FE61388C7D6BA (void);
extern void Vector2Int_Equals_m6D91EFAA6B3254334436BD262A4547EA08281BA3 (void);
extern void Vector2Int_Equals_m32811BA0576C096D5EB5C0CFD8231478F17229A6 (void);
extern void Vector2Int_GetHashCode_mA3B6135FA770AF0C171319B50D9B913657230EB7 (void);
extern void Vector2Int_ToString_m6F7E9B9B45A473FED501EB8B8B25BA1FE26DD5D4 (void);
extern void Vector2Int_ToString_m44BA6941AEF41076A39848B95DDEFEA88A094B5E (void);
extern void Vector2Int__cctor_m1851D9EC457C9518497A216CC821CB4D27BAC2DD (void);
extern void Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C (void);
extern void Vector3Int_set_x_m8745C5976D035EBBAC6F6191B5838D58631D8685 (void);
extern void Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72 (void);
extern void Vector3Int_set_y_mA856F32D1BF187BD4091DDF3C6872FD01F7D3377 (void);
extern void Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED (void);
extern void Vector3Int_set_z_m5782180F67C4257C505F124971985D99C3422F74 (void);
extern void Vector3Int__ctor_mE06A86999D16FA579A7F2142B872AB7E3695C9E0 (void);
extern void Vector3Int_op_Equality_mB10073AF3B08421C46BF678C8FF64AAD62C83617 (void);
extern void Vector3Int_Equals_m419967067E76BF0381E4CD8FE14DF5ED46ACFB02 (void);
extern void Vector3Int_Equals_mE4D179C5001B77DE05E3E4BC39DC9F6AE441EBD8 (void);
extern void Vector3Int_GetHashCode_mFAA200CFE26F006BEE6F9A65AFD0AC8C49D730EA (void);
extern void Vector3Int_ToString_m49EB16DEA24181270D65A0F4ED39B3E8A46DB539 (void);
extern void Vector3Int_ToString_m74C285E175F089CED3A53A678216CD15A0AD0067 (void);
extern void Vector3Int__cctor_m1D349715BC46140220E3F8E867861CF9F568ED65 (void);
extern void Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF (void);
extern void Vector4_set_Item_mF24782F861A16BB0436C2262FA916B4EE69998A6 (void);
extern void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813 (void);
extern void Vector4_GetHashCode_m53F6FCA56A0051C9D1AB41AA3EAA2C86CDAA8B92 (void);
extern void Vector4_Equals_mC2596CA0F441D25DE7A9419BE66A8FA2DA355CB9 (void);
extern void Vector4_Equals_m73FAA65A1A565EE28D6C9385603829894B7D4392 (void);
extern void Vector4_Dot_m40A3B2E258E53E4847583474E40AC29F68AF8BA3 (void);
extern void Vector4_get_sqrMagnitude_m864A2908BCF9E060BA73DE3DD259EC06F47F913C (void);
extern void Vector4_get_zero_m3D61F5FA9483CD9C08977D9D8852FB448B4CE6D1 (void);
extern void Vector4_op_Multiply_m02FE150FD7366546FC19A36B6928512376BF64E8 (void);
extern void Vector4_op_Division_m9B1B8692D50C864CFA585BDF97FB6FBC18967D90 (void);
extern void Vector4_op_Equality_mCEA0E5F229F4AE8C55152F7A8F84345F24F52DC6 (void);
extern void Vector4_op_Inequality_mD6A1C6E862F3EFB1B222A2DDCB7A7237042DE142 (void);
extern void Vector4_op_Implicit_m2ECA73F345A7AD84144133E9E51657204002B12D (void);
extern void Vector4_op_Implicit_m0217ADDC8CADDB93ACBABB17A50207698DAB0071 (void);
extern void Vector4_op_Implicit_mB193CD8DA20DEB9E9F95CFEB5A2B1B9B3B7ECFEB (void);
extern void Vector4_op_Implicit_m6673D431FBCA5AFB6CF06CD9783D07A4C90CC2AA (void);
extern void Vector4_ToString_mFA0DDF34C1E394F75EF65E06764A1BE750E7F388 (void);
extern void Vector4_ToString_m2BE67BEBBD3059C9CEE29BF34AD30E1D7057E914 (void);
extern void Vector4__cctor_mE18131D0C5FFBF3305D29CB0F337057BF8CEB802 (void);
extern void TypeDispatchData_Dispose_m5E70993B62AFC8BB19163A8D7711BD3B5CC9424C (void);
extern void TransformDispatchData_Dispose_m1C2E3B8C249D6E8785C19CC17C7ACBA4167A31BF (void);
extern void ObjectDispatcher__cctor_mA8CA2F8BC2ABD7806F5A05D683DB78208EDBDB9D (void);
extern void U3CU3Ec__cctor_m8766AEC9AA751EEF02B6EF76931FE6D163858575 (void);
extern void U3CU3Ec__ctor_m664FC291635917D034E486E0BFE015135BAB01E4 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__54_0_mD6C58FAB3C4B1F6D05D271D4EB6BA99BDBCD4542 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__54_1_mE001C98479DA3376C0CA590F7761221C7D57BCD6 (void);
extern void NumericFieldDraggerUtility_Acceleration_m68C7F9D2F37BF3F5658F30022507A438C4CD7BE1 (void);
extern void NumericFieldDraggerUtility_NiceDelta_mF0E1FACF86DA1579771FD66E98B0B3099A5D88CB (void);
extern void NumericFieldDraggerUtility_CalculateFloatDragSensitivity_m3D2CA2BA1D321F49061EE76A82868CBF44A08BA7 (void);
extern void NumericFieldDraggerUtility_CalculateFloatDragSensitivity_m15371E9C38110F5D47A63B328E1F8B91851F9331 (void);
extern void NumericFieldDraggerUtility_CalculateIntDragSensitivity_m18783F2C97722F41968E8CB9F2D1A15ED347A6BA (void);
extern void NumericFieldDraggerUtility_CalculateIntDragSensitivity_m2151DB0891CAF25A9D460E5D6ADB05D1262112D4 (void);
extern void NumericFieldDraggerUtility_CalculateIntDragSensitivity_mC168204AC43790918502BAE324E736E34B91DB01 (void);
extern void NumericFieldDraggerUtility_CalculateIntDragSensitivity_m1B98B6FC83BA9B2234B93A578F253B01C45E7E22 (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_SendMessage_m561CA89041EBAFF52314B0D35F7335AF03690EE2 (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_TrySendMessage_mD5B897823668810AC92B7A4DB37D2AC469B5AD92 (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_Poll_m81154E92307248D79B91A1C1EA75BEF458573D02 (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_RegisterInternal_m75ADFE89F806D7CCDA39F4BEB6E13F98789EC3A4 (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_UnregisterInternal_m52F310CFE97694C751D5B1FEC47D42C544CB221B (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_Initialize_m2DF230DCAD67005FD0517D836520E73944A6CF71 (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_IsConnected_m6C83428E73F46078BA1407A17A69702BC8F34956 (void);
extern void PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_DisconnectAll_m6143241C82774D473C8979A05BE0CE3ADAF37F98 (void);
extern void PlayerConnectionInternal_IsConnected_mB791A845711EE6CEB671A7E103E5A04FD4F48398 (void);
extern void PlayerConnectionInternal_Initialize_mCE19DC91D92DF51D2A41F389BDD32E959AC1718A (void);
extern void PlayerConnectionInternal_RegisterInternal_m32D34DB4DDFEDEC14B79F692591E8C606F2F45C2 (void);
extern void PlayerConnectionInternal_UnregisterInternal_m1289478D06B17D3EE7E0CF45DE0B93AD97D2BBD2 (void);
extern void PlayerConnectionInternal_SendMessage_m7CD63211EED2FBC267D2FA47CBA8743CCB202620 (void);
extern void PlayerConnectionInternal_TrySendMessage_m1181E4B71EB5AC3C5B885C875E3A59378E6E75D6 (void);
extern void PlayerConnectionInternal_PollInternal_m8EF1E92D3DA699EB3BBB509820CB8B35EB3D2940 (void);
extern void PlayerConnectionInternal_DisconnectAll_m48CA9F5E0FAA2FAB7A35838049289E063A78D6D8 (void);
extern void PlayerConnectionInternal__ctor_m522FC54B8DAAD52F95A07B1CA9E5AF0E36B13F45 (void);
extern void PropertyAttribute__ctor_m19247686E165101F140615C7306DC2DA3953D97D (void);
extern void TooltipAttribute__ctor_m9DA2380A6739B9D40E142C81E691BEBC7A79116F (void);
extern void SpaceAttribute__ctor_mD1981FDED1C938017DBD5557E292408BC8F2618E (void);
extern void SpaceAttribute__ctor_mAA6E6B2811F7585F504FB0C06D52ABF82F560290 (void);
extern void RangeAttribute__ctor_mFB67BD2663AD5B0D79F5E2D7E494FA8FCB21C6FF (void);
extern void MinAttribute__ctor_mE569E9A31ED222B7128543394AAF44DA09839262 (void);
extern void MultilineAttribute__ctor_m5BAEA5EB5EB078477AC5007100DE70EC06F95BBE (void);
extern void TextAreaAttribute__ctor_m5FEE25A73C001A99BC7A86895A0B88CF64FD6FA4 (void);
extern void PropertyNameUtils_PropertyNameFromString_m41A281EA1DD3AE83448598B2FCFAD0ADAB648675 (void);
extern void PropertyNameUtils_PropertyNameFromString_Injected_m616D5991D47A904E8098332548230DF6CF7CB37B (void);
extern void PropertyName__ctor_mFA341118B63F88B69464A6F1DF248B569686D778 (void);
extern void PropertyName__ctor_m9F28B1DA9CEDDEAA6663C795456B9F5E95CAE92F (void);
extern void PropertyName_IsNullOrEmpty_m80390EB235EF6A983214067BF86BCD6DBA2D1AEB (void);
extern void PropertyName_op_Equality_m86CFB3121BF5927D1D4D425A8272980CAAB73DAC (void);
extern void PropertyName_GetHashCode_m7D4B878BA78625CDD78C9B045B5EADFDBF94C378 (void);
extern void PropertyName_Equals_mFD87005C352B7BEB4279560B1A489409B0692143 (void);
extern void PropertyName_Equals_m7D00F3B148E65210A82101C31F1F217C9527113E (void);
extern void PropertyName_op_Implicit_m45CBB51D96B85B596D3094636A335CD475E10F4F (void);
extern void PropertyName_ToString_mDE271855F7B9A516185A66E12D90FE6B6C9DF6C0 (void);
extern void ResourcesAPIInternal_FindShaderByName_mAD5ED1470F0DE9CE9A95145187281879AE61B56D (void);
extern void ResourcesAPIInternal_Load_m80AAD6198709E0D3799797CA6D1F3995B1CA2EC2 (void);
extern void ResourcesAPI_get_ActiveAPI_mF850D558242EC69D97A42BCAEC2270D71F3250A4 (void);
extern void ResourcesAPI_get_overrideAPI_m2D010C9668878DA21ED92CADE587528848BB98E9 (void);
extern void ResourcesAPI__ctor_m27A1A0B35DF74437840934A70F47DE1BAEAFECA0 (void);
extern void ResourcesAPI_FindShaderByName_m9A6287AA24EC06DBF3B2630618015B5CB6F01315 (void);
extern void ResourcesAPI_Load_m54EE8C7AFD4B386B751AFC8C161B62CB2C983844 (void);
extern void ResourcesAPI__cctor_m29BD424C62AF8562DC203416CCD86EFDD8F588F4 (void);
extern void Resources_Load_m6CD8FBBCCFFF22179FA0E7B1806B888103008D33 (void);
extern void Resources_GetBuiltinResource_mD67A5C47BA93B04E0A9398611A735EA1803CAE5B (void);
extern void AsyncInstantiateOperationHelper_SetAsyncInstantiateOperationResult_mDF02114A7EA0900435DB628E06A2C0D740CC1D7B (void);
extern void AsyncOperation_InvokeCompletionEvent_m477EBEDE3FE8992BDC1DBFE02A313193CDA46DD4 (void);
extern void AttributeHelperEngine_GetParentTypeDisallowingMultipleInclusion_m699E7A570CBD00B81273CDFB539E1437D8A0E7DC (void);
extern void AttributeHelperEngine_GetRequiredComponents_m40EADB1A403020D292A62108D295B67111F10738 (void);
extern void AttributeHelperEngine_GetExecuteMode_m6863AD945E6A41B55DA50DB77C86773B088F86FF (void);
extern void AttributeHelperEngine_CheckIsEditorScript_m096C287F38FC9802504C6FBE079A28667358FF37 (void);
extern void AttributeHelperEngine_GetDefaultExecutionOrderFor_mF88FB3280A2C5AF1FE51080021A616C3437CE053 (void);
extern void AttributeHelperEngine__cctor_m24142033410C104D41A37E17A88149062046CA81 (void);
extern void DisallowMultipleComponent__ctor_mCED73439170619124E9FE0303137D0A7130C03B2 (void);
extern void RequireComponent__ctor_mB1C4FD7EA20C0ADA84C7956B948A96856B2465A9 (void);
extern void AddComponentMenu__ctor_m0C9845C59ED5CB4BEDB86EBD14EC574E13240C1B (void);
extern void AddComponentMenu__ctor_mFC3F5A41F4F41587531E9352DEC243EE49A01C25 (void);
extern void ExecuteInEditMode__ctor_mAA44FC0120898DDA33D063EF3692B7F0F22AD792 (void);
extern void ExecuteAlways__ctor_m2792EFBEBCECA35F3C1EB12B3BE9290B734C4A46 (void);
extern void HideInInspector__ctor_m6F39BCE00C935AB46163661E9D4B0B6A6B7976DE (void);
extern void HelpURLAttribute__ctor_m4671D9179DCF032E9F769A70A1665B1B60B233A7 (void);
extern void DefaultExecutionOrder__ctor_mD02339C868E98633AB5836930A963A506CCC9D1D (void);
extern void DefaultExecutionOrder_get_order_m362E5F2AB40AAA5154301F88DE93B80F865A31AF (void);
extern void ExcludeFromPresetAttribute__ctor_m6BBE5C9A0F32E56C97AE0A9A6FB66CC7DDC8C93A (void);
extern void Behaviour_get_enabled_mAAC9F15E9EBF552217A5AE2681589CC0BFA300C1 (void);
extern void Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A (void);
extern void Behaviour_get_isActiveAndEnabled_mEB4ECCE9761A7016BC619557CEFEA1A30D3BF28A (void);
extern void Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8 (void);
extern void ClassLibraryInitializer_Init_m0B7E37DD365582370981A7697AB7273F7A20FF0A (void);
extern void ClassLibraryInitializer_InitStdErrWithHandle_m88C6AA53D2E42443B5456B72FC78F09E13DC2504 (void);
extern void ClassLibraryInitializer_InitAssemblyRedirections_m5BA3935ED238E65FDACBE96E908377F37545CF54 (void);
extern void U3CU3Ec__cctor_m90743B80C6B01F70F5DD18094ED9917A4D66D75C (void);
extern void U3CU3Ec__ctor_mAAF6A78995DFBFBEADE41ECEAFC4482A3354A1F8 (void);
extern void U3CU3Ec_U3CInitAssemblyRedirectionsU3Eb__2_0_mED19C4287A4E13D96147812233377C55661C83EB (void);
extern void AssemblyVersion_op_Equality_m812148ACA50C9FD561C823DF1F6116A73711762F (void);
extern void AssemblyVersion_ToString_mEB86A8E88D80F7C7459258EDF519B208400FA74B (void);
extern void AssemblyVersion_Equals_m9FA3C12B7C0743C563E712CD08672EF9E99E1ADD (void);
extern void AssemblyVersion_GetHashCode_mC191CC77C4664CD9A3F59E18BEE76E6BB5DA336F (void);
extern void AssemblyFullName_Equals_mCCD71330EA2201DC5C3452AEEFF051AF203D0E9E (void);
extern void AssemblyFullName_GetHashCode_m470DD9F4F78B21172D3A438C139ABF5587B0C4D0 (void);
extern void AssemblyFullName_ToString_m4D31288544EE855F460B3833AB0D51DF0AEADCEF (void);
extern void Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371 (void);
extern void Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B (void);
extern void Component_GetComponent_m4352437B839B9601EB007CAC1007C8E1FB8A427D (void);
extern void Component_TryGetComponent_mC2472304301C1523E25A5FACD0BCD1CC471B7BD0 (void);
extern void Component_GetComponentInChildren_m4050899166730E8F6629B895597CF4ECC894B597 (void);
extern void Component_GetComponentInParent_mA402B7F87D23E039BB045630E5EA3AC020D8A7C2 (void);
extern void Component_GetComponentsForListInternal_m7B3A61AF88E5E6F845817EC28E22FD39944EFBC7 (void);
extern void Component_GetComponents_mFD04B9CB0BD37505417054D614436B5844DC4365 (void);
extern void Component__ctor_m4319162A6E6B02301078C1233F6E7F4A3E735486 (void);
extern void Coroutine__ctor_m65324E0C6062163C0378CD4190E903B4C2ED75DD (void);
extern void Coroutine_Finalize_m83673D20AB464E4C866408F798D5CA1F0391808D (void);
extern void Coroutine_ReleaseCoroutine_mEEFBA6D7CABF4E6FAF7C115B53BF530B30208869 (void);
extern void SetupCoroutine_InvokeMoveNext_m72FC77384CAC3133B6EE650E0581D055B34B2F5F (void);
extern void SetupCoroutine_InvokeMember_m988CC4721CD62977A75098C58C52A292457E9111 (void);
extern void CustomYieldInstruction_get_Current_m6F1287B6C4A16C875913A556D66ACB1BF599E6AF (void);
extern void CustomYieldInstruction_MoveNext_m875CC6944F46765EB3BE14306E83E7C54CBEB2CF (void);
extern void CustomYieldInstruction_Reset_mA357794CF2260A3E48748612BDE01CB0B7CF8A8D (void);
extern void CustomYieldInstruction__ctor_mB64531EC09E871EF60BFAC16918A774C977C7B50 (void);
extern void EnumDataUtility_GetCachedEnumData_m5A1FBC1D3D748CB99C3B76EDA6FD89E73777EBF6 (void);
extern void EnumDataUtility_HandleInspectorOrderAttribute_mB4880BEC4F5D38D3CDF931A62D0B2312B2B3989D (void);
extern void EnumDataUtility_CheckObsoleteAddition_mDA292A1BB417654B3A42A3ABAC80D5A0889AB4F3 (void);
extern void EnumDataUtility_EnumTooltipFromEnumField_m9A7A43EF79E0D58F5A5B12E85D4D7FE323954896 (void);
extern void EnumDataUtility_EnumNameFromEnumField_m530D557811A64AECFB98038D8FC7762F7F825CF2 (void);
extern void EnumDataUtility__cctor_mAE8CBD4D762193E934C4FEA724E2B21CCB632577 (void);
extern void EnumDataUtility_U3CEnumNameFromEnumFieldU3Eg__NicifyNameU7C8_0_m06FD95EB39F7F5EEC3EF1B71D7AFBA2AD8F26743 (void);
extern void U3CU3Ec__cctor_mAA1051B42F26135CC959F5EC7890436FAB2ED941 (void);
extern void U3CU3Ec__ctor_m0DFD48E0843CFE7BBD1816E7FB17F1A92545B9F6 (void);
extern void U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_5_m22CC7BA755DF7C90E69EBAE15CCD2B6F2357D768 (void);
extern void U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_1_mFABC2F08601EDC5AB10DC6CBCD6870A533C78DF9 (void);
extern void U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_2_m8D831A7EFA8414C67A47371867AA72BE854E81D5 (void);
extern void U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_3_mFF89FAEBB1E42BD08E9A554D3BD8B5E8D0B58B5C (void);
extern void U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_4_mB79B52279F2BE5BE38B132960B29AA87EACE5A2F (void);
extern void U3CU3Ec__DisplayClass2_0__ctor_mD6D5EFE439B60B998596D46FEC76C110A2B81DDB (void);
extern void U3CU3Ec__DisplayClass2_0_U3CGetCachedEnumDataU3Eb__0_m6FA97FBACA28AFB3C0353743AC9F747610619CAD (void);
extern void ExcludeFromObjectFactoryAttribute__ctor_m547CE65CC098EB4466C456476ECF2D78E2834FBB (void);
extern void ExtensionOfNativeClassAttribute__ctor_m64B864DD565CE1BF04DA861E6759163A1727F6A1 (void);
extern void GameObject_GetComponent_m99E12753EA84947521DC711CA33F452B5E65B474 (void);
extern void GameObject_GetComponentInChildren_m4A3692D1D93C726D5B02E588130C782A336961D5 (void);
extern void GameObject_GetComponentInParent_m80F84FC4D405C1F9987C0E77385749814AD0027C (void);
extern void GameObject_GetComponentsInternal_m5D5FD903F9CB151AC9782E5840D397F422A82F95 (void);
extern void GameObject_TryGetComponent_mC89200887E029C940C0E16EDC0BC7DE0CCDD0CF2 (void);
extern void GameObject_TryGetComponentInternal_mF4570971BBC0A69178D89A298F67C1D60E3881E5 (void);
extern void GameObject_Internal_AddComponentWithType_m2D986297A2133DD6896D9715F22C9627991D07FF (void);
extern void GameObject_AddComponent_mDF246771EC34613FA6AF0C98D443368FB43E9F36 (void);
extern void GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56 (void);
extern void GameObject_get_layer_m108902B9C89E9F837CE06B9942AA42307450FEAF (void);
extern void GameObject_set_layer_m6E1AF478A2CC86BD222B96317BEB78B7D89B18D0 (void);
extern void GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92 (void);
extern void GameObject_get_activeSelf_m4F3E5240E138B66AAA080EA30759A3D0517DA368 (void);
extern void GameObject_get_activeInHierarchy_m49250F4F168DCC5388D5BE4F6A5681386907B109 (void);
extern void GameObject_SendMessage_m452B3418BE240EC79550C15E1F2EAE61488F06DF (void);
extern void GameObject__ctor_m37D512B05D292F954792225E6C6EEE95293A9B88 (void);
extern void GameObject__ctor_m7D0340DE160786E6EFA8DABD39EC3B694DA30AAD (void);
extern void GameObject__ctor_m721D643351E55308EA4F5F41B67D5446D11C61F0 (void);
extern void GameObject_Internal_CreateGameObject_mA49D858513048B4CF239D4201C89903F4ECE8498 (void);
extern void InspectorOrderAttribute_get_m_inspectorSort_mB97F50A2118A606A94869ADFB548F8C747EDD14D (void);
extern void InspectorOrderAttribute_get_m_sortDirection_mC0FE63CA21C2158B7FD0C09659418A69F50D1313 (void);
extern void LayerMask_op_Implicit_m7F5A5B9D079281AC445ED39DEE1FCFA9D795810D (void);
extern void LayerMask_op_Implicit_m01C8996A2CB2085328B9C33539C43139660D8222 (void);
extern void ManagedStreamHelpers_ValidateLoadFromStream_mF6E14BAD1BC52711F99076381E5A57DA650B4C1A (void);
extern void ManagedStreamHelpers_ManagedStreamRead_mB44F01233FDB11E124CC64899912608FB5ECEEE5 (void);
extern void ManagedStreamHelpers_ManagedStreamSeek_m97EDF5D08F83234C056003A9890C2C3478EECC4B (void);
extern void ManagedStreamHelpers_ManagedStreamLength_mF75367CF96464E01EDF84070FF038125649AEDFD (void);
extern void MonoBehaviour_get_destroyCancellationToken_mBA72081B6235F4E1D10E9BA6D60005DD48730A10 (void);
extern void MonoBehaviour_RaiseCancellation_mFCD352361B8F5B9C9BA028967C2E49A22034712F (void);
extern void MonoBehaviour_IsInvoking_mF7CF0A2ABF31B61FC67A75E9210C16683E5020A0 (void);
extern void MonoBehaviour_CancelInvoke_m177BCBDFCEA3E09C02E3E444BF4FBA648FAE3CFA (void);
extern void MonoBehaviour_Invoke_mF724350C59362B0F1BFE26383209A274A29A63FB (void);
extern void MonoBehaviour_InvokeRepeating_mF208501E0E4918F9168BBBA5FC50D8F80D01514D (void);
extern void MonoBehaviour_CancelInvoke_m268FFD58AFF64C07FD4C9B9B8B85F58BD86F3A01 (void);
extern void MonoBehaviour_IsInvoking_m9CD08C2F7F5E83660FFE3B5A373B202CCBDB3708 (void);
extern void MonoBehaviour_StartCoroutine_m10C4B693B96175C42B0FD00911E072701C220DB4 (void);
extern void MonoBehaviour_StartCoroutine_mD754B72714F15210DDA429A096D853852FF437AB (void);
extern void MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812 (void);
extern void MonoBehaviour_StartCoroutine_Auto_m97F469F18612A2208D2EFDB3274DF0B4E3C9F4E6 (void);
extern void MonoBehaviour_StopCoroutine_mF9E93B82091E804595BE13AA29F9AB7517F7E04A (void);
extern void MonoBehaviour_StopCoroutine_mB0FC91BE84203BD8E360B3FBAE5B958B4C5ED22A (void);
extern void MonoBehaviour_StopCoroutine_m1DA0B9343DCDB53221A6CD707CBF0827A6FFF17F (void);
extern void MonoBehaviour_StopAllCoroutines_m872033451D42013A99867D09337490017E9ED318 (void);
extern void MonoBehaviour_get_useGUILayout_mBCD040C678BF8521BFBAB8FD59BC566B1F5BED89 (void);
extern void MonoBehaviour_set_useGUILayout_m56F0C62F4B6889D7472074ECCB56EBA462285134 (void);
extern void MonoBehaviour_print_m9E6FF71C673B651F35DD418C293CFC50C46803B6 (void);
extern void MonoBehaviour_Internal_CancelInvokeAll_mCBF8624858F884C064BC83740A0EF97A59461E83 (void);
extern void MonoBehaviour_Internal_IsInvokingAll_mCA9B8812ED7656429C2298F272AC1F654C40C3DA (void);
extern void MonoBehaviour_InvokeDelayed_mBD74A4D793E6836BEC5A0FABDA7FD573A87ACD4C (void);
extern void MonoBehaviour_CancelInvoke_m794BA6F7C1040DDA0D62D37AF97883E23627DC12 (void);
extern void MonoBehaviour_IsInvoking_m05A8A914FA3563CF28F1016095BA3A735F319A99 (void);
extern void MonoBehaviour_IsObjectMonoBehaviour_mC2F75720102B56F81F3D1329BE96C2C7B336B615 (void);
extern void MonoBehaviour_StartCoroutineManaged_m014E764B40594337F2B5AA03BFFC87DD8D4B063B (void);
extern void MonoBehaviour_StartCoroutineManaged2_m55C19C5C5C65E9883E12101A46F37AB1172C73E8 (void);
extern void MonoBehaviour_StopCoroutineManaged_m35C1C524554F9B058538E41E0650FA71373F292D (void);
extern void MonoBehaviour_StopCoroutineFromEnumeratorManaged_m81B57000F7ACB16B333800D66E8C74E7481E20B8 (void);
extern void MonoBehaviour_GetScriptClassName_m428B33342B759D78A638B6E383F0510F294DE285 (void);
extern void MonoBehaviour_OnCancellationTokenCreated_m9D65DA2F7558F7B428D28713891BA05116719179 (void);
extern void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (void);
extern void NoAllocHelpers_SafeLength_m4EC7245A2215693CE163E3F6ECFB105D742087DF (void);
extern void NoAllocHelpers_Internal_ResizeList_mC468D22C6FEE802726262F9B7CFC3DC23A5DD40C (void);
extern void NoAllocHelpers_ExtractArrayFromList_m3207C25A27FDFB2B9A96D4BB34ECB7C26AAAED24 (void);
extern void RangeInt_get_end_m5835FBEB410CB8AC0928AEFD95728A9AD57F6C63 (void);
extern void RangeInt__ctor_m3CB91E79C7B5AED97E564581025B2F66778B7CBE (void);
extern void RuntimeInitializeOnLoadMethodAttribute__ctor_mA563B1C8896C6490F88C060E13950DB600577352 (void);
extern void RuntimeInitializeOnLoadMethodAttribute_set_loadType_mCB29F9D9D69AB012FB797AB433AD3CFCE696AF10 (void);
extern void ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF (void);
extern void ScriptableObject_CreateInstance_mE015043D7EC19654FDFB830A9393B3914FF5FC24 (void);
extern void ScriptableObject_CreateScriptableObject_m63730D18415FC4EC61F77C57A4D184064E82D2C0 (void);
extern void ScriptableObject_CreateScriptableObjectInstanceFromType_mDA90002F7BB98734237184C1E160C7763B5A0AD6 (void);
extern void ScriptingRuntime_GetAllUserAssemblies_mA90D59F0C8D15BA303AD3EA52F992DFB3BE1C7F2 (void);
extern void ScriptingUtility_IsManagedCodeWorking_m023FD7C1DFD42CA5AA1F40219DDED8033F0BC3B6 (void);
extern void SelectionBaseAttribute__ctor_m6B586E58B9F8CF4D6ADF3802179F67174301F063 (void);
extern void StackTraceUtility_SetProjectFolder_m60DBA844239D34388650DB96CE47ED18835EE486 (void);
extern void StackTraceUtility_ExtractStackTrace_mFDB05BC4CA207364FFC93F44E29F35A271DF5200 (void);
extern void StackTraceUtility_ExtractStringFromExceptionInternal_m57F173812EF87153715A42DCE44D2F069F528FF3 (void);
extern void StackTraceUtility_ExtractFormattedStackTrace_m3857524451432DB6A0A5445E57B3DFC30369B0BB (void);
extern void StackTraceUtility__cctor_mA6A35A8164A7DB8BE1BF989E02D5F0B2067ACAFB (void);
extern void UnityException__ctor_m2FFB2690639A7421DA5DBFC40091AB23D87EA90F (void);
extern void UnityException__ctor_mF8A65C9C71A1E0DE6A3224467040765901959312 (void);
extern void MissingReferenceException__ctor_m9A8445A472E11453B6C6C93319A314179C363E5A (void);
extern void MissingReferenceException__ctor_m38AAD807B4CA850C73C5CAEC30ACE86628D94291 (void);
extern void TextAsset_get_bytes_m244B31755642C9623B570FC96B9A04523B1E5178 (void);
extern void TextAsset_Internal_CreateInstance_m9EB31EDB11B4AC96E3CBF5EC3E8FE018D71EE0AD (void);
extern void TextAsset_get_text_m36846042E3CF3D9DD337BF3F8B2B1902D10C8FD9 (void);
extern void TextAsset_ToString_m6A506652E11BF9679E6BE931EA2844035FF68923 (void);
extern void TextAsset__ctor_m27C97A718A563E124A07FAE057A69D4D44A93595 (void);
extern void TextAsset__ctor_mC8EEBC230157B4C459E87D591C722D8803FB5846 (void);
extern void TextAsset_DecodeString_m3345EAF0DE3FE4AAF99F105269E0B83C528198D9 (void);
extern void EncodingUtility__cctor_m15A59251290A2D2B0CD00F635C7C30F3B033A40B (void);
extern void UnhandledExceptionHandler_RegisterUECatcher_mDBA57543CC57B4265B7847FE9F4E51125FE5F61F (void);
extern void U3CU3Ec__cctor_m98D2C4143206DF565508D9D1A8FB0C38851DA96A (void);
extern void U3CU3Ec__ctor_mFC700DE43CA4803F7317D49C21D6010E21E4C40D (void);
extern void U3CU3Ec_U3CRegisterUECatcherU3Eb__0_0_mD21DF44CEC3DA35516DA5FC75CA8A2FF67E4E382 (void);
extern void Object_GetInstanceID_m554FF4073C9465F3835574CC084E68AAEEC6CC6A (void);
extern void Object_GetHashCode_m3FA03DBF8CFF6584BCD22BCFDD257AED8DEB5872 (void);
extern void Object_Equals_m086D5CEE892DA62DEE463ACFBA493174C56EDAD0 (void);
extern void Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A (void);
extern void Object_CompareBaseObjects_m0686B5F55766F49B84E0AE0D4F70DFB9BEA3861C (void);
extern void Object_IsNativeObjectAlive_m477E072492E19666F582F31A101C991BA107014F (void);
extern void Object_GetCachedPtr_m3B66BC474B4F735F68184F00248385BAF9AF650B (void);
extern void Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392 (void);
extern void Object_set_name_mC79E6DC8FFD72479C90F0C4CC7F42A0FEAF5AE47 (void);
extern void Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436 (void);
extern void Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB (void);
extern void Object_DestroyImmediate_m737E5829FEEAE70EE7A004D172042D52E336E1E3 (void);
extern void Object_DestroyImmediate_m6336EBC83591A5DB64EC70C92132824C6E258705 (void);
extern void Object_get_hideFlags_mA08F5E41671B8C6B5073C6B9E2799BCE6E0DF7F3 (void);
extern void Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4 (void);
extern void Object_CheckNullArgument_m4D03BBBD975CCCCB3F9438864E3E8BF54E1E3F26 (void);
extern void Object_ToString_m590B13E2C40DB814E3CF9C3615952B3CC7C4B36C (void);
extern void Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (void);
extern void Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (void);
extern void Object_GetOffsetOfInstanceIDInCPlusPlusObject_m45636617A1C7BC9D5D3064F8FBF18785126F22F8 (void);
extern void Object_Internal_CloneSingle_m24ECA1416702930DF5C316EA8B70D575315B636A (void);
extern void Object_ToString_mC776F339F5986CCB1F1D7901F335B9A61BDC0C9B (void);
extern void Object_GetName_m514CD5F3EA9F7BC4DFD914C98D4AA91C45DB3AD9 (void);
extern void Object_SetName_m284935BE1D41FFDFDEC73986CB228B1CCAED7EC5 (void);
extern void Object_FindObjectFromInstanceID_m977F314530A838CAB5497C8F5D0D8DA134B92E0C (void);
extern void Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6 (void);
extern void Object__cctor_mED93981CFB29C99D893E0C7ADB4B5BCB2A6C7903 (void);
extern void UnitySynchronizationContext__ctor_m4BA1C7C04C4B82783FDC935C2C50639211B11F5C (void);
extern void UnitySynchronizationContext__ctor_m3CC3D812A97540AB584CECA78B735D68FF30E4F0 (void);
extern void UnitySynchronizationContext_Send_mB69AAB0638FC77BD51BFA5BF7D2B0D568BBEB7F4 (void);
extern void UnitySynchronizationContext_Post_mD36839040EBAB66116699E68558BB8FDFF0FD834 (void);
extern void UnitySynchronizationContext_CreateCopy_m11E8C66F575BC1DF3F34A614E6B00D040745301A (void);
extern void UnitySynchronizationContext_Exec_m7E6352CBA70E4AB14F7D50A919C3F8C22F2E977A (void);
extern void UnitySynchronizationContext_HasPendingTasks_m5B62CCDDC05BCC946CB158FECC5FA6422DE1C960 (void);
extern void UnitySynchronizationContext_InitializeSynchronizationContext_mF6CADD1D15642024F450D918CE1DEA1A4128818A (void);
extern void UnitySynchronizationContext_ExecuteTasks_m3034D59292339809C6D7FA5932ACC365892B8CA1 (void);
extern void UnitySynchronizationContext_ExecutePendingTasks_m30855D2E24B36B23076FDDF03D6DE93F0433E4F9 (void);
extern void WorkRequest__ctor_m78DC33ED88BF9BA29E05D2180B2ADC439132F1F5 (void);
extern void WorkRequest_Invoke_mBFEC6A3799BAFDE7ED840A0523D3D56160C03921 (void);
extern void WaitForEndOfFrame__ctor_m4AF7E576C01E6B04443BB898B1AE5D645F7D45AB (void);
extern void WaitForSecondsRealtime_get_waitTime_m385602BA6C66A6169695FE9AD78F2B24F4FF8573 (void);
extern void WaitForSecondsRealtime_set_waitTime_m8317E2B7A2B2DC4E4A1B0CD0F9D4479294B51FB5 (void);
extern void WaitForSecondsRealtime_get_keepWaiting_m175C5DD180DFA38A323FA6C4C8EB058DBFC0A7CA (void);
extern void WaitForSecondsRealtime__ctor_mBFC1E4F0E042D5EC6E7EEB211A2FE5193A8F6D6F (void);
extern void WaitForSecondsRealtime_Reset_m44618BC7268A087CA316629EDF87282D37B6EAA4 (void);
extern void YieldInstruction__ctor_m23280B9DFE9C3E80554A656B4E7125BC9B2C027B (void);
extern void SerializeField__ctor_mD3D7B72B71C1F3F70060E2830710F48F965C753E (void);
extern void ComputeShader_FindKernel_m3BA5C50794FA6AF4C432E55FBBD7CB266532F659 (void);
extern void LowerResBlitTexture_LowerResBlitTextureDontStripMe_mEECB7042CAE059B760ABFEC12902030249248A8F (void);
extern void PreloadData_PreloadDataDontStripMe_mB4D9C8C36672A400FBE7E024C47E0FD152BABA02 (void);
extern void SystemInfo_get_operatingSystemFamily_mC8B13A7D2F34699EDDA4F7629F117C76F0C865FA (void);
extern void SystemInfo_IsValidEnumValue_mF380A352BF2023F7D9B278873ECFCD56A4EE2F32 (void);
extern void SystemInfo_SupportsTextureFormat_m833B0ABED13B5B8D0D4BCF082F3EFA51A3B5C860 (void);
extern void SystemInfo_get_maxTextureSize_mEE557C09643222591C6F4D3F561D7A60CD403991 (void);
extern void SystemInfo_get_maxRenderTextureSize_mD9AB6274BEAC0CDCF9AF26B3DC19CD57E548A6FE (void);
extern void SystemInfo_GetOperatingSystemFamily_mE40096D3196E10CC8940F79B729CA985D1CD4396 (void);
extern void SystemInfo_SupportsTextureFormatNative_m47D5DEAD487F9046B4F77B8E9D880D97DD59ACEB (void);
extern void SystemInfo_GetMaxTextureSize_m69CD656F42B72D3E49A923F174C701712B9C94EC (void);
extern void SystemInfo_GetMaxRenderTextureSize_mFDD81060C8E82A4D1F567BE54DB81EC3CC529115 (void);
extern void SystemInfo_IsFormatSupported_m412D2A8B391BDBCD1EDB5C17ADAB724CDB123499 (void);
extern void SystemInfo_GetCompatibleFormat_m3A1DEC64F2C85F1D7C45005009D93EFA33C8419B (void);
extern void SystemInfo_GetGraphicsFormat_mF4A09D38BA91B8F783C9189B5D744BA943292E0E (void);
extern void Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865 (void);
extern void Time_get_unscaledTime_mAF4040B858903E1325D1C65B8BF1AC61460B2503 (void);
extern void Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5 (void);
extern void Time_get_realtimeSinceStartup_m73B3CB73175D79A44333D59BB70F9EDE55EC9510 (void);
extern void TouchScreenKeyboard_Internal_Destroy_m83A7EDD06362B64AF552170085E3598D31625F70 (void);
extern void TouchScreenKeyboard_Destroy_m8B3C649CE02C77170A0F41D8EEF6E7465F8B9CAD (void);
extern void TouchScreenKeyboard_Finalize_mCAFC1635D50B4AF9EEBFB0A5DC1D5027A2FEAA6B (void);
extern void TouchScreenKeyboard__ctor_mA0C6FA07182A13B8F3F62731B94CAAD83F340861 (void);
extern void TouchScreenKeyboard_TouchScreenKeyboard_InternalConstructorHelper_m14B4B7D80E368896C0CC49EA76D2B3208DC7D0F1 (void);
extern void TouchScreenKeyboard_get_isSupported_mCFAC95CA6CAA06B4E21F42E3C40A39668D6B438E (void);
extern void TouchScreenKeyboard_get_disableInPlaceEditing_m063C253E629D35E9C1DEC911EA785AA796D35252 (void);
extern void TouchScreenKeyboard_get_isInPlaceEditingAllowed_mE57CDF32437DD1DCDC665072457C91DD31EB93D0 (void);
extern void TouchScreenKeyboard_IsInPlaceEditingAllowed_m457215121F1DEBEEE4D9665358C13F7E3D041AE7 (void);
extern void TouchScreenKeyboard_Open_m2E1E3A71919EE30C51539DF8A037B74BE2D78065 (void);
extern void TouchScreenKeyboard_Open_mE24AE590A477F9D027537FD170B1DC02627C4D7E (void);
extern void TouchScreenKeyboard_get_text_m74593E81B017446204A9DB1F7359922A2C005B1E (void);
extern void TouchScreenKeyboard_set_text_m0A8AA05F4D9D27E8764955F0041452145B6C6FBB (void);
extern void TouchScreenKeyboard_set_hideInput_m8FDDB21FB7E9B06B791649BBE369E4EA0F5F5299 (void);
extern void TouchScreenKeyboard_get_active_mB22402FB9E56D3F652DA785F01E504A530FF8775 (void);
extern void TouchScreenKeyboard_set_active_m4382D57F87E4C86B59864E86BE93A8A2A474B7C2 (void);
extern void TouchScreenKeyboard_get_status_mCC466FDEC7E1913D8585ABA7F048FC198CB19504 (void);
extern void TouchScreenKeyboard_set_characterLimit_mCD8F3BC047EF2515272A689368CF3678A419B854 (void);
extern void TouchScreenKeyboard_get_canGetSelection_m340ACEFDB9609DEED4FE7D451A4DCCC1024F767A (void);
extern void TouchScreenKeyboard_get_canSetSelection_m6CD6C069A9FEF91CC8014B877EB057ECF598EDF9 (void);
extern void TouchScreenKeyboard_get_selection_m1D44C9A8D4EA91F61706F048ED318E537DC46AB2 (void);
extern void TouchScreenKeyboard_set_selection_mC27C2948118086822A151118C379FAAF692DB2DF (void);
extern void TouchScreenKeyboard_GetSelection_mFE603E50B5D189F96A3E3D93FA5F70E7B2DA9C5A (void);
extern void TouchScreenKeyboard_SetSelection_m589290680988DFA5FBABA7336F58907C62D182DA (void);
extern void UINumericFieldsUtils_TryConvertStringToDouble_m2F7B52D176293C6C7EE5406D14F92E328F8D7A64 (void);
extern void UINumericFieldsUtils_TryConvertStringToDouble_m68C49071E5A3ED34D2320458D386C6668AA6AA49 (void);
extern void UINumericFieldsUtils_TryConvertStringToFloat_m9D49DF90FF6724632ADF8EC96A096D094B2FCBBD (void);
extern void UINumericFieldsUtils_TryConvertStringToLong_m9052E90D297DDA0EC1D836B5068D1C6247E8B179 (void);
extern void UINumericFieldsUtils_TryConvertStringToLong_m3BBC27998E58F710ED5451565A03191D4F137E91 (void);
extern void UINumericFieldsUtils_TryConvertStringToULong_m1F2F0AFF1E15B2D72AB71B2A9148C969D959DBFC (void);
extern void UINumericFieldsUtils_TryConvertStringToULong_mCB90A43CDC68458BE764A5766EFC0C576C13A850 (void);
extern void UINumericFieldsUtils_TryConvertStringToInt_m8E5245B57793548361F48C6674E35E84B1A272F3 (void);
extern void UINumericFieldsUtils_TryConvertStringToUInt_m08EE01CCF8950657039C54A46C3F9215E1C65EF2 (void);
extern void UINumericFieldsUtils__cctor_m97714D6085F635BF543C6CDCB026862FA2B19BF4 (void);
extern void DrivenRectTransformTracker_Add_mC0CE417831BF58E6DA81770CE5E2A99B142EEFEC (void);
extern void DrivenRectTransformTracker_Clear_m9A7F5130E4007F70B14AB1FF13A2997C073A64EE (void);
extern void RectTransform_add_reapplyDrivenProperties_m92F5BD1B2500C408447F882F378CB52647AB6E72 (void);
extern void RectTransform_remove_reapplyDrivenProperties_m265B69E542E3AABF284ED76EDF974C3A83B2D242 (void);
extern void RectTransform_get_rect_mC82A60F8C3805ED9833508CCC233689641207488 (void);
extern void RectTransform_get_anchorMin_mD85363930BE38EC188F933B9F4D58320CAB72F03 (void);
extern void RectTransform_set_anchorMin_m931442ABE3368D6D4309F43DF1D64AB64B0F52E3 (void);
extern void RectTransform_get_anchorMax_mEF870BE2A134CEB9C2326930A71D3961271297DB (void);
extern void RectTransform_set_anchorMax_m52829ABEDD229ABD3DA20BCA676FA1DCA4A39B7D (void);
extern void RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680 (void);
extern void RectTransform_set_anchoredPosition_mF903ACE04F6959B1CD67E2B94FABC0263068F965 (void);
extern void RectTransform_get_sizeDelta_m822A8493F2035677384F1540A2E9E5ACE63010BB (void);
extern void RectTransform_set_sizeDelta_mC9A980EA6036E6725EF24CEDF3EE80A9B2B50EE5 (void);
extern void RectTransform_get_pivot_mA8334AF05AA7FF09A173A2430F2BB9E85E5CBFFF (void);
extern void RectTransform_set_pivot_m79D0177D383D432A93C2615F1932B739B1C6E146 (void);
extern void RectTransform_get_offsetMin_mD02BE5256DADAF02CEEF99214C4E80478CD5287B (void);
extern void RectTransform_set_offsetMin_m07F38B4105C7CA9CC9FBDC9ED0DB008602880AB9 (void);
extern void RectTransform_get_offsetMax_m6A51C62A2C69780EFD879D3CFE4EE2CBF4AD3D73 (void);
extern void RectTransform_set_offsetMax_m5514D09D86516F2C0E25FA6D11A3A4274D3D002D (void);
extern void RectTransform_GetLocalCorners_m18B3E5ED5EB24AD46279199A134CD7F218D3DD11 (void);
extern void RectTransform_GetWorldCorners_m6E15303C3B065B2F65E0A7F0E0217695564C2E09 (void);
extern void RectTransform_SetSizeWithCurrentAnchors_m53A04549B7687A1DEA2C7484E56D89809390CE44 (void);
extern void RectTransform_SendReapplyDrivenProperties_mCBDC6A65528E92A7E7D5DDC6EB94A058050CFF8D (void);
extern void RectTransform_GetParentSize_m462044ABB7282640CCC3929A7BC3FC5609C30C42 (void);
extern void RectTransform__ctor_m9E23A4767F17F806A633E34487CF311EEBBB0542 (void);
extern void RectTransform_get_rect_Injected_m2CAD7BD1A157125337831D705F8953CB43EFCA22 (void);
extern void RectTransform_get_anchorMin_Injected_mA00B0680244E546BE0C7F715CD27BF15708F33F0 (void);
extern void RectTransform_set_anchorMin_Injected_m062070AA530E9DB918780A05DA5D1A7813594696 (void);
extern void RectTransform_get_anchorMax_Injected_m33D2FB73C038385395F18D4B75C090476D08F5E0 (void);
extern void RectTransform_set_anchorMax_Injected_mFD5947257451DAF1230D993A794DA690915443A5 (void);
extern void RectTransform_get_anchoredPosition_Injected_m101611A28C40B0E8ABE9983AE9ADC26940A2F9D7 (void);
extern void RectTransform_set_anchoredPosition_Injected_m9B719BB4C1D0E4EDE750CB93E19251DA94573F8A (void);
extern void RectTransform_get_sizeDelta_Injected_mB107881782C0DFF99C0EC61A4D423FB3C91D5405 (void);
extern void RectTransform_set_sizeDelta_Injected_m3690D9F545C7C324591F81934046370DF7F1702E (void);
extern void RectTransform_get_pivot_Injected_m1D193D87AE52D7BCF41990B13ADF1A807928289F (void);
extern void RectTransform_set_pivot_Injected_m9693DF374A51072B4804362B6E7E065BD4A5B48C (void);
extern void ReapplyDrivenProperties__ctor_mC06AEE119C82C068873EE368C7C8DBE9CAD28949 (void);
extern void ReapplyDrivenProperties_Invoke_m3440A5F41B8B52D671A1C26356CB20CF8E7AC39A (void);
extern void Transform__ctor_mB597BB13F66ADC3B8A3D45A2ABDEC8C02B421B93 (void);
extern void Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1 (void);
extern void Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95 (void);
extern void Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134 (void);
extern void Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F (void);
extern void Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C (void);
extern void Transform_get_localRotation_mD53D37611A5DAE93EC6C7BBCAC337408C5CACA77 (void);
extern void Transform_set_localRotation_mAB4A011D134BA58AB780BECC0025CA65F16185FA (void);
extern void Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F (void);
extern void Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633 (void);
extern void Transform_get_parent_m65354E28A4C94EC00EBCF03532F7B0718380791E (void);
extern void Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234 (void);
extern void Transform_get_parentInternal_mADE30238BECA9CDE15FB84E208FB5C6FDC31C14E (void);
extern void Transform_set_parentInternal_m7AC9FA19DF51534D5E83CB8B495CDC11BA17E979 (void);
extern void Transform_GetParent_m0DDE14F6AA36850AAEB05B13AB8B18CB02BA875B (void);
extern void Transform_SetParent_m6677538B60246D958DD91F931C50F969CCBB5250 (void);
extern void Transform_SetParent_m9BDD7B7476714B2D7919B10BDC22CE75C0A0A195 (void);
extern void Transform_get_worldToLocalMatrix_mB633C122A01BCE8E51B10B8B8CB95F580750B3F1 (void);
extern void Transform_get_localToWorldMatrix_m5D35188766856338DD21DE756F42277C21719E6D (void);
extern void Transform_TransformPoint_m05BFF013DB830D7BFE44A007703694AE1062EE44 (void);
extern void Transform_InverseTransformPoint_m18CD395144D9C78F30E15A5B82B6670E792DBA5D (void);
extern void Transform_get_childCount_mE9C29C702AB662CC540CA053EDE48BDAFA35B4B0 (void);
extern void Transform_SetAsFirstSibling_mBE0D0E76099F829466DC2FBD71ACFCF3C8EC03BD (void);
extern void Transform_get_lossyScale_mFF740DA4BE1489C6882CD2F3A37B7321176E5D07 (void);
extern void Transform_IsChildOf_mFE071BE1E775E825401FE0A9B9BE49E39D256CEA (void);
extern void Transform_set_hasChanged_mCE980898F6D52F81E7E6B772DCA89E13A15870AE (void);
extern void Transform_GetEnumerator_mA7E1C882ACA0C33E284711CD09971DEA3FFEF404 (void);
extern void Transform_GetChild_mE686DF0C7AAC1F7AEF356967B1C04D8B8E240EAF (void);
extern void Transform_get_position_Injected_mC69C78EAC69F2912B161B5710E69F7D3DC30C815 (void);
extern void Transform_get_localPosition_Injected_m4EA29DEBBB27D41E17E6F7469217B50B7594DF22 (void);
extern void Transform_set_localPosition_Injected_mB0AC6C9E6298454212FD390CB010ED9F5B0B075A (void);
extern void Transform_get_rotation_Injected_m6532D51417F17B804F56FC809B833F3BE524F0FC (void);
extern void Transform_get_localRotation_Injected_m1597F48A85A1F4FFDC4597B66906CC45A0F3A1A1 (void);
extern void Transform_set_localRotation_Injected_m7F19BC3A69E968FF240368F65E095082A9253B53 (void);
extern void Transform_get_localScale_Injected_m39985DA5D17EF46D4E8858AA98AF498BCC8A99D9 (void);
extern void Transform_set_localScale_Injected_m4ECD0F3FCC76D0B0FE5CE13E8B4BBB06489C44F2 (void);
extern void Transform_get_worldToLocalMatrix_Injected_mDBC9452FF28DF93AA1F7DF84AF1526F08E160963 (void);
extern void Transform_get_localToWorldMatrix_Injected_m17F83E29C6BC4F3C9891D9F7FF6F2059E09BB21D (void);
extern void Transform_TransformPoint_Injected_mC39341E3D842D1AEAF3DE2496BD9CCDE6087D65E (void);
extern void Transform_InverseTransformPoint_Injected_m6F7266D1FD453F86081B7286C0848DF8F32593A6 (void);
extern void Transform_get_lossyScale_Injected_mDCE0BE28BAC6A6A89056535B481A6F84C36E8F68 (void);
extern void Enumerator__ctor_m83A956CC6A8C1F2318C7660553BAD2D2B25AD71A (void);
extern void Enumerator_get_Current_m4732420B5A80C615B7CA373E750542E7F91D93DF (void);
extern void Enumerator_MoveNext_mCC9D1D19DCDF3F54E1FFC2798D073C01C202D984 (void);
extern void Enumerator_Reset_m91311F4F60075A13C6B8A27BC2856B37DC18A63E (void);
extern void SpriteRenderer_InvokeSpriteChanged_m105236F9C9637A421F96627823413A679319AFB8 (void);
extern void Sprite__ctor_m14DB77A0955914B9B8D8189BB1A6B190B3CCF919 (void);
extern void Sprite_GetPackingRotation_mE9EBA99C7A1F3BCED26BCFB086C136CC1A4358E9 (void);
extern void Sprite_GetPacked_m020A62AD57E08E1E3AF4F9FF20A77A7E80052684 (void);
extern void Sprite_GetInnerUVs_m68A07E15B8D8F07E33559998000B524B54E1951A (void);
extern void Sprite_GetOuterUVs_m0239F5571EA1AE399B426DE9362EEAC73A3ECC42 (void);
extern void Sprite_GetPadding_mF346EAFF67C810A108E64366EB5CB3CB2E01D066 (void);
extern void Sprite_get_bounds_m042F847F6C5118E6B14A3F79A1E1C53E7DFBF452 (void);
extern void Sprite_get_rect_m2D85032EBEDC505E346E49291B8816BDB18DF625 (void);
extern void Sprite_get_border_m024C8361A808BF597EC6E1849AADDA9C756B459F (void);
extern void Sprite_get_texture_mEEA6640C1B5D38F84CB64C775B201D7D9F48E045 (void);
extern void Sprite_get_pixelsPerUnit_m5A5984BC298062DF4CD2CB3E8534443FFCF31826 (void);
extern void Sprite_get_associatedAlphaSplitTexture_mBD0221BDF9855712C681C4DDCAD4EFA6EE614FDB (void);
extern void Sprite_get_pivot_mDFC0A205317DB2F3B6C720B8A5BE1C27D01C1D44 (void);
extern void Sprite_get_packed_m6B6B98A3891B350711499532C07F8D393AAB868E (void);
extern void Sprite_get_packingRotation_m3FD3489CC43013B2D5228AEB1ED8099E6C5B1D10 (void);
extern void Sprite_get_vertices_m2584A7F59A8E8362F16E0DDA4CC25A1EDF8D1D31 (void);
extern void Sprite_get_triangles_m5D2A3D916814891FF5DF236D8A2D72C89A66EFD4 (void);
extern void Sprite_get_uv_mAD4DAE6EAEC23340B69D0991FA4D8E72C6CA62FF (void);
extern void Sprite_GetInnerUVs_Injected_mC34F24437EB70A80781267A638BD909D68A091EA (void);
extern void Sprite_GetOuterUVs_Injected_m27113B4C012B99AF083390AD49203D329C15544A (void);
extern void Sprite_GetPadding_Injected_m12B39280FA7B844ADFEAB0BA1355F654755A67E9 (void);
extern void Sprite_get_bounds_Injected_m987E852C2009674973F2E254460636CF859F411A (void);
extern void Sprite_get_rect_Injected_mF254371640E3209C35452CD7CFFD61A06AD4EA97 (void);
extern void Sprite_get_border_Injected_m9D59B69C43462D8DBB6186B858264F4E84C3E200 (void);
extern void Sprite_get_pivot_Injected_m084D8C724F7754DDFB1D7DDF2A5E48DB10C585B9 (void);
extern void APIUpdaterRuntimeHelpers_GetMovedFromAttributeDataForType_m572E9998D4C574A15D5D044699A572DEBC3FC1E3 (void);
extern void APIUpdaterRuntimeHelpers_GetObsoleteTypeRedirection_m135466CB807CB14025653915037D56D246F0F46C (void);
extern void DataUtility_GetInnerUV_m0CEE9FB4108D7A880A4807F2F5BD5FA98C9917ED (void);
extern void DataUtility_GetOuterUV_m408EFF91CB39DE165F8D00D42603DF1C49C57CF2 (void);
extern void DataUtility_GetPadding_mB8E2E01509BFABEEACC45EA11D6A0FCF05F809C6 (void);
extern void DataUtility_GetMinSize_m927FDAD3190433E999165BD16CE81677EA9C0896 (void);
extern void SpriteAtlasManager_RequestAtlas_mE6D9398ADA0A5CB1A76407B59511779F2DAA2593 (void);
extern void SpriteAtlasManager_add_atlasRegistered_mA46A6A347F25B2E03DB4FD8044B93B4FD8ED50A5 (void);
extern void SpriteAtlasManager_remove_atlasRegistered_m67E745D3503463E3DB9CC12C157ABB4F469ABE79 (void);
extern void SpriteAtlasManager_PostRegisteredAtlas_mEBA789EAA074F9C6450ADF50722343BEF4509110 (void);
extern void SpriteAtlasManager_Register_m44D4E9341918EA32BEC92A77E851FF9A3BC21505 (void);
extern void SpriteAtlas_CanBindTo_mB4326EC04E7C2CC9D43AE04AEE9B91171F3BFA01 (void);
extern void UnityEventTools_TidyAssemblyTypeName_m744218193DC58EB2F113300DDE351170683797F7 (void);
extern void ArgumentCache_get_unityObjectArgument_mEA22BE8C25CDC789963C2DABF068E88147A66C69 (void);
extern void ArgumentCache_get_unityObjectArgumentAssemblyTypeName_m85640B88D8DA790019005A4ADD948E036ED79694 (void);
extern void ArgumentCache_get_intArgument_m7515338C0F3B5843E40CC48C303D2EFC02D9C19C (void);
extern void ArgumentCache_get_floatArgument_mDED33C174CAD9DFAD58F9D6DF482557C0FC20D38 (void);
extern void ArgumentCache_get_stringArgument_m4CA65BC60FC1FDCE88779C009ED0E1DC4BED2D9A (void);
extern void ArgumentCache_get_boolArgument_mB7A56994202FCB50BA04A6DBED9BAC45871F700A (void);
extern void ArgumentCache_OnBeforeSerialize_mF01AF8DE34554D86AEC843FEB41D14F3172D481F (void);
extern void ArgumentCache_OnAfterDeserialize_mD1C2E914447C2F69B43850F15AB19B62AF49DE96 (void);
extern void ArgumentCache__ctor_m8410B763CA027E30237E5954888A7F508800A331 (void);
extern void BaseInvokableCall__ctor_mD64C67D6FFB881F98555408743D7BB5CA7217B39 (void);
extern void BaseInvokableCall__ctor_m7633F06F55DFC3324C46A7C3DD6C55DC949FA0FE (void);
extern void BaseInvokableCall_AllowInvoke_m7BBC3A3F424104A84947708ECF8EEF74707F7661 (void);
extern void InvokableCall_add_Delegate_m5AD40C6D21D67A44980DF3B99946C4A2F17D9A10 (void);
extern void InvokableCall_remove_Delegate_mB8464CD88899199AAA70CD5EA4E02DCFB16045E1 (void);
extern void InvokableCall__ctor_mF3F94B432C977EE2DE7834EC2936E90D271C0464 (void);
extern void InvokableCall__ctor_m4FA1428E3A33219B2C8C5C571A705AC6B862FA70 (void);
extern void InvokableCall_Invoke_m874703DD260A64342495E79986B31EDA8D06C1F4 (void);
extern void InvokableCall_Invoke_m6F4828FD2B3E3BBB7AA6EECC2C37FB08538363F4 (void);
extern void InvokableCall_Find_mC76E5065AEEFC89956540199A4CB92E953E4B32F (void);
extern void PersistentCall_get_target_mA12C3C9A21F6F5335761CB0EB3C7C965D8C393AE (void);
extern void PersistentCall_get_targetAssemblyTypeName_m303DE56BDE5CD469D0210E1EA73F54B4C88228BE (void);
extern void PersistentCall_get_methodName_mFD7F88289C9EF5DE6D7EFD3FEF1A5C640CBAF088 (void);
extern void PersistentCall_get_mode_m3FFA4D4FC3DA0C38106323CD33ABBFA53ED01928 (void);
extern void PersistentCall_get_arguments_mA3B29A1F4E7328523674ADC6FC0C7332BA053410 (void);
extern void PersistentCall_IsValid_mD63347854BC781710D4CC9B5FC3C3996E84A325D (void);
extern void PersistentCall_GetRuntimeCall_m0DDE14D286D9995CCE65D2DFF27D57E4D476F072 (void);
extern void PersistentCall_GetObjectCall_mF7C9F7A24733E6B440637285FF76AF47AFDD021D (void);
extern void PersistentCall_OnBeforeSerialize_mD36FE363489E1A7C338AC7392F0DA13094825872 (void);
extern void PersistentCall_OnAfterDeserialize_m52A9B0536D334B3C89A0A9E4D923AA212201F485 (void);
extern void PersistentCall__ctor_m6EE5F241C45D97046ECAFCF45FB0DE96E7827142 (void);
extern void PersistentCallGroup__ctor_m1B17318026E3D419B2C194F66882E3BED6C4200A (void);
extern void PersistentCallGroup_Initialize_m937649041F14D0D20F959B07BA099246EC32BCCB (void);
extern void InvokableCallList_AddPersistentInvokableCall_mFB82EE201D90D84E0E25934EA879067BD666C0C1 (void);
extern void InvokableCallList_AddListener_m279B8BAED30DA27C305ADDF241F05CD2BC59625A (void);
extern void InvokableCallList_RemoveListener_m5C78FE9ECE5990F29636216E879139D5863F36C8 (void);
extern void InvokableCallList_ClearPersistent_m9A776CBBC13667875F1765B32B469BC12AFD4192 (void);
extern void InvokableCallList_PrepareInvoke_m0CF5EBCDF4913AFC13CBE09F6CFB687D0F771301 (void);
extern void InvokableCallList__ctor_mE70F25915B775E7258A12670B76C7F7B3C36BF1A (void);
extern void UnityEventBase__ctor_mB1F958EAC1A7C4B31253F2E1FED173A628725DEC (void);
extern void UnityEventBase_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mC47C72ED57A467E299925019E7DB9645D0F631F9 (void);
extern void UnityEventBase_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m835BA25C9F342C93FB9DF774C0894A82C4F049CB (void);
extern void UnityEventBase_FindMethod_m0B00339CC16B63CF1C50714D018A87948FC0B23B (void);
extern void UnityEventBase_FindMethod_mE417FEA28EC49921FA28EBDAB1214B0E6EC7E91B (void);
extern void UnityEventBase_DirtyPersistentCalls_m356D77B4849FC63501507E4D3F1054BD86D6B1CF (void);
extern void UnityEventBase_RebuildPersistentCallsIfNeeded_m981B8A6658A88F620345D2C7F4ADCD0D788B0266 (void);
extern void UnityEventBase_AddCall_mA78C058ED530789A28F42347B653190FEC84DBBC (void);
extern void UnityEventBase_RemoveListener_mFF8F8FAD5F18BA872C3CE005DC134B6828E1AD3B (void);
extern void UnityEventBase_PrepareInvoke_m4D04FA5D7025C093047DCD3DFEEFB9DF48764FC2 (void);
extern void UnityEventBase_ToString_mE86F29D699C7537CACCAF3945F797EE659CE6522 (void);
extern void UnityEventBase_GetValidMethodInfo_mCFA9547C470F2F90619A1514108BCE0F49F9B0CD (void);
extern void UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131 (void);
extern void UnityAction_Invoke_m5CB9EE17CCDF64D00DE5D96DF3553CDB20D66F70 (void);
extern void UnityEvent__ctor_m03D3E5121B9A6100351984D0CE3050B909CD3235 (void);
extern void UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302 (void);
extern void UnityEvent_FindMethod_Impl_m62E3D2795BACFF1BA2ED6A431ABD5FB2C7D3D681 (void);
extern void UnityEvent_GetDelegate_m6665C6282D3668BC57F2702FD0C3B108F4CFD226 (void);
extern void UnityEvent_GetDelegate_mBD5D37CFB826CB3329477A509A62BF7CE26A9EF8 (void);
extern void UnityEvent_Invoke_mFBF80D59B03C30C5FE6A06F897D954ACADE061D2 (void);
extern void FormerlySerializedAsAttribute__ctor_mD7361D52007EF623D139A726F892198E089F3C9D (void);
extern void PreserveAttribute__ctor_mF9E65066D9317F56C6F4AE274A1E2D55D6A62F96 (void);
extern void MovedFromAttributeData_Set_m155005FB8BCE0569C40B02D75FFADB3FBDB7EEDD (void);
extern void MovedFromAttribute__ctor_m1B944ED92ED635A19DD4E82765BC0200C0BF3E4D (void);
extern void MovedFromAttribute__ctor_mFDCCBD975A9BCC410529DB2B7C01659ACF8005CC (void);
extern void Scene_get_handle_mD508BE60333C6168610E12CECAB12E9B11C25E53 (void);
extern void Scene_GetHashCode_m74ACBFB8C656D5620A9A1E62D04E55ACBF63ADC9 (void);
extern void Scene_Equals_mB973481492F291BF8EAF1AD66B9F7FA3ACF3928D (void);
extern void SceneManagerAPI_get_ActiveAPI_m2040A088481EEBB6D6B9B1B2A4090CBA1129FFA3 (void);
extern void SceneManagerAPI_get_overrideAPI_m636C24986664B542327F2528A1128B7738DEF6E6 (void);
extern void SceneManagerAPI__ctor_m697F6B718DCE9B5E6CA4D58BBFB0CA275E003307 (void);
extern void SceneManagerAPI_LoadFirstScene_mA4217ED8AD09C74E82DA9C10A0558AF830228E6F (void);
extern void SceneManagerAPI__cctor_mEF0EF066433E952992273F1624DE367B9FCD52A6 (void);
extern void SceneManager_LoadFirstScene_Internal_mBD991BBA665DF9A290C6525A09B6D0D275CC5259 (void);
extern void SceneManager_Internal_SceneLoaded_m929615D240C989E0EA73E3141C8AAAA73BCB51D4 (void);
extern void SceneManager_Internal_SceneUnloaded_m8F6929B480F6398033CF78E654F01D9FDDBA0321 (void);
extern void SceneManager_Internal_ActiveSceneChanged_mD1207F7C46CFBA85FA58A4ACCF7C326A8AEE55DC (void);
extern void SceneManager__cctor_m130A0393C55C3A0553E24DC895939CC7184D91F9 (void);
extern void PlayerLoopSystem_ToString_m259B8533D2C64C15D381B16F32C710A0018684A0 (void);
extern void UpdateFunction__ctor_m0D1D766F22D02176396DA6745A60EA046EE8227B (void);
extern void UpdateFunction_Invoke_m9BCEE4E5BEE924EB804DA64314B78D0E831C179B (void);
extern void PoolManager_Register_m0AF905E5CD87059815F22468F3BA73706DCA4958 (void);
extern void PoolManager__cctor_m97C7011FB89B21685C0ABF9F8C0FE8C1BC7AA835 (void);
extern void MessageEventArgs__ctor_m66E6239501EEE8FCE1DA218E8189F46FB0669A45 (void);
extern void PlayerConnection_get_instance_mF08ABE514910AD8B105D42561CF97CBF135ABD3B (void);
extern void PlayerConnection_get_isConnected_mFDFB30D1AD4A7BFC66207092270E86B8D467209D (void);
extern void PlayerConnection_CreateInstance_m7903C5C28DAA3A2FF48EF6593E03A5E5A6DF7777 (void);
extern void PlayerConnection_OnEnable_m3BE74152671ABA48E7AD707773B93F57BDA53002 (void);
extern void PlayerConnection_GetConnectionNativeApi_mDF3BB4FF695E3ED19E525951FEED123C52FB4781 (void);
extern void PlayerConnection_Register_m8760D786583FD5793A2FCE5EEB8DDA17C649CF86 (void);
extern void PlayerConnection_Unregister_m46303AD2C27A3A71B6569376E5C461AF1D0DFCE1 (void);
extern void PlayerConnection_RegisterConnection_mF9F575D16B80B1299D0E058E5F4A8DBAA907784F (void);
extern void PlayerConnection_RegisterDisconnection_m2D2554B55821F11EB89442D54125A5C2984E0EC2 (void);
extern void PlayerConnection_UnregisterConnection_m0213376CEDCAAF5AF7C49226660C2F2182F2E1FD (void);
extern void PlayerConnection_UnregisterDisconnection_mB2E0A093BA8F75B183A2774CA49FD5762B4E8A6E (void);
extern void PlayerConnection_Send_mBFE41D75C28DF0885455CE809D40C489818F91B7 (void);
extern void PlayerConnection_TrySend_m6D589698ADF1E9A0E4CC208651F6CC86AD572423 (void);
extern void PlayerConnection_BlockUntilRecvMsg_mBDA2944F58B7984DDFA6A998CA558FC43B64896F (void);
extern void PlayerConnection_DisconnectAll_mDC4057C27894D4216DF83A9EF9107C86AA3C5D03 (void);
extern void PlayerConnection_MessageCallbackInternal_mABE4761961414EE58E31F8D06F9870AFD1BBD1BC (void);
extern void PlayerConnection_ConnectedCallbackInternal_m64117185C66F8947BFB1ED0E41C6D35A9AC35177 (void);
extern void PlayerConnection_DisconnectedCallback_mC66E0C59A86620D43E7C1418F8EAA006B63690A2 (void);
extern void PlayerConnection__ctor_m03E1218C1A0210FFDC4E2B2E2F79B7416E9D8F11 (void);
extern void U3CU3Ec__DisplayClass12_0__ctor_m07EBBD06394F8590D2B8DFD50B6D1EE94ABB0CBB (void);
extern void U3CU3Ec__DisplayClass12_0_U3CRegisterU3Eb__0_mAC55D4E3203F06ED7F9872599F1CF02DF2430859 (void);
extern void U3CU3Ec__DisplayClass13_0__ctor_m6AA357ABA48CA75462E938084C7953E820CB18AA (void);
extern void U3CU3Ec__DisplayClass13_0_U3CUnregisterU3Eb__0_m929D945DEF8430CA1E953420F9C28F43198F9FF5 (void);
extern void U3CU3Ec__DisplayClass20_0__ctor_m75B603A2B5F9AB50F7A52CCD844186FE1A6981AA (void);
extern void U3CU3Ec__DisplayClass20_0_U3CBlockUntilRecvMsgU3Eb__0_mE65B4F1DEAC961650E79C59F69302B4AC63CE3F1 (void);
extern void PlayerEditorConnectionEvents_InvokeMessageIdSubscribers_m67F47A7AA6EC55A672AD1020F329C4332BEEFFAE (void);
extern void PlayerEditorConnectionEvents_AddAndCreate_mB3B1355A8FDC43E5974CC3DE5BB17CB0C4328308 (void);
extern void PlayerEditorConnectionEvents_UnregisterManagedCallback_m61A82E77AA85A903BE0E534FEE601CABF9DE7809 (void);
extern void PlayerEditorConnectionEvents__ctor_mA1F7A1F05DA196CDC66A85361C5589F504C557B3 (void);
extern void MessageEvent__ctor_mD99E3A2C521C8B2B544F5DF2376258E9613D08EE (void);
extern void ConnectionChangeEvent__ctor_mEEB8C3ABC40815A0BBC2EEEEAD77ACAE1A6A0554 (void);
extern void MessageTypeSubscribers_get_MessageTypeId_m782C2CCF400D0C5A0FB343FBE1B66C9C09971449 (void);
extern void MessageTypeSubscribers_set_MessageTypeId_m4FA721221C0D7812EA1AFB6C406C0620FF06761B (void);
extern void MessageTypeSubscribers__ctor_mFC17A12701D7DB8739ABF17DB1D5EC41531CFED6 (void);
extern void U3CU3Ec__DisplayClass6_0__ctor_m2EE8CF87D7C3816A57E4D6238DEA0660DB2C7303 (void);
extern void U3CU3Ec__DisplayClass6_0_U3CInvokeMessageIdSubscribersU3Eb__0_mAB457C850FBAB1CF66C7EFAAE9F57EAD0FED6CFF (void);
extern void U3CU3Ec__DisplayClass7_0__ctor_mC3BBDE08CDDEF0DA0811796906BA4A74C287CC2E (void);
extern void U3CU3Ec__DisplayClass7_0_U3CAddAndCreateU3Eb__0_mD687F2BC0FE80692E5F88480DC5B861FE122F82E (void);
extern void U3CU3Ec__DisplayClass8_0__ctor_mEFD5A284D56758F6EA5F97C12FDE739E7E1C2C93 (void);
extern void U3CU3Ec__DisplayClass8_0_U3CUnregisterManagedCallbackU3Eb__0_m3906587CC8F4CF5B752383CE6EA540B27E67055D (void);
extern void DefaultValueAttribute__ctor_mC1104F2F0A2CD67CE308CD7E5C1CCE74482C1BB4 (void);
extern void DefaultValueAttribute_get_Value_m8FC7A517D291CDDB7B5D17E811F5CB11FCF59D24 (void);
extern void DefaultValueAttribute_Equals_m6E5B33344C4FD2FC4775A657481B63F82F18B925 (void);
extern void DefaultValueAttribute_GetHashCode_m6F99553C7E76E711DCA6368417F66898F5AF6359 (void);
extern void ExcludeFromDocsAttribute__ctor_mF280A16634D72D09D550C2BA2294D9234D50C771 (void);
extern void VertexAttributeDescriptor_get_attribute_m2F7C7084857741A39E9FF0D4BABF98F9C7018AAA (void);
extern void VertexAttributeDescriptor_set_attribute_m526ED099EEA12DCB2FCB1BC88445FBF921E850A2 (void);
extern void VertexAttributeDescriptor_get_format_mF8EF98F3E047BC26D177C3A008BCDAF979E52C79 (void);
extern void VertexAttributeDescriptor_set_format_mCA82263EBD7802621D9ECE42D0F44D4AAECD46C5 (void);
extern void VertexAttributeDescriptor_get_dimension_mAB440DDFA08BF61D717547EC8B9C43C3DFC56FB8 (void);
extern void VertexAttributeDescriptor_set_dimension_mEF4325AE27221C732B214354A2DEB044ACFC69EE (void);
extern void VertexAttributeDescriptor_get_stream_mAA4D41E2B4B9024161B176700100ADB27855E4FF (void);
extern void VertexAttributeDescriptor_set_stream_m9A3991AA0365A1DCEDF80A61990F7B48913A1C0A (void);
extern void VertexAttributeDescriptor__ctor_m713B31395FB13FDEB2665F5C4C31572D5875A43A (void);
extern void VertexAttributeDescriptor_ToString_m45F7E7D6715E000C617173246F2C9BE23DF3FC8F (void);
extern void VertexAttributeDescriptor_GetHashCode_mCB8C5F6AD422FE6A410C5406843FCE4B0ED5F2F8 (void);
extern void VertexAttributeDescriptor_Equals_m6FBFEE42E1BAAAC2D38434EDF0906C5B76D56BE5 (void);
extern void VertexAttributeDescriptor_Equals_mF328DE139864C201987238048AC79F8925CA435D (void);
extern void GraphicsSettings_get_lightsUseLinearIntensity_m74D1A18837CB7E9D3A9BF20D44212F94DD1F67B9 (void);
extern void OnDemandRendering_get_renderFrameInterval_mC9E8FC6225DCCB074FD3D7BD128734DA321A2CCD (void);
extern void OnDemandRendering_GetRenderFrameInterval_m2E7629CD00648D6DEC5D78E631510ACF1350A931 (void);
extern void OnDemandRendering__cctor_mED0B1BA868A046D26D214B1DD1C0BAAD6147E9BB (void);
extern void BatchID_GetHashCode_m3F4DFD3D722865BBC47F74C5E452E985B6BBBA3A (void);
extern void BatchID_Equals_m953C8EE9ABF083CFAA3D7210F1085C9FD7EED71D (void);
extern void BatchID_Equals_m4207BA1B44B5BF1DA2847571B6D9A981F5AE3CFF (void);
extern void BatchID__cctor_m64258A0EFBDACA106FC41F54A1CF9936A884A4B7 (void);
extern void BatchMaterialID_GetHashCode_mE28980612614E66848ADABB7825CA73FFA39DD09 (void);
extern void BatchMaterialID_Equals_m776A152CA1957E92FCD24B4E2EDAF8A6D78E953E (void);
extern void BatchMaterialID_Equals_m073A3DECD4CDE73F0CC314024B89399982E5BA83 (void);
extern void BatchMaterialID__cctor_mCAFD39D3BA87D50321EA76B09E7CD4B8028E9A35 (void);
extern void BatchMeshID_GetHashCode_m62EDD33030F375FAAD44A23FAD7901A2BE1D08A9 (void);
extern void BatchMeshID_Equals_mA501F8E992E7B32BD8985DD68D5D0B360A104A42 (void);
extern void BatchMeshID_Equals_m985986D473BB12E9F43C59B41846660884B15C06 (void);
extern void BatchMeshID__cctor_mC7DF8454B495B27D09F84E06E8D42B5F4224043A (void);
extern void BatchPackedCullingViewID_GetHashCode_m588865495A1A1F2AC79A62D76B2DDC91D743F4A5 (void);
extern void BatchPackedCullingViewID_Equals_mD7B8D7EFB1678D80FC11B9FA1171988669CD0C7E (void);
extern void BatchPackedCullingViewID_Equals_m2E5BC66A5B8D6737BABE5D6A0019F901192D2A9F (void);
extern void BatchCullingContext__ctor_mB26CB3EE7FD392F94B9816FCA04D76B8AAB6FDF3 (void);
extern void BatchRendererGroup_InvokeOnPerformCulling_mCBE957A7C05B4023359DE8EADB3B17D93956CCC3 (void);
extern void OnPerformCulling__ctor_mFB91FB8D6D713AC39AFB921AF1C23A50EC80529B (void);
extern void OnPerformCulling_Invoke_m9DA6498101531B96227E97025316476D1EB79DF2 (void);
extern void LODParameters_Equals_mAD45601A9E881620B23A7922D8AA5AFBC91E0B9F (void);
extern void LODParameters_Equals_mF956A067D82497A00AC8234322CD848E6FB7BADE (void);
extern void LODParameters_GetHashCode_mECFCEAAAC935759A99C30C69BBC99A1148F46F40 (void);
extern void RenderPipeline_Render_m41E15C2221365D92636651792FBDAD1A04E8AF1A (void);
extern void RenderPipeline_InternalRender_mCB77395158F4572F348D8227BA9127ABF1C9C5BE (void);
extern void RenderPipeline_get_disposed_mF0D4B88DF44249E0FC2371697B913DD5A81B3850 (void);
extern void RenderPipeline_set_disposed_mB7EC4BD04C80015CBCC0B92A65A6DE615F2828A8 (void);
extern void RenderPipeline_Dispose_mDF8A62A6B7D3A00128C9341921C036D015C41179 (void);
extern void RenderPipeline_Dispose_m581E2B33EF0CCAF2C3E7BD71AE32B4974D259286 (void);
extern void RenderPipelineAsset_InternalCreatePipeline_m662E6A3D3B14C19D5318E172A4AF81FEF71C6252 (void);
extern void RenderPipelineAsset_get_renderingLayerMaskNames_mC0978F38B2EADD1462593AC6D8395E612CB1329D (void);
extern void RenderPipelineAsset_get_prefixedRenderingLayerMaskNames_m89254E02D74CC6FC9320104B8A1748534FE55F6B (void);
extern void RenderPipelineAsset_get_defaultMaterial_mCF112570E7B47208A0B700BA97B33CAE6713D323 (void);
extern void RenderPipelineAsset_get_autodeskInteractiveShader_m261A0788B4F73AFC5A50953878552BC3BFF8B674 (void);
extern void RenderPipelineAsset_get_autodeskInteractiveTransparentShader_m6E2A2439FCB378F64DC46CB6DB517A1661A306F8 (void);
extern void RenderPipelineAsset_get_autodeskInteractiveMaskedShader_m42DB9A70B290E217B91616E9274C9B7E58D54362 (void);
extern void RenderPipelineAsset_get_terrainDetailLitShader_m74F2FA112CD18493E7003F6F711AA09A8C930136 (void);
extern void RenderPipelineAsset_get_terrainDetailGrassShader_mBE126045BF9048B59CE56D1BD9C69C3DEB4CDD96 (void);
extern void RenderPipelineAsset_get_terrainDetailGrassBillboardShader_mE233539926B92ADB7A057C34CBCD7E51823C75A2 (void);
extern void RenderPipelineAsset_get_defaultParticleMaterial_mC68CA6787815E00CF501F268F34DA0B3C83B3013 (void);
extern void RenderPipelineAsset_get_defaultLineMaterial_mB4DD122B043417F3810FEAAA74E79B369A26B484 (void);
extern void RenderPipelineAsset_get_defaultTerrainMaterial_m129D75CFE9689112452AA911C24BEE4939ADB520 (void);
extern void RenderPipelineAsset_get_defaultUIMaterial_mBB9F6694EEEB97AC6140C094A48743660A0DAF04 (void);
extern void RenderPipelineAsset_get_defaultUIOverdrawMaterial_m19832E76629C0AFC18F0D61C5C1CC47533200038 (void);
extern void RenderPipelineAsset_get_defaultUIETC1SupportedMaterial_m2D1DC4B89F59283C8AE3101F3DC05D88C10D4C10 (void);
extern void RenderPipelineAsset_get_default2DMaterial_m2418C7E1A0974B2A06CF3BE81590C7B0DF1DB113 (void);
extern void RenderPipelineAsset_get_default2DMaskMaterial_mEE67105F80768BDFD1C93E8709FEBF2D4561E14E (void);
extern void RenderPipelineAsset_get_defaultShader_m633AD6A97B2D1D436E42920D766B1283C1FED858 (void);
extern void RenderPipelineAsset_get_defaultSpeedTree7Shader_mCEF3795C9E36F0A9D9A3CE29824790B172637252 (void);
extern void RenderPipelineAsset_get_defaultSpeedTree8Shader_mAEB3D02FC0A0016BAC5C22BC1967827403E5C17A (void);
extern void RenderPipelineAsset_get_renderPipelineShaderTag_m1F68B8E1EC2EB1BA57542EDF80505125B2BE4D34 (void);
extern void RenderPipelineAsset_OnValidate_mD160C7BDEA559BAF3DDA48B4819307E07B377F52 (void);
extern void RenderPipelineAsset_OnDisable_mE99CEED707BDC901AD37DC976FA3A3A313E7E00C (void);
extern void RenderPipelineAsset__ctor_mC45BECAED54BEDC4555AF010323EABF49BA7B78A (void);
extern void RenderPipelineManager_get_currentPipeline_m270A15A23593BAD8D6A20CE2E328210FF9AA692D (void);
extern void RenderPipelineManager_set_currentPipeline_m1C8CDEC2FD862823D0C4DC007CB57DBAFB1CDB3F (void);
extern void RenderPipelineManager_OnActiveRenderPipelineTypeChanged_m7BCA10285B43964387C7DA20EFC7C0C6A47C4E24 (void);
extern void RenderPipelineManager_OnActiveRenderPipelineAssetChanged_m377430D7BE5CE854DA6034968CF1095AAB15ED1C (void);
extern void RenderPipelineManager_HandleRenderPipelineChange_m8B7BA0E0ADC1AD84C4A2E119E08C225EF519482E (void);
extern void RenderPipelineManager_CleanupRenderPipeline_m1104AEE59AFE2EC1F1AD15DCF862802230466F99 (void);
extern void RenderPipelineManager_GetCurrentPipelineAssetType_m78BBEBD08F2BCB77FA23BDCD54C10A4102115EF5 (void);
extern void RenderPipelineManager_DoRenderLoop_Internal_mB646C8738F4A9859101F3BE94809E2E10BBDB1FB (void);
extern void RenderPipelineManager_PrepareRenderPipeline_m342C65E962B20B547DB2BAF537EA870457736108 (void);
extern void RenderPipelineManager_IsPipelineRequireCreation_m97B62DE27F930ADB3B8DE6831DDF1185837E826F (void);
extern void RenderPipelineManager__cctor_m8A9308C1CD1B4F533F5D9855ECB44D6B71620807 (void);
extern void ScriptableRenderContext_GetCameras_Internal_m852016B3544E3ED5FEFB9695EC175622A5B6A8C8 (void);
extern void ScriptableRenderContext__ctor_m10159F14BB69F555C375E13BB77A1898FDB42FA5 (void);
extern void ScriptableRenderContext_GetCameras_m9B2329F79132EE49B719560AD739FD3601C44189 (void);
extern void ScriptableRenderContext_Equals_mBFDA5815F2B6ABA9B16503DA906B8BA42078718D (void);
extern void ScriptableRenderContext_Equals_m99E5A233945DFC3B9A786F2413ECE68E5019AB88 (void);
extern void ScriptableRenderContext_GetHashCode_mA1EE09239F1ACFC29A2ADB027D5E76E690510207 (void);
extern void ScriptableRenderContext__cctor_m0C385A2E228FAAEBACE8E912E26F05147525E8B1 (void);
extern void ScriptableRenderContext_GetCameras_Internal_Injected_mFA0C805805CE2CC5BB6AD8DB0075733BF194FEE7 (void);
extern void ShaderTagId__ctor_m4191968F1D2CE19F9092253EC10F83734A9CFF5B (void);
extern void ShaderTagId_Equals_m02826F7AFC63AA3AE5DB14F7A891F8F173FD9A33 (void);
extern void ShaderTagId_Equals_m932EFCC38C276EEB2784BBC866330F4C595F52E0 (void);
extern void ShaderTagId_GetHashCode_mF5E3A1F96CBDFDCEFABE1B56125EBBA6E3B9EFEF (void);
extern void StencilState_set_enabled_m6DC861C699D1044E896E833D2DAE69B82F796564 (void);
extern void StencilState_set_readMask_m1BA8F9033413889D4E77DA343DC0029566A9BB9B (void);
extern void StencilState_set_writeMask_m94471C671E03D42F36DA61436B1068B362375D65 (void);
extern void StencilState_set_compareFunctionFront_m1388C37901DAB6AF9D23C0F01946DCCE19BC9BFC (void);
extern void StencilState_set_passOperationFront_m1F15CC29366DAEAA6CCE1DB0622C70D6ECC5A3EB (void);
extern void StencilState_set_failOperationFront_mADCECAE5D2E75ABAE51650F1F314E661D09C2CD6 (void);
extern void StencilState_set_zFailOperationFront_mC7D8F0A08B9AEC4203BD6B352CB795E8011EFBB6 (void);
extern void StencilState_set_compareFunctionBack_m8AF73F4E8FC95A46D33E3192C50702D2AA15D61D (void);
extern void StencilState_set_passOperationBack_m4B1395FE21F5B5C809DC6F31D5824A90E05ED220 (void);
extern void StencilState_set_failOperationBack_mD279271DD1F99EE5B8BC19F5AE60988E6C6F0E4A (void);
extern void StencilState_set_zFailOperationBack_mC092ABD8A5EA87245640A10E54C6A1990C4F6864 (void);
extern void StencilState_Equals_m9FFB8A41D8838FD128875CB2D4DAA760C6DF1051 (void);
extern void StencilState_Equals_mEA45A5D2BF2223B15EE0FB8BCEDBA9CB534ADF4B (void);
extern void StencilState_GetHashCode_mB4A02DEE780377C853D16FFF49CCB9D9F4F711A5 (void);
extern void SupportedRenderingFeatures_get_active_m09012C98E24D5B2E8C09F6657FC5CD19B2AF3054 (void);
extern void SupportedRenderingFeatures_set_active_mB2A1A6137C816592E6526CD3DA7405260EAEA8AE (void);
extern void SupportedRenderingFeatures_get_defaultMixedLightingModes_m7F9FDF9012EC41E36466613C8A9103D74666CBC4 (void);
extern void SupportedRenderingFeatures_get_mixedLightingModes_m929C0CE80A4990993EBCAB8B46C1E273A0829137 (void);
extern void SupportedRenderingFeatures_get_lightmapBakeTypes_m1311A5AD5BE1A6BA3251238C893D7D340358C156 (void);
extern void SupportedRenderingFeatures_get_lightmapsModes_m925D670110EF7109A26BE1B228066E1201FAAE38 (void);
extern void SupportedRenderingFeatures_get_enlightenLightmapper_mF7C756BBD4E605DD047BD502DFF8569C4CEE8F27 (void);
extern void SupportedRenderingFeatures_get_enlighten_m6F973FEB7CCF0BB1B7A2F25317EADC5F6FD95ED6 (void);
extern void SupportedRenderingFeatures_get_rendersUIOverlay_m657FFFC5B360F7BCE9964EF50E7449779224AEFC (void);
extern void SupportedRenderingFeatures_get_autoAmbientProbeBaking_m42E98E922511B1CF790FC414C9A85D70DFACA2C8 (void);
extern void SupportedRenderingFeatures_get_autoDefaultReflectionProbeBaking_mFDB934E6645FA5CA95E1F0BEF4A12345A1025207 (void);
extern void SupportedRenderingFeatures_get_overridesLightProbeSystem_m62C56592C44FDD2FC89EAA4ADE71435CDE65EB64 (void);
extern void SupportedRenderingFeatures_FallbackMixedLightingModeByRef_mEC3BA55F7A145AFCD9762F1FD752CA4D587D0F88 (void);
extern void SupportedRenderingFeatures_IsMixedLightingModeSupported_m11E0ADD90300D5396090ED9B2EFD9924524D50AF (void);
extern void SupportedRenderingFeatures_IsMixedLightingModeSupportedByRef_mE48C26F367ABEC734BDB7A8A66FEB8796A401AAF (void);
extern void SupportedRenderingFeatures_IsLightmapBakeTypeSupported_mE8EE2ACBE267FDAEFA6229CD67298C9D3A16C691 (void);
extern void SupportedRenderingFeatures_IsLightmapBakeTypeSupportedByRef_mABE7E2BC06CA25519079DF4EEC89667EB51B8460 (void);
extern void SupportedRenderingFeatures_IsLightmapsModeSupportedByRef_m99BF1104DDA5C0EB37E529F76670E7F10E6319E8 (void);
extern void SupportedRenderingFeatures_IsLightmapperSupportedByRef_m891045E2F4FF9AC667E6FC75F6119D5AB546FBDB (void);
extern void SupportedRenderingFeatures_IsUIOverlayRenderedBySRP_m15CF763E502A362DC677DEBBEC12A9B45CEEB458 (void);
extern void SupportedRenderingFeatures_IsAutoAmbientProbeBakingSupported_mBC9E03F60617335C1E8D4DE88FD57F591CDA6C50 (void);
extern void SupportedRenderingFeatures_IsAutoDefaultReflectionProbeBakingSupported_mD59A4D48FC5A6095035739F187C9BCF4D0DC67C5 (void);
extern void SupportedRenderingFeatures_OverridesLightProbeSystem_mB8D7CE26D5DA36E58988A09F399227549F3B7B08 (void);
extern void SupportedRenderingFeatures_FallbackLightmapperByRef_mBA3E826098F185845769A99830E50C43294304FF (void);
extern void SupportedRenderingFeatures__ctor_mF7728980F81142B7BD45FBB25AB001B17A4BF0ED (void);
extern void SupportedRenderingFeatures__cctor_mD97BB2123C1AC4DAEF2B5D8D1DC8219C31968416 (void);
extern void SortingGroup_get_invalidSortingGroupID_mEA453186185B225FA2410988959180BAEC604310 (void);
extern void SortingGroup_GetSortingGroupByIndex_mC4CFB06D8C4B0EA27FC4BDC60AA05F4E0B28426B (void);
extern void SortingGroup_get_sortingLayerID_m37C70CEA78DA6E28F405C67EDE95D5C2191F0055 (void);
extern void SortingGroup_get_sortingOrder_mC80606E6BB9A6D5E3AB61A0451C48203A0C1AB6A (void);
extern void SortingGroup__ctor_m0F2104D49BFE2551466B75F1A75E20EFBD0E350B (void);
extern void Playable_get_Null_m0D0BB32BC23983C490A363AD6AAC47D8B72864F2 (void);
extern void Playable__ctor_mD2EB35E024816AEED68795D0124EAB30E05BF6C4 (void);
extern void Playable_GetHandle_m39356D23E849DC5428B262092657662C064E04F8 (void);
extern void Playable_Equals_mD72D3DB892B8867A0E7BAC032A16C08616EEFF86 (void);
extern void Playable__cctor_m079282CFC9FB3C8D02C0F6F9FF45C167DF583459 (void);
extern void PlayableAsset_get_duration_m4668A767DDB780565E6506E63B4797B820405CFE (void);
extern void PlayableAsset_get_outputs_m5760B1B5EE08B0327FA7D90AE92C94227B1C993C (void);
extern void PlayableAsset_Internal_CreatePlayable_m8D90055AAB62B51D1F73B58F30715C4512100714 (void);
extern void PlayableAsset_Internal_GetPlayableAssetDuration_mD80D4032B1E5DECC8710CB63A49E815F21EFDBDA (void);
extern void PlayableAsset__ctor_m36B842356F02DF323B356BAAF6E3DC59BA9E1AB8 (void);
extern void PlayableBehaviour__ctor_mA6202DCD846F0DDFC5963764A404EE8AFABEA23A (void);
extern void PlayableBehaviour_OnGraphStart_mB5944807796239EFB530022539674C4E4D185D65 (void);
extern void PlayableBehaviour_OnGraphStop_mF80DFC8A3C2D2CA9299011D9E871ED6A8A9586CA (void);
extern void PlayableBehaviour_OnPlayableCreate_m22B0F0051A677A523C5702AABC6B1C9D358E90B8 (void);
extern void PlayableBehaviour_OnPlayableDestroy_m3DB0AF7BD9689DA1BCCBBFD19BDD544143027C3C (void);
extern void PlayableBehaviour_OnBehaviourPlay_m05F6FCCBC6E8FB4BA0BE2690045AF28BF95C6FE2 (void);
extern void PlayableBehaviour_OnBehaviourPause_m431A7BD2EE99C1862563FEA37E20C365B753930B (void);
extern void PlayableBehaviour_PrepareFrame_m33FED1E870D350D8276712A2CD75118FEFAA86BD (void);
extern void PlayableBehaviour_ProcessFrame_mB80DDB2AB5D7EC0D3B9A466D37BE8556F6BBD2A0 (void);
extern void PlayableBehaviour_Clone_m6A5B052F4ECA2ADED5937A4843777F52CCD33EE8 (void);
extern void PlayableBinding__cctor_m3055AFB9F43633F1353C40FC9E1B2A4492732AF1 (void);
extern void CreateOutputMethod__ctor_m5A339017CD8ECB0140EB936FD2A5B589B20166B4 (void);
extern void CreateOutputMethod_Invoke_mEC7DC5D9A9325BFFB17C248AE9738637704B89CC (void);
extern void PlayableHandle_get_Null_mF44FE0A71C67054D272F454C91F7E08CBF14A975 (void);
extern void PlayableHandle_op_Equality_m0E6C48A28F75A870AC22ADE3BD42F7F70A43C99C (void);
extern void PlayableHandle_Equals_m60AD76B7D38CA989AE84501B2E9F9ED5CB5F9670 (void);
extern void PlayableHandle_Equals_m81BA0E127133DFF3E45DA61D185FDF48E16BCF45 (void);
extern void PlayableHandle_GetHashCode_m10FB32ECDC0B9D7BDAEA9E3B76BDDF4614F4EF4F (void);
extern void PlayableHandle_CompareVersion_m228CA864DC2BCAA0E03B4C74EC9F2A7B529526D9 (void);
extern void PlayableHandle_IsValid_m07631D12846BAAF2CC302E69A28A44BFE9EB5098 (void);
extern void PlayableHandle_GetPlayableType_mD9750F1B85DF086F52641D6AB85789601486B686 (void);
extern void PlayableHandle__cctor_mE31857278AA27F9CF449BD99AC79EC5E295A5278 (void);
extern void PlayableHandle_IsValid_Injected_m9D662778C1A39FD8AEC18AEF053AE5C3DBD7B4B9 (void);
extern void PlayableHandle_GetPlayableType_Injected_mD71007C85AACC8A18E2A5296B88FD8EA47349A9D (void);
extern void PlayableOutput__ctor_m55FBB20EC479F67641835EA48D84A1AB3DF39747 (void);
extern void PlayableOutput_GetHandle_m12FF2889D15884CBEB92A6CB376827EBA5A607BF (void);
extern void PlayableOutput_Equals_m4CC730818751114DC5643600B5FE20243F4B7121 (void);
extern void PlayableOutput__cctor_m02CBFEB6C9DB324655B9D354B32C268EED13749A (void);
extern void PlayableOutputHandle_get_Null_m656E8D2549FA031DA8A2EA5B39CE3B33D75B69F8 (void);
extern void PlayableOutputHandle_GetHashCode_mC2FAF756D71026E3AF4492157EDAE7186429B079 (void);
extern void PlayableOutputHandle_op_Equality_m116A314100562913DD28474B8D7DA5FBFCA9CD3C (void);
extern void PlayableOutputHandle_Equals_mB9106CB9333E0BF4C893E43AD7A23B64471CC21A (void);
extern void PlayableOutputHandle_Equals_mF5C23882B2A007186F00EB3D23E9BD6664E8DAE6 (void);
extern void PlayableOutputHandle_CompareVersion_mAB102578900E20BB3B4273F94D1A6AFDB6E20FFD (void);
extern void PlayableOutputHandle__cctor_mBA610D820061BDA36802735EEC57A83B0985CFC2 (void);
extern void LinearColor_get_red_m376617B8E3156420835055189BB28D953FE46A2A (void);
extern void LinearColor_set_red_m0ACFCEDDD205A6F235BE95936816E92898B01B52 (void);
extern void LinearColor_get_green_mCCE90A662234EE3605368F3AEC14E51572665AE5 (void);
extern void LinearColor_set_green_mBD9C7EA6415DC54B3F6B643C3CD02B71565F0694 (void);
extern void LinearColor_get_blue_mAFAEA5D5590DD14CFC48BC18DF4BFEBBDCB0A99A (void);
extern void LinearColor_set_blue_m3FEEAF946772BB177733B67D9DA4B72D84874375 (void);
extern void LinearColor_Convert_m0E220E18AC54A8040BAD7FFEB0D81538639F9BBA (void);
extern void LinearColor_Black_mF5AEFA40487500C1683D14FFA58554BF4D7B1A42 (void);
extern void LightDataGI_Init_m112DEBB76EC57AC52E6384C97A3E8B2EAA867207 (void);
extern void LightDataGI_Init_mACE06E00CC639CA89F3847E9DB55FD0F00812A7A (void);
extern void LightDataGI_Init_m0A999D118CDCBDA99B9E24231ED057D943C9C67B (void);
extern void LightDataGI_Init_mDC887CA8191C6CADE1DB585D7FEB46B080B25038 (void);
extern void LightDataGI_Init_mB2D1C73EDFEA6815E39A0FE3ED2F7BF9A7117632 (void);
extern void LightDataGI_InitNoBake_mBDF2EFB22D4BEE63B6F25F4EE9F1522D2866ED43 (void);
extern void LightmapperUtils_Extract_m936FF4E20F593777EABF072404B37D0C1EB3AF5D (void);
extern void LightmapperUtils_ExtractIndirect_m5776341FC44CD3BBB634828E668732C2A490BB78 (void);
extern void LightmapperUtils_ExtractInnerCone_m8B2B838A7D49A49D64813232503D5C3CA8957C5E (void);
extern void LightmapperUtils_ExtractColorTemperature_mEA79654385184193BC807A191696BE14B04ABEAA (void);
extern void LightmapperUtils_ApplyColorTemperature_m5286438BDED2F10292887505A26B1E33C714C325 (void);
extern void LightmapperUtils_Extract_m44511C1C63663F51CD77ABF24CC4B34B9A826F0F (void);
extern void LightmapperUtils_Extract_m47570BBE32168BBEA4C823D83C8A94A4CBF03AE2 (void);
extern void LightmapperUtils_Extract_m9F0C60CB137D268694B8CB324C73E799E1CE73F9 (void);
extern void LightmapperUtils_Extract_m3B3FFE050376D624857D5D67413BD532518949F1 (void);
extern void LightmapperUtils_Extract_mA319A386DA025BF5F0B7D9C398ACD3BE3AF65ABB (void);
extern void LightmapperUtils_Extract_mF6521637E4DD97C8BBD71696B5A61C7B7B8C83D4 (void);
extern void Lightmapping_SetDelegate_m8BEF0FE5035180FF94119860CD15BBE2BE90129D (void);
extern void Lightmapping_GetDelegate_m073E4FFA73169C20833F77984024BD328003258A (void);
extern void Lightmapping_ResetDelegate_m8D4AAF4F08C8697953B3CB110DD4E6CD130371D9 (void);
extern void Lightmapping_RequestLights_m1967533AFFB328B3386E7E0D1EC414105E509B80 (void);
extern void Lightmapping__cctor_m6AEDE40A651280EC1A7944E9CFD161AFB78802B3 (void);
extern void RequestLightsDelegate__ctor_mFFCE8681C67A169A04BEA2201C393E1FC84CAB7D (void);
extern void RequestLightsDelegate_Invoke_m01792B793691E6471596FF9B30E4D6F8EA18227E (void);
extern void U3CU3Ec__cctor_m766681161796D9912477BF60542C4DD1EB0D2096 (void);
extern void U3CU3Ec__ctor_m3FBD26AEC83F79DACB13A7EF6FE5F539A71F0902 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__7_0_m3DE1C9F0E58017EDCEAFA5FEC90132A153B492F6 (void);
extern void CameraPlayable_GetHandle_mA04469CA50B43AF6219F9967B8AEB310CB5455BD (void);
extern void CameraPlayable_Equals_mD0FA195F3EA6511043E8F0AA1680CEB7E0E2E2CF (void);
extern void MaterialEffectPlayable_GetHandle_m748319E116317E9ADD1EA36A4EDA488338471058 (void);
extern void MaterialEffectPlayable_Equals_mC55640B5D29F90360F9743549FABD43C5AA320EC (void);
extern void TextureMixerPlayable_GetHandle_mB75CF651C6BDDF347ED6938D0F1DE4BED92BB7CD (void);
extern void TextureMixerPlayable_Equals_m6838329B39779020FC3309B7406B8A0418F44FE7 (void);
extern void BuiltinRuntimeReflectionSystem_TickRealtimeProbes_m0CD6423541B0FCB022D55498C348A013E06E5F39 (void);
extern void BuiltinRuntimeReflectionSystem_Dispose_m2CDBD30196F65463B8E86AC97DA2370A4D68762D (void);
extern void BuiltinRuntimeReflectionSystem_Dispose_m6B57B7E11B7A095063597FBCB0C6EE7036003F6B (void);
extern void BuiltinRuntimeReflectionSystem_BuiltinUpdate_m833B3EB0E69D46FDAB5A890FECB417C4D9D5D941 (void);
extern void BuiltinRuntimeReflectionSystem_Internal_BuiltinRuntimeReflectionSystem_New_mA100197AC7CA73600AAD55A67E43039EEF8D2C27 (void);
extern void BuiltinRuntimeReflectionSystem__ctor_mC85D8357332DEC8325E27837409E463208ACE0E5 (void);
extern void ScriptableRuntimeReflectionSystemSettings_set_Internal_ScriptableRuntimeReflectionSystemSettings_system_mA216659518CF27854FB65C184B10197AB74AFBF7 (void);
extern void ScriptableRuntimeReflectionSystemSettings_get_Internal_ScriptableRuntimeReflectionSystemSettings_instance_mED3776EB64E9E6BF61705125F20F6893C2098E03 (void);
extern void ScriptableRuntimeReflectionSystemSettings_ScriptingDirtyReflectionSystemInstance_mCDA2744C4AD02637B40F84222084C18F0FC369EB (void);
extern void ScriptableRuntimeReflectionSystemSettings__cctor_m8225B0B8673C3B8B4529F9BFDAC91D417F1DB083 (void);
extern void ScriptableRuntimeReflectionSystemWrapper_get_implementation_m1AFA781CCFEFE334D758AC43A9FAB9E0FB0F5C40 (void);
extern void ScriptableRuntimeReflectionSystemWrapper_set_implementation_mF1552E093F0F437DF191D7CBB0CF7981C36744D8 (void);
extern void ScriptableRuntimeReflectionSystemWrapper_Internal_ScriptableRuntimeReflectionSystemWrapper_TickRealtimeProbes_mDC08C9639CAF2D13623E82B3A9C51689D2FED2B3 (void);
extern void ScriptableRuntimeReflectionSystemWrapper__ctor_mCF4DB3AC3AEB1FC08CB03DD0C1733E9BDED4DF8D (void);
extern void GraphicsFormatUtility_GetGraphicsFormat_mE38154E9B9C810EDAF2FAD3E1F1CD856FFC13F3C (void);
extern void GraphicsFormatUtility_GetGraphicsFormat_Native_TextureFormat_m4A193B562F6F81CE2C1C755B26B67564C2F65319 (void);
extern void GraphicsFormatUtility_GetGraphicsFormat_mB9E291EB1EC96594074112E54A7B9CAC20FC7BFA (void);
extern void GraphicsFormatUtility_GetGraphicsFormat_Native_RenderTextureFormat_m27057B9C12BF2ADFF0C8A39BD7D03A9615304942 (void);
extern void GraphicsFormatUtility_GetGraphicsFormat_m3DD7EAFBC4F60FA47453B93DAA7B392AEC818BD5 (void);
extern void GraphicsFormatUtility_GetDepthStencilFormatFromBitsLegacy_Native_m6C8C3D62D09CAA5333599E53ED45700AEE76E06B (void);
extern void GraphicsFormatUtility_GetDepthStencilFormat_m76EEE7255F874FD3AC8E149830EE48F345DF8425 (void);
extern void GraphicsFormatUtility_GetDepthBits_mA3ED2245DC3C1C593668C2F152A0DA42052CEE94 (void);
extern void GraphicsFormatUtility_GetDepthStencilFormat_m963D66601AD1C71D4E90483076BCDB175F958321 (void);
extern void GraphicsFormatUtility_IsSRGBFormat_mF3A393D43D68789A16087FF64CA2C050A8485C53 (void);
extern void GraphicsFormatUtility_IsCompressedFormat_Native_TextureFormat_m3CE0D5ED90F6323D412C876460BE13792C8CCC0C (void);
extern void GraphicsFormatUtility_IsCompressedFormat_mE23F74E1C0D8EF955FB7D776C17FD6955FB700DD (void);
extern void GraphicsFormatUtility_CanDecompressFormat_mDC3A7D8AC07ABAC875EACD11F48C5B571E22CEC6 (void);
extern void GraphicsFormatUtility_CanDecompressFormat_m443675E54409D934EE0DC0FDA5CF6D56DE9C4282 (void);
extern void GraphicsFormatUtility_IsDepthStencilFormat_mB9AC0AC27E959CF7C23FDE141E3A4D3561FAC616 (void);
extern void GraphicsFormatUtility_IsPVRTCFormat_m7B1CF5EAD3BAEF83A7B5B198C16F54FC9C081D13 (void);
extern void GraphicsFormatUtility_IsCrunchFormat_mEEE165E8F2D82A469181DA2C4A5C227CCF585DAB (void);
extern void GraphicsFormatUtility__cctor_mA72A657D7B9FB8670567D2CB6B6FA3A1419980D1 (void);
extern void Assert_Fail_mCC76351EF5EAA85F4012330D498CB802861B41BA (void);
extern void Assert_IsTrue_mE42C53B7220324D1FBAFB7AE48A7D8DD7796A663 (void);
extern void Assert_IsTrue_m390B3F48332F46CE76AB45491A60ACDCCF521AAE (void);
extern void Assert_IsFalse_mC11212C418E6B5009F6474AE90FFB24734482E56 (void);
extern void Assert_AreEqual_mA4CD725133015119B0219ABAA6337650814B803B (void);
extern void Assert_IsNull_mB7DAD5AA378E4A36BC1D27B88CFADD31023530BB (void);
extern void Assert_IsNotNull_mB889D13B7935F20A9CE32629C00434099E30D931 (void);
extern void Assert_AreEqual_m718BB4BD31FA3176A3A236F96BC5405EB750D6CF (void);
extern void Assert__cctor_mC2C4583746B3D6E98224F4692EAE5EF17B5F9CC8 (void);
extern void AssertionException__ctor_m01CD9ADC1B0067C20CDC2A0697BBF3969E67FEB6 (void);
extern void AssertionException_get_Message_m4320D1607BDF97D451569837340C8E4A04461089 (void);
extern void AssertionMessageUtil_GetMessage_m5AD26DAEC5DCCEB15C198EF6B70FED9E32BF299C (void);
extern void AssertionMessageUtil_GetMessage_m0785AB2BEBDA81CFE63F87428268C91D63685EB3 (void);
extern void AssertionMessageUtil_GetEqualityMessage_m64D77BB9CA4284DD9561C238BB1F97B566830DBB (void);
extern void AssertionMessageUtil_NullFailureMessage_mECBDB36C0C5433898BC4D3CF0AE55CEFBBCC9A50 (void);
extern void AssertionMessageUtil_BooleanFailureMessage_m1390F2418023DC1717341A836F0F40FBC5801FB4 (void);
static Il2CppMethodPointer s_methodPointers[2109] = 
{
	EmbeddedAttribute__ctor_m93D23E0241AEA98DE88285ECFB378AAD361CDC83,
	IsUnmanagedAttribute__ctor_m0C60E16DF38A964480B9B8C9A714838FED66E804,
	MathfInternal__cctor_m405C235849BE19E6F82F403A518E0E2958F043AE,
	TypeInferenceRuleAttribute__ctor_m356F2A18ACB338DE48EADD04E97C971C554EDDE0,
	TypeInferenceRuleAttribute__ctor_mECD854F0BC27EC215F4BD1C6F0CECA9BEC090E0D,
	TypeInferenceRuleAttribute_ToString_m009A152A07FC88BF49F1C72FC615F2FB7350F2BD,
	GenericStack__ctor_mD21753D674298C09F3684F19DD42680323055586,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	JobHandle_Complete_mDCED35A125AAB37EDDAB2E31C805B4904B614A4A,
	JobHandle_ScheduleBatchedJobs_mCA0E146397E30E31FB57C96DAA1820451886DACA,
	JobHandle_ScheduleBatchedJobsAndComplete_m2F3EDE00C3539E9FD3F0BDB88A83934295D46DF7,
	JobHandle_CombineDependencies_m5B482F42E4C9CAC22FF24C1BF76F3AFB870DBB3E,
	JobHandle_CombineDependencies_m97D94C038D7FAF721612B7BBE2147B05FCFF5C40,
	JobHandle_CombineDependenciesInternalPtr_m5C891AA874B6FE7E553F177DBBD7E5C106C9B7D9,
	JobHandle_Equals_mD29DF760383603EABB2E626975DE8EB2F2A6234D,
	JobHandle_CombineDependenciesInternalPtr_Injected_m73A10C581A7FDBCCB1E71C1EC2F882F94B6640E1,
	JobProducerTypeAttribute__ctor_m562A2FC62E2DF7109DD703C0270B0B372607C534,
	JobsUtility_GetWorkStealingRange_mEFCE7247B469F69E0590111B435E0010BFB8F73A,
	JobsUtility_ScheduleParallelFor_mEF775B0FF09686D0525DDFA9ED95A72EEDEC995C,
	JobsUtility_CreateJobReflectionData_m1A2233D7CF938F4C9ECE371DF271C8EC20D12664,
	JobsUtility_CreateJobReflectionData_m17265DED7C2DBB0B2130896E2B8AC4CF5BD7FCE7,
	JobsUtility_InvokePanicFunction_mDD3D5F9B1F0A26924CC82DB52E0561D32DC8E255,
	JobsUtility_ScheduleParallelFor_Injected_mFB3DF4AB62DD13FB4CE2311E7441DCE239129A64,
	JobScheduleParameters__ctor_m5FFED3B28A1FA2C3EC7D1C50A7D7E788C411CE04,
	PanicFunction___ctor_m17297514778BEA5F35E562880A6C317CF34856DF,
	PanicFunction__Invoke_m1AF64BA1DC405B9451341076B6E099CBB173A12A,
	Il2CppEagerStaticClassConstructionAttribute__ctor_m124F17545F01CC678CE74870FC9EE5D4891EE935,
	IgnoredByDeepProfilerAttribute__ctor_mE21E2304501A1CE16AE545436B1FF557CE51C1DA,
	ProfilerCategory__ctor_m59B0D65E2CE7D723F30A4FAA5796A1CBE105B298,
	ProfilerCategory_get_Name_mFED02A355294B8B0365E03D12BC1299E37442894,
	ProfilerCategory_ToString_m091164890366F89EFBFC0FF811B897C234B67541,
	ProfilerCategory_get_Scripts_m84B0F774438E512DFAF8FD21E0FBE76F00419AFE,
	ProfilerCategory_op_Implicit_m441AE38B56781EE2D3F0865F65C81A77BEC6D76B,
	ProfilerMarker__ctor_mDD68B0A8B71E0301F592AF8891560150E55699C8,
	ProfilerMarker__ctor_m5958260A54C3A7F358A71AACDF47BA28178A5AB7,
	ProfilerMarker_Auto_m133FA724EB95D16187B37D2C8A501D7E989B1F8D,
	AutoScope__ctor_m7F63A273E382CB6328736B6E7F321DDFA40EA9E3,
	AutoScope_Dispose_mED763F3F51261EF8FB79DB32CD06E0A3F6C40481,
	DebugScreenCapture_set_RawImageDataReference_m935F402BCD29599C153DF8B982FAAE26FC1F9F24,
	DebugScreenCapture_set_ImageFormat_mEB839CF83D4271BEBDE907158D8F2FBC9CB88FFE,
	DebugScreenCapture_set_Width_m4C018D3EECDEDCA9EED150FE159218A403210451,
	DebugScreenCapture_set_Height_m079B049644D69F95F746269B437D8DEBEC3701E7,
	ProfilerUnsafeUtility_CreateCategory__Unmanaged_mA424CE7A67002B0B92C43C6B1D151FC1FF858A56,
	ProfilerUnsafeUtility_GetCategoryDescription_m7C69231967E0B9EDD5D41B362A352897373C3391,
	ProfilerUnsafeUtility_CreateMarker_mC5E1AAB8CC1F0342065DF85BA3334445ED754E64,
	ProfilerUnsafeUtility_CreateMarker__Unmanaged_mB19C1DFE1BC2155D3744E5A79DE73418234DDA22,
	ProfilerUnsafeUtility_SetMarkerMetadata__Unmanaged_m3D9F5F66E63969EF79D5F7548DB4464CEE9181C2,
	ProfilerUnsafeUtility_BeginSample_mB5106F4E7ECEF54906545665ED23928D14F5FCA7,
	ProfilerUnsafeUtility_EndSample_mFDB4EFB160A9CB817D2F8ED21B88693616B27409,
	ProfilerUnsafeUtility_CreateCounterValue__Unmanaged_m0CF807211082E8E242EFD66417B15AC600C86C05,
	ProfilerUnsafeUtility_Utf8ToString_mB64501855C1C01B54BFEDFDE0D2CAEAD2A5353E8,
	ProfilerUnsafeUtility_GetCategoryDescription_Injected_m1971DD6E5FB42E96C147FE3B55870F2947E050AC,
	MemorySnapshotMetadata_get_Description_mF22499F79675403674B8B45F78C23E7BEC2BC88D,
	MemorySnapshotMetadata_set_Description_m00757DCA7E64343633755932EC6F15A1B61A4621,
	MemorySnapshotMetadata_get_Data_mB61CE35A8C57F83EE5582E10A0B306CBC0BA3624,
	MemorySnapshotMetadata__ctor_mE8ED135D7BD5CBE5D2B849B160B3D7EBBE5B468B,
	MemoryProfiler_PrepareMetadata_m329170F1BBB5E061A086FC61FC03EF452094F6CF,
	MemoryProfiler_WriteIntToByteArray_m788E8C96D88066FF17DA6A86FF28EBD14CF69C7A,
	MemoryProfiler_WriteStringToByteArray_mCC3816A5BEA5C0C7D672D17DFC3985B33C0FAEC7,
	MemoryProfiler_FinalizeSnapshot_mDB37521587585D8FF80D9E32764F2BB33BBB1FAD,
	MemoryProfiler_SaveScreenshotToDisk_m864CDEF0A856BC92783884E8D5F92ADEE88816D5,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeArrayDispose_Dispose_mC90E863B89568E988E0F8FB8E79596A72C5EE061,
	NativeArrayDisposeJob_Execute_m2F3CAEB5BFACF52C44D2A2485554D88018650A7F,
	NativeArrayDisposeJob_RegisterNativeArrayDisposeJobReflectionData_mDDD42E5EAFDCF8373611DFF7CEDB64712DA55895,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SharedStatic_GetOrCreateSharedStaticInternal_mA2EDCD03A23E5C9D8AC312E73F81232E126DA304,
	NULL,
	BurstRuntime_HashStringWithFNV1A64_m3E38919BF51D34D084240B8B6EFB6A753411A335,
	NULL,
	NativeContainerAttribute__ctor_m9249B57B7D6F9E1D425EC32392FB975B8568AD9B,
	NativeContainerSupportsMinMaxWriteRestrictionAttribute__ctor_m340DBE8A3B007A450B4643E7350C29D92514B782,
	NativeContainerSupportsDeallocateOnJobCompletionAttribute__ctor_mBE27310D46174944DF46D01B8AE4BBE69DA94729,
	NativeContainerSupportsDeferredConvertListToArray__ctor_m40F59B7627032F45D2ED023BA9525EEF40660C27,
	WriteAccessRequiredAttribute__ctor_mB36E4CF6313543DEB0DFA64575446C5ABB6F4C8B,
	NativeDisableUnsafePtrRestrictionAttribute__ctor_m8E1BE43B9791FD4E5790060D6506003D35F081CC,
	NativeDisableContainerSafetyRestrictionAttribute__ctor_mA6D239ECE04E6D05432C3ACB98ED6CA92C4371A1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnsafeUtility_MallocTracked_m618762A86F170FB10656114217D3D125D60CE297,
	UnsafeUtility_FreeTracked_mB96B3C035F2CD1517BF8C29218CBD7B710B3279A,
	UnsafeUtility_MemCpy_m5CEA91ACDADC522E584AE3A2AB2B0B74393A9177,
	UnsafeUtility_MemCpyStride_mD7836B76B4F1E7F30DFC859D4E6D7242AFD27C90,
	UnsafeUtility_MemSet_m4CD74CD43260EB2962A46F57E0D93DD5C332FC2B,
	UnsafeUtility_MemClear_m6C4377117084A11A667A567BC2F5E606A632A7C1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	BurstDiscardAttribute__ctor_m0AC3131F7C5B377DCA604CD7BB8AC4AA4E161033,
	BurstCompilerService_GetOrCreateSharedMemory_m1293EB3119CBEE41DBCC0E3B2235601BD927BFE6,
	NotNullAttribute__ctor_mBD764326957BF5036FB7217B794F6B48B73A6B51,
	PureAttribute__ctor_mEAFD7538811204E5F43B816F28B63FA197A6A09A,
	SortingLayer_GetLayerValueFromID_mEB8A5234102CD7B2C6158661A931EA120A38707C,
	AnimationCurve_Internal_Destroy_m240B298D0A13EEC1652955C4BDCDBE9B7B2EE296,
	AnimationCurve_Internal_Create_m638BB04F32C3013A7CA810879E46FD9F32E9FE05,
	AnimationCurve_Internal_Equals_mEBA61732FE57654C4E3AF5B317918D1641E36050,
	AnimationCurve_Finalize_m803AC16166EE497C4DFA996B15692D91F4D04C3C,
	AnimationCurve_GetHashCode_m1AEBC4C469357E1FA2541CC4B8D2B39C284CF8F6,
	AnimationCurve__ctor_mEABC98C03805713354D61E50D9340766BD5B717E,
	AnimationCurve__ctor_m0D976567166F92383307DC8EB8D7082CD34E226F,
	AnimationCurve_Equals_mE47717A57755581C546986799C9DBC64D98928A9,
	AnimationCurve_Equals_mC44657401804A22DCA648BD8495FC4E8A2F088A3,
	Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34,
	Application_get_isFocused_mFEEC52E355AA7AAA6B7250520CA544D80BE77524,
	Application_get_isBatchMode_mDE2DA11B2DAC8D8239BACD75A56A6EE6BAA4DC36,
	Application_OpenURL_mE748FA4D503715DEE12BCA4BDD8A5305AE41DB89,
	Application_get_platform_m59EF7D6155D18891B24767F83F388160B1FF2138,
	Application_CallLowMemory_m89AE742136DDE1754212C8B3092481F3DFA41CC3,
	Application_HasLogCallback_mEF8E25750ECCAAB596ACBF7CF75B2BD43B3157CC,
	Application_CallLogCallback_m8E71361EED127C3D753168559078020878A0592C,
	Application_Internal_ApplicationWantsToQuit_mB7CD318CF9B29D34E26D2E25F460957D41D26854,
	Application_Internal_ApplicationInit_mE781BDF7FB93F32B339F1B52300088309CC21C79,
	Application_Internal_ApplicationQuit_m4E0E8DE66A2E435A1A4EFF21CBCD2132895AFF84,
	Application_Internal_ApplicationUnload_mBACA14D6291E2FD79766019FBA0A48EBE16AFFAB,
	Application_InvokeOnBeforeRender_mBD6251514058981E187B0D695C02CB00B983B963,
	Application_InvokeFocusChanged_m85307C7E83A7D2314E44CF0B505D1158B4A1D58A,
	Application_InvokeDeepLinkActivated_m9484D7A6C4CB0DD73B83D5F442FB5620CE9F1136,
	Application_get_isEditor_mEAC51E3ACE6DCE438087FB14BD75A3C219D354D0,
	Application__cctor_m5B1DED32A96960B4E37EA614575B6366BCDFD549,
	LowMemoryCallback__ctor_m3B5E06F22A115B898C51A5C4B20166E2A92E0375,
	LowMemoryCallback_Invoke_m3B2AE4EC5556D1D98237EF965477487AE4C7D708,
	MemoryUsageChangedCallback__ctor_m01FF11979DFBEDA8CC5051D939338C23C5BF929C,
	MemoryUsageChangedCallback_Invoke_m3C0E629B4D924D71D84DED6B9C78BD62263F9F1F,
	LogCallback__ctor_m327A4C69691F8A4B01D405858E48B8A7D9D2A79D,
	LogCallback_Invoke_m88EACBF170351AE6FC8E6F5154CD09179D67BB47,
	ApplicationMemoryUsageChange_set_memoryUsage_m331F962287453AC69EEE1222C0F11D222F7B2957,
	ApplicationMemoryUsageChange__ctor_mD1B7299FCDDF14B479AF66AFDDFC2D710AC6A3C0,
	BootConfigData_WrapBootConfigData_m47132663C7FB94C3CA9627E5955C1848165AEF72,
	BootConfigData__ctor_m67B06923C40A5363192040A89BB92479B4B74A9A,
	Camera__ctor_m6D8568D99066C871656AC9A712A43D96A413BEBA,
	Camera_get_nearClipPlane_m5E8FAF84326E3192CB036BD29DCCDAF6A9861013,
	Camera_get_farClipPlane_m1D7128B85B5DB866F75FBE8CEBA48335716B67BD,
	Camera_get_depth_mDF67FFF8ED61750467DFC4C6D8F236850AD1BB1D,
	Camera_get_cullingMask_m6F5AFF8FB522F876D99E839BF77D8F27F26A1EF8,
	Camera_get_eventMask_mEBACB61FFA0C8AAF3495454BABA50EE70655290A,
	Camera_get_clearFlags_mA74F538C124B391EF03C46A50CA7FF7B505B7602,
	Camera_get_pixelRect_m5F40F5C324EB252261F66962411EE08CC4BE39E7,
	Camera_get_targetTexture_mC856D7FF8351476068D04E245E4F08F5C56A55BD,
	Camera_get_targetDisplay_m204A169C94EEABDB491FA5A77CC684146B10DF80,
	Camera_WorldToScreenPoint_m6612AF37FFBBACC568877D4AA3AD5F11C76D9657,
	Camera_WorldToScreenPoint_m26B4C8945C3B5731F1CC5944CFD96BF17126BAA3,
	Camera_ScreenToViewportPoint_m8907015773080F63D1034CEDEDEA4AF14FB2F3C5,
	Camera_ScreenPointToRay_mA8EEC93B0731C859D2FF29D7DECFB806E3D9F0CC,
	Camera_ScreenPointToRay_mA27CE345E80542143237233D503A71392594AA9B,
	Camera_ScreenPointToRay_m2887B9A49880B7AB670C57D66B67D6A6689FE315,
	Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF,
	Camera_get_current_m25217A02CB09E3BD50E3E0327879E870AD58C6C3,
	Camera_GetAllCamerasCount_m349F020DACEA1A160FB99917EFAED041316D4CCB,
	Camera_GetAllCamerasImpl_m9267137E1EBF23B51A2A5842C0DA9A333142E29C,
	Camera_get_allCamerasCount_m7B9CAA9E8F2BC5587942A4CCBF4D6CA1FFD01BDC,
	Camera_GetAllCameras_m55D302710121EEBC17D2F6EE4AE975C37ECC53F4,
	Camera_SetupCurrent_m564B3A5A985E2DF0C7CE924FD93CD9545002C515,
	Camera_FireOnPreCull_mE55D48DC9F94241A79F59C53FCEC0E6B81FB2CA6,
	Camera_FireOnPreRender_m543170A0D796CFF44B97DAC68C2208F061143ADA,
	Camera_FireOnPostRender_m5C39B4A52A27EE6D9FC15A44518B60CEEDCA6EF5,
	Camera_get_pixelRect_Injected_mA85B24A9EDBD279189D560BD46B611E69C8EEDA3,
	Camera_WorldToScreenPoint_Injected_m3D83428A7AC8033EB4D357DF55BFA2ED652DEB64,
	Camera_ScreenToViewportPoint_Injected_m08D0DAE62A0BAF0443871DAF87FE4B3F18CC45C2,
	Camera_ScreenPointToRay_Injected_m9B7E1A86A79D7578D03345360E86AED3C8D09C59,
	CameraCallback__ctor_mB48D13F30E749B551E4692E4F2D762C375F62B41,
	CameraCallback_Invoke_m67E54F44AA3C83F0D9EF47A5FBC6AF43B0B26362,
	CullingGroup_SendEvents_m90EC6607039ADA3126DD4650881B3E38BA30D4C1,
	StateChanged__ctor_m1D704B739C7C0F0D91873D24D10A92942826F0C9,
	StateChanged_Invoke_m050DE641598E0C558A8CC865EAD679194581182C,
	ReflectionProbe_CallReflectionProbeEvent_m10D7A2D70D6814FD80DAEC78E09268D7ADA98C7E,
	ReflectionProbe_CallSetDefaultReflection_m7CF493CA345676C58B735C0AE506A248037BE89B,
	ReflectionProbe__cctor_mD074E46A055B388464E1D0B487885ED8F1887A4A,
	DebugLogHandler_Internal_Log_m20852F18A88BB18425BA07260545E3968F7EA76C,
	DebugLogHandler_Internal_LogException_mC3895F1E5B79BD8151B3F94462F5516A68AB26AF,
	DebugLogHandler_LogFormat_m216B169EF9B669F2ED4C59F6B9F326D4EBBDF821,
	DebugLogHandler_LogFormat_m21A399AC8FD346A226DADB661E8009DCA79D0DE8,
	DebugLogHandler_LogException_mF66A663A86BF5D412BC9A4B3F92DA5CF61049F08,
	DebugLogHandler__ctor_m1078DDBE735090C0215BFDF2409A376F10F7A070,
	Debug_get_unityLogger_m4FDE4D41C187123244FE13124DA636BB50C9C1E1,
	Debug_ExtractStackTraceNoAlloc_mEA19F1BD13F74CCE53CED8FA2C07380772C58FB6,
	Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB,
	Debug_Log_m06155ED25645EBBC06B4C8F05235EF41B1489C7E,
	Debug_LogFormat_m37A43E151078921783B62F980955A0EAFFA4CBA6,
	Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2,
	Debug_LogError_m94F967AB31244EACE68C3BE1DD85B69ED3334C0E,
	Debug_LogErrorFormat_m96690322C941D23A125E5769C9803606859A707C,
	Debug_LogErrorFormat_m13C2B705E2899F85833797AA48E8416052FFAD5A,
	Debug_LogException_mAB3F4DC7297ED8FBB49DAA718B70E59A6B0171B0,
	Debug_LogException_mD4CF3A9C64D8D4BA0570D529E705D134A9A5E498,
	Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9,
	Debug_LogWarning_m23033D7E2F0F298BE465B7F3A63CDF40A4EB70EB,
	Debug_LogWarningFormat_mD8224DEBCB6050F4E2BF55151F0C6A29B87DEFBC,
	Debug_LogWarningFormat_mEC3E8902D2EF0D6CDC7D2643D75DF8A7A6F84713,
	Debug_Assert_m6E778CACD0F440E2DEA9ACDD9330A22DAF16E96D,
	Debug_Assert_mA460392021AC0A8210C9081E3C1C9652DBF32BF6,
	Debug_LogAssertion_m71A412A496EAB476FFF6253618C706B2F1E4AFF0,
	Debug_LogAssertionFormat_mBA7469D6C6033C5D45F9B7F2578BFDE7EF1DB578,
	Debug_CallOverridenDebugHandler_mB78E42B7792823F50830A325BB1CF50C45FEAA8B,
	Debug_IsLoggingEnabled_mAB371F62AA458AC5D6CF25ACD58A1C951F3EFD01,
	Debug__cctor_m9D586A54F5AF4804DA5FEC7549187109B05F7831,
	NULL,
	NULL,
	ExpressionEvaluator_EvaluateDouble_m71E6969B279D8C6CBA9F791C72C7060723485285,
	ExpressionEvaluator_InfixToRPN_m9FBA3B21BB31EE4FA9E386D4BEBB209F6639E8E5,
	ExpressionEvaluator_NeedToPop_m072F0CF46B080F707C0A006F2B799AF147A10926,
	ExpressionEvaluator_ExpressionToTokens_m747EBEA2A4BDBA0F16E4284CFB0A926E6A4E350D,
	ExpressionEvaluator_IsCommand_m284D94B1E0B6A7D150CD0E10685295DE3EBFFC51,
	ExpressionEvaluator_IsVariable_m911973EE168593DAF621784B2269EEEBCFCC7FAC,
	ExpressionEvaluator_IsDelayedFunction_mDFF1026B946A73CBBE83D9702C5D5CC5B22A693C,
	ExpressionEvaluator_IsOperator_m2E59B892CAD05E19661608528FC1726A089678E8,
	ExpressionEvaluator_TokenToOperator_mE2F2E3298DF6B1EFE3208CEE9D0C0FFB611EECD5,
	ExpressionEvaluator_PreFormatExpression_mFB5CD012219ADB8F4285CC17959B55C373F4D2B4,
	ExpressionEvaluator_FixUnaryOperators_m6E35555535A294B25FFB7D3AD8D5497CB66E3429,
	ExpressionEvaluator_EvaluateOp_m23DBADDB2BEDC645C36EB6FE80931BBD7D9B2094,
	NULL,
	ExpressionEvaluator__cctor_mF48F33D70CD19BFAE74ACFF4212A1333E7260EE5,
	Expression__ctor_mCA43087EA66CD3F2E0E8F34CB1A3C65918DCB401,
	NULL,
	Expression_Equals_mE905BB68355581FA84E9B62547AA78B07FB39B48,
	Expression_GetHashCode_m032D8211AB706936EFE29A6F0116CC8DB419FA28,
	Expression_ToString_mDE463408848472DB7DD0931BE220E89DF3A435C3,
	PcgRandom__ctor_m60661E44F818E77DABECCC669F8802633503DF1E,
	PcgRandom_GetUInt_mE5C9062173864BB7154CA25573D2835FA70589CA,
	PcgRandom_RotateRight_mE8B5A561CCF8FAA9A6D56E27DF9BD691121EBB61,
	PcgRandom_XshRr_mDF8F5B670486970BB8896219DD6887AFC2806EDF,
	PcgRandom_Step_m0C0C0E3BED534CFC75FC9DF507A708043B1278B0,
	Operator__ctor_m110EBC47E0EBC2AC1D40E3737BA9A164222B9C71,
	U3CU3Ec__cctor_m587D04CDB69A94B5029BB4AAD5DB0993E512B44A,
	U3CU3Ec__ctor_mE7C64602A710F6CEF0B7768C9A2371250224A9F6,
	U3CU3Ec_U3CExpressionToTokensU3Eb__14_0_mED0A7EEADBF72B4F54461A0D1E6C939772258CAC,
	Bounds__ctor_mAF7B238B9FBF90C495E5D7951760085A93119C5A,
	Bounds_GetHashCode_m59C79B529D33866FE45FEFC0C69FBD3B4AC7E172,
	Bounds_Equals_m93E0B9D24C73E57A6FABB9D312101D48183C88CC,
	Bounds_Equals_m615135524315743D29633C33B6C8B16B754266DB,
	Bounds_get_center_m5B05F81CB835EB6DD8628FDA24B638F477984DC3,
	Bounds_set_center_m891869DD5B1BEEE2D17907BBFB7EB79AAE44884B,
	Bounds_get_size_m0699A53A55A78B3201D7270D6F338DFA91B6FAD4,
	Bounds_set_size_m950CFB68CDD1BF409E770509A38B958E1AE68128,
	Bounds_get_extents_mFE6DC407FCE2341BE2C750CB554055D211281D25,
	Bounds_set_extents_m09496358547B86A93EFE7BE6371E7A6FE937C46F,
	Bounds_get_min_m465AC9BBE1DE5D8E8AD95AC19B9899068FEEBB13,
	Bounds_get_max_m6446F2AB97C1E57CA89467B9DE52D4EB61F1CB09,
	Bounds_op_Equality_m5D72DDC8A0C2493FAFD00E88BC3B21D600CB5B33,
	Bounds_op_Inequality_mBE4883EFEDADB40B7243DC1F19BD01B5908CD2E0,
	Bounds_SetMinMax_mB5F7DDF18EDB7F3F25FA6D2B36824F28978C540F,
	Bounds_Encapsulate_m1FCA57C58536ADB67B85A703470C6F5BFB837C2F,
	Bounds_ToString_m1BCCCC8C6455A77DE5C964968C33305EF7A4A0D2,
	Bounds_ToString_m085531A8E800327829FCD48DEA671A4A0B8D21CA,
	BoundsInt_get_position_m0A58811AA258865B63CCFEDD693E278367411B4B,
	BoundsInt_set_position_m72954A6270A27FCC62B2B32290CE32D5784A837E,
	BoundsInt_get_size_mE7C4A0C3BF45CEA7A28ABF98E2C15CB69EF3A32C,
	BoundsInt_set_size_m518DA559D9E67DE136B3CCB37470E147FA088CE1,
	BoundsInt__ctor_m93F7EDF326B3BA01465FA229F6CEED0ED48D32FF,
	BoundsInt_ToString_mACB0BAF0A766690D30CA39FF52EA783583653B3F,
	BoundsInt_ToString_m0505A1F9CB063D0F588D7212D695894D5B1460D8,
	BoundsInt_Equals_m4C99DB2D3AD7DD9E0A75562F0AE24A14AE63587D,
	BoundsInt_Equals_m143E0673DA604FDEBBF40115D50BE078E343F1E6,
	BoundsInt_GetHashCode_m9740EA5B8C8E9B4DD47D9D6E619D61F5B99115CC,
	Plane_get_normal_mA161D94E6F7327BC111007C880B76E1731729EFB,
	Plane__ctor_m2BFB65EBFF51123791878684ECC375B99FAD10A2,
	Plane_Raycast_mC6D25A732413A2694A75CB0F2F9E75DEDDA117F0,
	Plane_ToString_mF0A98DAF2E4FA36A98B68F015A4DE507D8BB3B5A,
	Plane_ToString_mE12B74C757E52A84BE921DF2E758A36E97A11DDA,
	Ray__ctor_mE298992FD10A3894C38373198385F345C58BD64C,
	Ray_get_origin_m97604A8F180316A410DCD77B7D74D04522FA1BA6,
	Ray_get_direction_m21C2D22D3BD4A683BD4DC191AB22DD05F5EC2086,
	Ray_GetPoint_mAF4E1D38026156E6434EF2BED2420ED5236392AF,
	Ray_ToString_m06274331D92120539B4C6E0D3747EE620DB468E5,
	Ray_ToString_mA76F7B86876505F674F3E20C18C8258103622C10,
	Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23,
	Rect__ctor_m503705FE0E4E413041E3CE7F09270489F401C675,
	Rect__ctor_m5665723DD0443E990EA203A54451B2BB324D8224,
	Rect_get_zero_m5341D8B63DEF1F4C308A685EEC8CFEA12A396C8D,
	Rect_MinMaxRect_m540D2DD8C255D276AD4AE06D9CCA2A667EFA39E5,
	Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB,
	Rect_set_x_mAB91AB71898A20762BC66FD0723C4C739C4C3406,
	Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49,
	Rect_set_y_mDE91F4B98A6E8623EFB1250FF6526D5DB5855629,
	Rect_get_position_m9B7E583E67443B6F4280A676E644BB0B9E7C4E38,
	Rect_set_position_m9CD8AA25A83A7A893429C0ED56C36641202C3F05,
	Rect_get_center_mAA9A2E1F058B2C9F58E13CC4822F789F42975E5C,
	Rect_get_min_mD0D1BABF9C955D2D9CCA86E257B0783ACDEE69AC,
	Rect_set_min_m6557D7D73C6F115CA7C92E38C88EA9E95FC89253,
	Rect_get_max_m60149158D9A01113214BB417AA48CEF774899167,
	Rect_set_max_mAD2D6D5DC1F5A6E69A0A0BD7E34C209F91C381F0,
	Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9,
	Rect_set_width_m93B6217CF3EFF89F9B0C81F34D7345DE90B93E5A,
	Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8,
	Rect_set_height_mD00038E6E06637137A5626CA8CD421924005BF03,
	Rect_get_size_mFB990FFC0FE0152179C8C74A59E4AC258CB44267,
	Rect_set_size_m346E4F7077E5A1C0F4E21966232CD726CB9E6BAA,
	Rect_get_xMin_mE89C40702926D016A633399E20DB9501E251630D,
	Rect_set_xMin_mA873FCFAF9EABA46A026B73CA045192DF1946F19,
	Rect_get_yMin_mB19848FB25DE61EDF958F7A22CFDD86DE103062F,
	Rect_set_yMin_m9F780E509B9215A9E5826178CF664BD0E486D4EE,
	Rect_get_xMax_m2339C7D2FCDA98A9B007F815F6E2059BA6BE425F,
	Rect_set_xMax_m97C28D468455A6D19325D0D862E80A093240D49D,
	Rect_get_yMax_mBC37BEE1CD632AADD8B9EAF9FE3BA143F79CAF8E,
	Rect_set_yMax_mCF452040E0068A4B3CB15994C0B4B6AD4D78E04B,
	Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B,
	Rect_Contains_mB1160CD465F3E9616AA4EED72AFFD611BD8D2B6B,
	Rect_OrderMinMax_mCF0D63521F398D682E285F8795781C1D2A3418D9,
	Rect_Overlaps_m5A540A24DAD3327006A3A2E209CC17992173B572,
	Rect_Overlaps_m3F0BA2C8BB81491978B21EB21C8A6D3BBED02E41,
	Rect_op_Inequality_mB5D7316EB50B1DDA9324F4BE6741DFF6A673137D,
	Rect_op_Equality_mF2A038255CAF5F1E86079B9EE0FC96DE54307C1F,
	Rect_GetHashCode_m8E55539476EA6B7A6E0CEC5F980227CD15B778F1,
	Rect_Equals_mD7EB2046512E4A46524A7ED929F1C38A32C408F8,
	Rect_Equals_mE725DE506D3F1DB92F58B876BDA42AACD4D991B5,
	Rect_ToString_m7BF74645745862DA4751965D0899F94376F77F10,
	Rect_ToString_mA9EB8EC6A2E940240E0D0ECFA103B9B7EFB3D532,
	RectInt_get_x_mA1E7EF6DEAD2E900D7D56B7A3957C05081EBA9CA,
	RectInt_set_x_m2D2F3A87E9899A29444DBDD0BB11CB19F13AA075,
	RectInt_get_y_m440422264E6FCAA91E01F81486A78037AC29D878,
	RectInt_set_y_m45D5C1D817698266BED66D41A705956A1571858D,
	RectInt_get_width_m6B7B2FB764EAE83B7F63E7F77FA33973606761A7,
	RectInt_set_width_mCD96AA9D096114147F8411A340CE4AD3476DCD4C,
	RectInt_get_height_mE25FB938714942D7A3BA0B3C21BC5CB913D5709C,
	RectInt_set_height_m823A353A80B8B5180AEDF968A6E85B9D9B75C1C6,
	RectInt_get_xMin_mA5FB5AF1133380E080CF750D21327DE27EADEE1B,
	RectInt_get_yMin_m6914C2254158DF797E20E381626DC08A2700147B,
	RectInt_get_xMax_mBA05CE52847E3D3CB8295055706B3E0D4350E9F0,
	RectInt_get_yMax_mAE5D758A1241F7722F8FB9B46861583F76C8FE44,
	RectInt__ctor_m6E8B3A6C7EE11257A6B438E36274116FE39B5B42,
	RectInt_Overlaps_m9E82E1C8BFDA3297221E5FDC8B8773AD3F50C4EE,
	RectInt_ToString_m7EC8BB4830459B8CF5BF3032E9A526A6EE18D386,
	RectInt_ToString_m7EAE8CA8D77B7D6DDD46B61D670C71046006B92F,
	RectInt_Equals_mE9EA164664CA30C1C099EFB658D691F55A793B96,
	RectOffset__ctor_m7DB8E8EDDB814824299F9EF661DAFA32854F0CC9,
	RectOffset__ctor_m0711AF5DF27B8F3EC6CAF54755CDE46B76C00DBE,
	RectOffset_Finalize_mF43E63CF652D658B255D033A099CAED2C4262FBC,
	RectOffset_ToString_m8FAEA1A73C37F5827D0444EB4A8C5BA0781506F8,
	RectOffset_ToString_mEE066023D51F743F0370E3547E7145FAE86B00BC,
	RectOffset_Destroy_mFFB78BB790E3BAFE464C29DB5F396FB2FA8E6E70,
	RectOffset_InternalCreate_m6570FA60917650C43BBA7229F56215B92EC69873,
	RectOffset_InternalDestroy_mFFF37ED69D9FDAE656F347FD3E6E36F7BEB0ACC1,
	RectOffset_get_left_mA406D7AFF76E48507EF143CDB1D157C4D5430D90,
	RectOffset_get_right_m07C826B0BC79B0CBC01F5FF489D456C553F047BF,
	RectOffset_get_top_m82E49FB93A5BD417131136F5A7DBA0F251F10263,
	RectOffset_get_bottom_mDF9C1EC125F94245D5532C34FCFB65BE0F2A9D0B,
	RectOffset_get_horizontal_m5C1795C027E4987A8532DC27D88FB3B9FFEC1352,
	RectOffset_get_vertical_m43E46D9F313BB617044184A65350E6498A0709F0,
	LightingSettings_LightingSettingsDontStripMe_mA06509B760BA1A7DDB4CC3CE584DC2C3E1FE0237,
	BeforeRenderHelper_Invoke_mDBB6C0ECACC207FD3A4D354177C93AC24B2184B5,
	BeforeRenderHelper__cctor_m1BA6EA4EC2C3720A5966A677530FC51FE683EF44,
	CustomRenderTextureManager_InvokeOnTextureLoaded_Internal_m18ECD151BFA0492A839269B69A1CDCA444D0FF1F,
	CustomRenderTextureManager_InvokeOnTextureUnloaded_Internal_m86253D77C4A01969A81716A470F3D67B079D58DC,
	Display__ctor_mD4B0D1F494D3472058E47A940600FAC93D68D1DF,
	Display__ctor_m0F78D1B697F3D82FFD274A6AA716E96FA6A4B156,
	Display_get_renderingWidth_mD23656E7A8270AF79B0EF4124959B3E18BEDF0C7,
	Display_get_renderingHeight_m0627691B7B2D7037A856597E43BFDF1F0CC2B0B8,
	Display_get_systemWidth_m515BF32963F049A9710F11CE8F6445BAA63D7155,
	Display_get_systemHeight_mC20ADD124FBEF94796F736684A3AF4D0AA569FC7,
	Display_RelativeMouseAt_m39BFC645EB81B36E705F1E9D3116EFC3995B9FE7,
	Display_get_main_m6EB21BF1B5F545173BEECDB8F1F547FD74B4895C,
	Display_RecreateDisplayList_m8FB39A95A6DAE41CD0194BECB71B80B79A2E6B88,
	Display_FireDisplaysUpdated_mF99580245BC4B28765E06B57611C52A0497E9994,
	Display_GetSystemExtImpl_m56DF5AA95B2433409A37AB7BECFBC1538A5C112D,
	Display_GetRenderingExtImpl_m4AF3F04825972A502967DFDE02E2B723CA689E46,
	Display_RelativeMouseAtImpl_m6950C34B75D0FD275E9ACC264EC41823081EEFC8,
	Display__cctor_mC3998EEB2068A145697B8B6E65DF16BA7FED52CB,
	DisplaysUpdatedDelegate__ctor_mCF8A6ABE7CCC32908022E0AB13ED3BF1EBD2719D,
	DisplaysUpdatedDelegate_Invoke_m4E0A3493E2C3A9CE0D5B5B3FD40A8389BFBA1853,
	RefreshRate_get_value_m7F8BB0D20DAB1EF882F1FC97E0C7618FCD319561,
	RefreshRate_Equals_m16184432DA438F6BAF730078987262C7DE97913C,
	RefreshRate_CompareTo_mD74AD821FF0DA633F9719E2B824C84E77E443D66,
	RefreshRate_ToString_m59B00D8F20B6DAB001CD394F23653AC693DF8047,
	Screen_get_width_mF608FF3252213E7EFA1F0D2F744C28110E9E5AC9,
	Screen_get_height_m01A3102DE71EE1FBEA51D09D6B0261CF864FE8F9,
	Screen_get_dpi_mEEDAA2189F84A47BD69D62A611E031D5C59CFE8E,
	Screen_get_fullScreen_m9B3B4F9FB4B6FD6E1AEF626736C4462A2FD7407C,
	Graphics_Internal_GetMaxDrawMeshInstanceCount_m3CA9D7C4AD2A389C9F4C95B0FF04B6903B5A9048,
	Graphics_Internal_SetNullRT_m9D3FB09C1C0176C01B5B9D8299FB29FCC68550A9,
	Graphics_Internal_SetRTSimple_m8A08CC6791B24AD4ED3262FC14F65F71FBDE7C01,
	Graphics_SetRenderTargetImpl_mFA652AAB6B11A0B3740395807051662E51FD9F97,
	Graphics_SetRenderTargetImpl_m20519EB34D9C8900A5BFD438C2B97C69724EFE0B,
	Graphics_SetRenderTarget_mCD697A459B0374E91E854C7DB850D4DE9B3523C2,
	Graphics_SetRenderTarget_m995C0F14B97C5BF46CCF2E7EF410C1CC05C46409,
	Graphics__cctor_m37525F50A7B79CDD75A1DFA2AEFFA998BCBDD94C,
	Graphics_Internal_SetRTSimple_Injected_m049F9654EADC509446BE61023C735FE7BDEA590E,
	GL_Vertex3_mEA9925548F447556F7899E69604B787EE57F6978,
	GL_TexCoord3_mD95B6C3D77105453E0A01B1A29DFDDDF14BB7111,
	GL_TexCoord2_mBD9A9E6D397F8669FAE40DA950AB1FD7D674D1FB,
	GL_ImmediateColor_m8311AC8E6A292EE6247F6533785F4DC0CB1FA242,
	GL_Color_mE2D27D8FCA9B5E07ECC15574BCBCBA16E6E0CB3E,
	GL_SetViewMatrix_m7916323155AF8AD717E539A8E0D91391755E9278,
	GL_set_modelview_mCAE007CC5BE38316397954370950EB43991FBBD4,
	GL_PushMatrix_mB505DD9B224528266FCADC716A16343838105A09,
	GL_PopMatrix_mCE0D33302104D1168B6382136039E979E8C02855,
	GL_LoadOrtho_mE86AB2DBBC5C2BA67E7B743A2352E61C372CEADC,
	GL_LoadProjectionMatrix_m4310EBD83BF1858124306A227C18FF52152718BD,
	GL_GLLoadPixelMatrixScript_m3B24502667E2873BC00326BC5EA7CB1974E1B4CC,
	GL_LoadPixelMatrix_mF1C5A4508C5F110512C116A5DDE7AB0483FE961A,
	GL_Begin_m17A70A7A3C161D8A127C11BDC5FC393392AB70C7,
	GL_End_m6CE9D562B738075125F901B1D5254520EC30AB36,
	GL_GLClear_m889A7C7A3406665D8631A4946DD478CE1DADE1C4,
	GL_Clear_m49FFE8774C00488FD65A2638F7D02E67325923FA,
	GL_Viewport_mA93DBAE1018A273684A07C8B8B444EFFBF2B38B7,
	GL_SetViewMatrix_Injected_m13A8BCB6F2D98B7172E2FF190A6F2E7FE3AB4029,
	GL_LoadProjectionMatrix_Injected_mFB43E70DB0AA3CF3FD0B212495D125844579816C,
	GL_GLClear_Injected_m27F584AF9138B297159C619DEEEB427E722D2DED,
	GL_Viewport_Injected_m18D18FFCCD4FDD941820797CFBE7270ECFF27D4E,
	LightProbes_Internal_CallLightProbesUpdatedFunction_mD73BBA534592554972ECBC7D7EE1542A4ECEE6EA,
	LightProbes_Internal_CallTetrahedralizationCompletedFunction_m491992F2C5429BCA63C2076F29BBEE7B10BA54CB,
	LightProbes_Internal_CallNeedsRetetrahedralizationFunction_m72321DC24BB3DDFFABCEC02F72A5BE5826B829FE,
	Resolution_ToString_m058CE120CC83F314D0C8D4A706F9AA068BC9CF34,
	QualitySettings_OnActiveQualityLevelChanged_m4E490A1A4A4BD3E8838E134EB3E6E3D6AB9955E6,
	QualitySettings_get_activeColorSpace_m4F47784E7B0FE0A5497C8BAB9CA86BD576FB92F9,
	MaterialPropertyBlock_SetTextureImpl_m4F16E63E86A344A8FB595BBD7132641593427F95,
	MaterialPropertyBlock_SetVectorArrayImpl_mFED50BE8138077759CB79FF448A7AD9B4F27D981,
	MaterialPropertyBlock_CreateImpl_mB5C499DC3ACC3B8AEB29249CC5896877810467EA,
	MaterialPropertyBlock_DestroyImpl_m169142814063295DE7C2FE69CD3580B2F952776E,
	MaterialPropertyBlock_Clear_m18CD90F6F18294A59C408FFCCC8F6F5EE204E6D4,
	MaterialPropertyBlock_Clear_m83CE1CC476A80F162FC89DBF6C2C78659B6E1253,
	MaterialPropertyBlock_SetVectorArray_m92BE44069E96A65D975F989B9C2703819B4D9709,
	MaterialPropertyBlock__ctor_m14C3432585F7BB65028BCD64A0FD6607A1B490FB,
	MaterialPropertyBlock_Finalize_m39FFB2D5E0348431D63EE94E263032CA6C7040BF,
	MaterialPropertyBlock_Dispose_m4D2F96068928FBC127E3A48B45DB5F3C0022B3E3,
	MaterialPropertyBlock_SetTexture_m39F531D3F35D6C5B661A7B4F07DD7B8ACC22627F,
	MaterialPropertyBlock_SetVectorArray_m5C0A3017A7EA9EE5F01295E346EA72D70A8BD682,
	Renderer_GetMaterial_m890C1B7FAA74CFC4B362EE3E8E61F6778D0EA189,
	Renderer_GetSharedMaterial_mD825E40750BD40B66D0A9FE4C283040E516FF192,
	Renderer_SetMaterial_mD7F173BF5941C840EB5A24FEF8B7BC3BAFAF7CCA,
	Renderer_get_enabled_mFDDF363859AEC88105A925FA7EA341C077B09B54,
	Renderer_set_enabled_m015E6D7B825528A31182F267234CC6A925F71DA8,
	Renderer_set_shadowCastingMode_mB0CD3D153E43044388AEFF7AEFDA93E80EB36D11,
	Renderer_set_receiveShadows_mABEB4C72E96E65117B7FFFD4180247565D0C1A09,
	Renderer_get_sortingLayerID_m3D7AE74F1B87099810CF969CB4520C85F9AE5F92,
	Renderer_set_sortingLayerID_m289E44FD06B6692C7B2ADD1189FE4FC013180C49,
	Renderer_get_sortingOrder_m4CE7ADEEC8E2F28CC1D10B1D4091A10F8F1583FA,
	Renderer_set_sortingOrder_m4C67F002AD68CA0D55D20D6B78CDED3DB24467DA,
	Renderer_get_sortingGroupID_m1DEFE39027236FF31AB603993BBC13C072E52118,
	Renderer_get_sortingGroupOrder_m40F058B3AFC92CCEF2475E0BD9A96464154E999E,
	Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656,
	Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81,
	Renderer_set_sharedMaterial_m5E842F9A06CFB7B77656EB319881CB4B3E8E4288,
	Renderer__ctor_m8B4EE9696B155A1B0A2CF13EBFC363CE175B9271,
	Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5,
	Shader_TagToID_m5B5214F0AABE47C7465EEA717C78568AE1251FE9,
	Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA,
	Shader__ctor_m2BD5A93728781AEF4DCA08F3BCC93C0A62B864C1,
	Material_CreateWithShader_m3AFA4A4172FE7A3BD861AA015F4DA8BF21E7EB3B,
	Material_CreateWithMaterial_m11AFA8AF4CF07621B8F872D231F52287D510E49B,
	Material_CreateWithString_m95D6FADE4506835842E09A7E26513252F560C87A,
	Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF,
	Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C,
	Material__ctor_mF1676C2EE42E6BCE74AC3C90E207A35E515D1FD8,
	Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983,
	Material_set_shader_mBD3A0D9AB14DE2F7CD5F2775E9AD58E15424C171,
	Material_get_mainTexture_mC6C6B860B44321F0342AEFA0DD7702384334F37D,
	Material_GetFirstPropertyNameIdByAttribute_mCBB72749633F7B8A879D33F0B34DB4F54F8C2439,
	Material_HasProperty_m52E2D3BC3049B8B228149E023CD73C34B05A5222,
	Material_HasProperty_mC09A83B44E368A217F606DD4954FA080CC03EC6C,
	Material_set_renderQueue_mFBB72A781DCCF0D4B85670B597788EC2D02D1C14,
	Material_EnableKeyword_mE8523EF6CF694284DF976D47ADEDE9363A1174AC,
	Material_DisableKeyword_mC123927EBF2F2A19220A4456C8EA19F2BA416E8C,
	Material_get_passCount_m7BA071AFFA34DC3E49B56A16CB8B098566BDE765,
	Material_GetTagImpl_m0A15070E738DE3834BEDD9D1634C498B5B744C2B,
	Material_GetTag_m0B37070270E231B88808DB1B3F9EF4C1851627D4,
	Material_SetPass_mBB03542DFF4FAEADFCED332009F9D61B6DED75FE,
	Material_CopyPropertiesFromMaterial_m4148227E6A0B8E66315D8115F656B7F8BEAE915B,
	Material_GetShaderKeywords_mEEE6AA1A327A7D63B8CD31306B70AAED6F7B7456,
	Material_SetShaderKeywords_mB0751FC936E7546A20DD6531B52E3B0EAF5BB3A8,
	Material_get_shaderKeywords_m11982F09EED6BB0A892342E1A72AEA470C44B105,
	Material_set_shaderKeywords_mD650CF82B2DBB75F001E373E2E1ACA30876F3AB8,
	Material_ComputeCRC_m5F743B7178F5E474A8FE15E5C0B1A6527E6E3A77,
	Material_SetFloatImpl_m78678CE6EBB67BA3E9D58AD37CE3BDA3789F43AB,
	Material_SetColorImpl_mC2AF99ED12166A5219C0E8B79D0D17C0FCFC0E3D,
	Material_SetMatrixImpl_m634CD43159467FE20FF3860B5F9848101A9B0CE3,
	Material_SetTextureImpl_mC5772481EBD0F0A488C564B424446514DF34CBD9,
	Material_GetFloatImpl_m39798817949E201E1CF85DBBD416C746F3D64794,
	Material_GetColorImpl_m44455DBA8C6391B5638D11934B8E4CC0713B4EFF,
	Material_GetTextureImpl_m0363E7A333009D2FBEEBA51FC5D6E219563FF288,
	Material_SetFloat_m879CF81D740BAE6F23C9822400679F4D16365836,
	Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8,
	Material_SetColor_m573C88F2FB1B5A978C53A197B414F9E9C6AC5B9A,
	Material_SetVector_m44CD02D4555E2AF391C30700F0AEC36BA04CFEA7,
	Material_SetMatrix_m668579C6402F88BFEF769D39E484BAD4CE6B0067,
	Material_SetTexture_mA9F8461850AAB88F992E9C6FA6F24C2E050B83FD,
	Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A,
	Material_GetFloat_m52462F4AEDE20758BFB592B11DE83A79D2774932,
	Material_GetColor_mCCC62F29234C5D2D9B19EE2D7DA46A4573AFA765,
	Material_GetVector_mFE0366FDCB87331910BCE5E234030C20A25484A2,
	Material_GetTexture_mBA07A377A7DDD29CD53E6F687DE6D49A42C0C719,
	Material_GetTexture_mE5D02B13E7AF35ABAE4FFC49E69FAF8F36F91191,
	Material_SetColorImpl_Injected_m7AF196BB00E62E5B47067FD643740884C692694A,
	Material_SetMatrixImpl_Injected_m982E0183155834D0018A8A0174DBE0536F56D49E,
	Material_GetColorImpl_Injected_mC524E0342AAF07A17E22CDBA0FAA0BAC0689CED4,
	Light_get_type_m0D12CD1E54E010DC401F7371731D593DEF62D1C7,
	Light_get_spotAngle_m28B2CD7ADE25422693E7B1FA23E8615E9D7098FC,
	Light_get_color_mE7EB8F11BF394877B50A2F335627441889ADE536,
	Light_get_colorTemperature_mA5B7C9A5B315B27625764B8CE7EF5ADC06060B08,
	Light_get_useColorTemperature_mD76967684F904F6068B58EE78BD65001D8AFF3EF,
	Light_get_intensity_m8FA28D515853068A93FA68B2148809BBEE4E710F,
	Light_get_bounceIntensity_m535008F539A0EF22BBB831113EC34F20D6331FAE,
	Light_get_range_m4156F07BA6CD289DA47080B590D632721D975A22,
	Light_get_bakingOutput_mF383DB97CFD32D65DA468329E18DD2DD61521CED,
	Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5,
	Light_get_cookieSize_m1BB417985207915659198F63CF825A23A8ED30B0,
	Light_get_cookie_m44A0C4B92F6CD6F2F8536A91C51B77FEEF59715E,
	Light_get_color_Injected_m7B1E67B800788DF5DAF6C3114CBACA3B029A900A,
	Light_get_bakingOutput_Injected_m587C60162C878716DF9310258756C2F587E35185,
	MeshFilter_DontStripMeshFilter_m91485EC5EAB0DFED85197CF48DB2DD5C872695AC,
	MeshFilter_set_sharedMesh_m946F7E3F583761982642BDA4753784AF1DF6E16F,
	MeshFilter__ctor_m77A77552ED64DE8DBE9DD89910941E83CC61862A,
	MeshRenderer_DontStripMeshRenderer_m6AA14C7B8A41C6BBCDE33338AAB96239048192A1,
	Mesh_Internal_Create_m484E627641C5E449D1B755F473DE5F6CD116FC45,
	Mesh__ctor_m5A9AECEDDAFFD84811ED8928012BDE97A9CEBD00,
	Mesh_GetIndicesImpl_m2A93DF68D761B4F7559A40617C41B543535B8739,
	Mesh_SetIndicesImpl_m8C51773B1B4F164E4A9EAE3A8A90D73319595B89,
	Mesh_PrintErrorCantAccessChannel_mD80AC1870BC51714BE0C97B15D3E3D4EC42AC85E,
	Mesh_HasVertexAttribute_m6FA9574E4DEE19158535F93281791AD6FB550595,
	Mesh_SetArrayForChannelImpl_mB6E87CFC14E1C7F12A921A2EB90004DC0108BE3C,
	Mesh_GetAllocArrayFromChannelImpl_m30C7972CA5A67CD01EBBDDE2FD8F48CE2F8F86F6,
	Mesh_GetArrayFromChannelImpl_mBCA166B62E25425D987638F2B4876613D113E66E,
	Mesh_get_canAccess_m97F255BBB8C009D643920E2D095B6DB8868E3572,
	Mesh_get_vertexCount_mB7BE0340AAF272933068D830C8E711FC8978E12C,
	Mesh_get_subMeshCount_mC0141293D0F339D8D721CCA2612B32E6FD7E3F8B,
	Mesh_get_bounds_m9E3A46226824676703990270A722A89A4860A100,
	Mesh_set_bounds_m2E526E9B61ACA77D644C22A9D8EB49583012B54E,
	Mesh_ClearImpl_m671C073131284D65405DE7480536EE16A2815A62,
	Mesh_RecalculateBoundsImpl_m33B65339F18DE70FDB0914F466281CDC39CAE5C2,
	Mesh_MarkDynamicImpl_m989606245279A69A710361DC0443BE39960DF857,
	Mesh_GetUVChannel_m52936A342F9C96EB7F0214A2F310018E1A25A689,
	Mesh_DefaultDimensionForChannel_mCE2BFE4516CB37EA677E8671997D986FF654C130,
	NULL,
	NULL,
	Mesh_SetSizedArrayForChannel_mBF8A0B7A6E58552C4142CD22FF31E7A0E125EC93,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Mesh_get_vertices_mA3577F1B08EDDD54E26AEB3F8FFE4EC247D2ABB9,
	Mesh_set_vertices_m5BB814D89E9ACA00DBF19F7D8E22CB73AC73FE5C,
	Mesh_get_normals_m2B6B159B799E6E235EA651FCAB2E18EE5B18ED62,
	Mesh_set_normals_m85D73193C49211BE9FA135FF72D5749B16A4760B,
	Mesh_get_tangents_mEA6C7BF6A2B9A8BD0E9A62963E048F973878299E,
	Mesh_set_tangents_mF547B7E4F9C70FB7CD6168139180A70AD306169B,
	Mesh_set_uv_m6ED9C50E0DA8166DD48AC40FD6C828B9AD2E9617,
	Mesh_set_uv2_m37B442C04EBB029C0AD9545C54F95D9BDAD8E9B5,
	Mesh_get_colors32_mA230CB5687CDCCEA5527BD5A0640E9535CB640A2,
	Mesh_set_colors32_m0E4462B7A1D613E6FB15DD7584BCE5491C17820F,
	Mesh_SetVertices_mCE01139EEEC3D10B4A6CEA1749FD84DABC53599E,
	Mesh_SetVertices_m193FDA5D9CB31FDFEEEC4CF17C0524A255E74E2F,
	Mesh_SetVertices_m13AB485BDB6B8F740338B20075A3E1510A6F96FA,
	Mesh_SetNormals_mB4072A4D2600768FD62BC9CAAFF8C43955A4A365,
	Mesh_SetNormals_m7EB9E43C0287E99F2140901EB9CE5A75728B1C29,
	Mesh_SetNormals_m07608D2B5CA012B8B3502868EF6D4B4DF859DE51,
	Mesh_SetTangents_m0FAEBB1D9ACF3607640669AD70AD6BFACA9D3CBB,
	Mesh_SetTangents_mAE30C8D38845CEC4002FE0938607CDE0D2358FBE,
	Mesh_SetTangents_m87066124EB0091440267546DB9A27EC89BF6E21B,
	Mesh_SetColors_m4D4920186213CCC1DA28CD3C7BDBD13F0D00541F,
	Mesh_SetColors_mEF64AD954EACB5F5EFFD8DA6AEA7D80CD1233F37,
	Mesh_SetColors_m07FAA75430500D2B2384A4A8185311411C504DEA,
	NULL,
	Mesh_SetUVs_mA0EA40129409D956FF22FCF7E01E27382879E255,
	Mesh_SetUVs_m69E3F4333E212B3CAA0EDFFF14A2835897079D35,
	Mesh_SetUVs_m25164DB799B915ECE8AE94B4A1F844CE936844B9,
	NULL,
	Mesh_GetUVs_m3FCD854132DA16719481B8D97DF335A0C7244344,
	Mesh_PrintErrorCantAccessIndices_mC2A8F7BBED09D4D6ABC265AFCC2979795C1ABF0C,
	Mesh_CheckCanAccessSubmesh_m216B541BBC526DB8F0ADBC9210E440D6DF875283,
	Mesh_CheckCanAccessSubmeshTriangles_m96B4AFC9B20344F39C936698D23FBA1DACCBA020,
	Mesh_CheckCanAccessSubmeshIndices_mB6659E6FDE4EFDECB233C194E4AC066F5FF60533,
	Mesh_set_triangles_m124405320579A8D92711BB5A124644963A26F60B,
	Mesh_GetIndices_m112B85EF32EE3C446947CE6CBC1AF3D50FC18179,
	Mesh_GetIndices_mFF08708714AB105E1097F1C2065D2A1ACAC88183,
	Mesh_CheckIndicesArrayRange_m0B8030BE6248E1E411D92E3255777E6E530527F4,
	Mesh_SetTrianglesImpl_m0B0B6FAC63A88C640C3AA9DD5F1748BE6B2A2EC2,
	Mesh_SetTriangles_mD495DA0B00DB0E60A2B7B500D644F4470C1D61DB,
	Mesh_SetTriangles_mD3AB650CCB405159EFDB9EC36AB21FF20213BFFB,
	Mesh_SetTriangles_m0E33B0C08C74A076A4B79F031DA60629B6CD86BA,
	Mesh_Clear_m0F95397EA143D31AD0B4D332E8C6FA25A7957BC0,
	Mesh_RecalculateBounds_mA9B293F57C6CD298AE2D2DB19061FC23B05AB90B,
	Mesh_RecalculateBounds_mCCC67392C58860F92A9674D0816BCA2D1730F077,
	Mesh_MarkDynamic_m718089940F240AFE625D6DC9DA4E6F20229CC322,
	Mesh_get_bounds_Injected_mE40C051E4181E7F70DB928CEA13A3A31DC9C29F7,
	Mesh_set_bounds_Injected_m13D417C394E27CE4A14DF253F8DD435677FC2835,
	Texture__ctor_mC0C7974BEBD867CEB281409FEA15A78CD91B19CC,
	Texture_GetDataWidth_m9D5166D12895E4E905ED072D611DF6A834C232BC,
	Texture_GetDataHeight_m37C3DE842FB101FD509904A0D76593F970B34995,
	Texture_get_width_m9569A27BA478BC380EBF83A53429F0E66E429108,
	Texture_set_width_m309DEA6AA9203B160624755C5D9EA75AB08AD6AE,
	Texture_get_height_mE95D093681C11FD77191F51843F6615605A87258,
	Texture_set_height_m2A62EC27DC1F0AE6A952B2B65FF7BF68710CDF36,
	Texture_get_isReadable_m6268EDAFF304287D8754EC0F115377722A316139,
	Texture_get_wrapMode_m1DE1C2813B72EDCCCEB396CFC91989358E8C3AD0,
	Texture_get_filterMode_mFEF0AEA29E8468450EF85533965DCEBE66D02A45,
	Texture_set_filterMode_mE423E58C0C16D059EA62BA87AD70F44AEA50CCC9,
	Texture_get_texelSize_m05CA60DE53EF7CD5D2CBFA68B69B764E4D463359,
	Texture_Internal_GetActiveTextureColorSpace_m2F9F2D07316E0B52679BA943369AFBF7C292949B,
	Texture_get_activeTextureColorSpace_m286856BA92961FD58FE181C8DDC417EC0572643C,
	Texture_GetTextureColorSpace_m94DFBCD1BFD13B49E0FDCFB545D41E7E1C99084C,
	Texture_GetTextureColorSpace_m7536B009835ED0098E1185F3BB2E5EA33558C1C0,
	Texture_ValidateFormat_m5E00A267F1E4805EFCE6F6ACE707518221589596,
	Texture_ValidateFormat_m1BC113E81713CBAF60BCA4D022ACC0C6B239E740,
	Texture_CreateNonReadableException_m29786CD930E89C281564A9B341FD4088FBC8C94F,
	Texture_CreateNativeArrayLengthOverflowException_m508BE4928B3768ED618EF1FA10E022F6FF12F870,
	Texture__cctor_mDE84772DB367950D96076B69BCF675ECC5819CE8,
	Texture_get_texelSize_Injected_m9AD407D56C4BDA37E028DAEDC0D787332BCFDD18,
	Texture2D_get_format_mE39DD922F83CA1097383309278BB6F20636A7D9D,
	Texture2D_get_whiteTexture_m3A243ED388F9EF0EC515015A6E0E50FD261D2FA1,
	Texture2D_Internal_CreateImpl_mFF0B15BB9C5620AEDC0B4172790F6C2328511E06,
	Texture2D_Internal_Create_m1CA68A3DB0A6340F32D58CFA3801D37547F0B52A,
	Texture2D_get_isReadable_m61A2BCE2B901DF11A910F5F8E93A72D4FD091B84,
	Texture2D_ApplyImpl_mF4AA09D491BDA81E4A3B3BA21157436CFEC39DAA,
	Texture2D_ReinitializeImpl_m4C524C33B85EDD5FD09CAF2FA2F922FFA8BE366C,
	Texture2D_SetPixelImpl_m9B08AB57ACD44485ECC3728BD3DE39F5E80A64AA,
	Texture2D_GetPixelBilinearImpl_m1BD6DEFD90A950A3A1A235ED641F4B20F5433C8B,
	Texture2D_ReinitializeWithTextureFormatImpl_mB571C523D62065D09C7EBCFDC258A0195A7905AE,
	Texture2D_GetWritableImageData_m8E26026A332040F8713E5A2A13C5545797159A5E,
	Texture2D_GetRawImageDataSize_mBD92C0F2A09A023841785BBB642C2B7696EE7CBE,
	Texture2D_SetAllPixels32_m3B35E8F24EAE2368148524647EA47BC731A7A07A,
	Texture2D_ValidateFormat_mD70A9FEEADE89325F05E3650404D8815EE9871F4,
	Texture2D__ctor_mD20B6FF794FE6E7AD5991DDA34777A415F47CA0E,
	Texture2D__ctor_mB1445796B2A76B3D867B2603205F513AF494B9F3,
	Texture2D__ctor_mECF60A9EC0638EC353C02C8E99B6B465D23BE917,
	Texture2D_SetPixel_m2CCFC5F729135D59DC4A697C2605A3FC5C8574DB,
	Texture2D_GetPixelBilinear_m6AE4AF4FD181C478DF0F2C5C329F22A263ABFF5C,
	NULL,
	Texture2D_Apply_m36EE27E6F1BF7FB8C70A1D749DC4EE249810AA3A,
	Texture2D_Apply_mA014182C9EE0BBF6EEE3B286854F29E50EB972DC,
	Texture2D_Reinitialize_m9AB4169DA359C18BB4102F8E00C4321B53714E6B,
	Texture2D_Reinitialize_mE7FBFD2EEF3BE3135269959DBBE253A2B79A82DF,
	Texture2D_Resize_m6A784927A609BAE045AFCF77886AEEE72D19FB90,
	Texture2D_Resize_m80C2555F96D9952F9C3B57C53B231A64F3DBA605,
	Texture2D_SetPixels32_m436DA034F0483F6527B7C4B74744A02ABE2EA276,
	Texture2D_SetPixels32_m169F9873A21FB8DECA24DC1B8F06FB23D13BEE2A,
	Texture2D_SetPixelImpl_Injected_m6B877CD888C30B122813AB3FDB853E336DBCBDE0,
	Texture2D_GetPixelBilinearImpl_Injected_m609C33C58A1944B31C61309F71B459DA3AD99F07,
	Cubemap_Internal_CreateImpl_m475DC72A8FADCE671347FF2167512030FD9FAD73,
	Cubemap_Internal_Create_m2E5D8A83C1A13C5309CC1504367206A4A10E68CB,
	Cubemap_get_isReadable_m66D87D825AE04E10C23B74E93E0111E87AF7478C,
	Cubemap_ValidateFormat_m60822B130F11C0593CF08E9C6145277C30EF0394,
	Cubemap_ValidateFormat_m73C8D32EC70CA5CFB8AF951C1E4D8C92A757ACDD,
	Cubemap__ctor_m3359CE12114733CB661B308AB0F73F65B14F4758,
	Cubemap__ctor_m3649B5EC5B2A7238E7F35A68BCAAFDDF19B96A18,
	Cubemap__ctor_m3821285A2DC0E20113E2BEAE3F16C9AF925FE14B,
	Cubemap__ctor_m395695939CC8AE11ABC2EBEE0731AA09B9780DB3,
	Cubemap__ctor_mCA2E50856972E2CABE59E804ACF8B5C393E81AEF,
	Cubemap__ctor_m7FAD821CFD73DA99987C1FEBA363BB2FBF84C034,
	Cubemap__ctor_m24E373BED44132D82BB86CB3EB668E652D2E3DD1,
	Cubemap__ctor_m4C0C7AD2F162CF6D98856430F3C2A41CE57123E5,
	Cubemap__ctor_m991258D254D93EC429FBCD9A82F394C7757963A5,
	Cubemap_ValidateIsNotCrunched_m8DE2FA7581B5DD6221A458125693A060769EAFE9,
	Texture3D_get_isReadable_mC8D0F22C0C3C9524FE31A0E3A70A27233AAF1031,
	Texture3D_Internal_CreateImpl_m1821CEA836681AC07E9AFEB4D9B70660E9959B6C,
	Texture3D_Internal_Create_m931AE6E3EBC7E03D2747E9F3B227B778D6A91644,
	Texture3D__ctor_mBF5766666A0CAAE44719627620B2BA7F99A14054,
	Texture3D__ctor_m09A62E979935144611CA0FA433C5655764D848AC,
	Texture3D__ctor_mA853CA41A85C285281FB928BC926B0798987B17F,
	Texture3D__ctor_m2814019477DFC8BAD31E7C3CEEEBB693DC26A67E,
	Texture3D__ctor_mFAEE4FD4E1E5A8093603E8C08F3909DDEDD41EFB,
	Texture3D__ctor_mD7476DE7FCF3F8811FD94C81CD51AD5A8AF2CD34,
	Texture3D__ctor_m8899763E7E513380681AEA91563525E89A3EF37A,
	Texture3D__ctor_m0026373ADF87B100336F82579802E7819A5DB0F2,
	Texture3D__ctor_m578EC77B0E1A9EF9E92003BA516E227DB85C699C,
	Texture3D__ctor_mD27F46A8B13B79866370D54112CB569B104886A2,
	Texture3D_ValidateIsNotCrunched_m7B73D943590E60158407EC4F8C940061D3850A56,
	Texture2DArray_get_isReadable_m1C8E2BAFBA65C894DAC0F30C9FC685173B879C32,
	Texture2DArray_Internal_CreateImpl_m4EE7049C7DBB008FE670114F5B54B81A4443E41F,
	Texture2DArray_Internal_Create_m11CD6B09AB104B2042FAE24CC112B67A0A3CFDD5,
	Texture2DArray_ValidateFormat_mBE783ADB196C4C046E69C370387E0CAFB5B26A25,
	Texture2DArray_ValidateFormat_m2155230AF829C3BD0B76D886B9D83A74F9994921,
	Texture2DArray__ctor_mE60F3475E1987C78E2055BFDB60394D9CBDEA395,
	Texture2DArray__ctor_m052C0D528905EC9347CCE98B4043E83BAB094218,
	Texture2DArray__ctor_m465591352202E3D5CAF529F8CB094FB2E52C6EA0,
	Texture2DArray__ctor_m6EA4D33D0E4804BD374D86BDE37A5F7F860200C4,
	Texture2DArray__ctor_m35E6845C6F246D79286ACF218449854B8430B5AC,
	Texture2DArray__ctor_m7C3429ECA397399F19C750F842573B920C6CAF78,
	Texture2DArray__ctor_mC1501E822F10F3BDECDFA39EF8EE1CEB5EA84998,
	Texture2DArray__ctor_m815CEB6ACC6C586A3D696AFA09D78C5B1957F9C0,
	Texture2DArray__ctor_m02B575835C1D88384829865AAC5A30228AB9C12C,
	Texture2DArray_ValidateIsNotCrunched_m66FF8B3CB9A2A6695B90FD47E64F17672E7E825D,
	CubemapArray_get_isReadable_m316414972F31FD8FA45F78EB2A41585E886CA868,
	CubemapArray_Internal_CreateImpl_m6E7C2AA04A147BB82B16156FC0A464EE4DC6B406,
	CubemapArray_Internal_Create_m522E033D18C28E019CA8B49B6A5F898FE5D2965C,
	CubemapArray__ctor_m540A30E402A99397CC4A65A38954283C69FB9B75,
	CubemapArray__ctor_mC6CA963CA8814B26F0711EA68E22D9D3669B2F82,
	CubemapArray__ctor_mD255863D4BD2CC18AF0065CAB121609A82019676,
	CubemapArray__ctor_m0C70134AD214861C2B196A31E5F5156B56CEBF7D,
	CubemapArray__ctor_m5BA9CE57CA5F1023131EFB9B946095B797D47273,
	CubemapArray__ctor_m1DB13F0A5997B95E8E30369997CEF9268496881F,
	CubemapArray__ctor_m80BD460BEB50BAF7787C64B85A147ABF05AEB09A,
	CubemapArray__ctor_m8F5B690A043EB48E7996148A50F243BF5FE3D65E,
	CubemapArray__ctor_mF817A910C4140CA704BEA27D6111D1BAFD07E7F1,
	CubemapArray_ValidateIsNotCrunched_m10B18C4C73CF14C45CC0C9774DE108E7D106DB37,
	RenderTexture_get_width_m1B8A7D17591897EFAA8C841795A9BD15EA5C7B31,
	RenderTexture_set_width_mDCDF28DB4E7CC398E5EA31B7A8B39B0D34D7FAB1,
	RenderTexture_get_height_m9695AE75401A1D89227C6827843F4ADF4A3EAD74,
	RenderTexture_set_height_m04F8A4678F13855D357AA3458097DF1FD6D5BDEE,
	RenderTexture_SetColorFormat_mB68B8EC61ACF16D78BC3198ABBEDFC9921A39B9D,
	RenderTexture_set_graphicsFormat_m4CFDFF4CEC81B5350AE94D466585C8186E06B733,
	RenderTexture_set_depthStencilFormat_mCEED5DA45F9F19CD67D960D896400E85B1211855,
	RenderTexture_GetActive_m01B8BCBAC96889D938D87000815544470E7EE40B,
	RenderTexture_SetActive_m54E8330D7137E080D7E2EF2FD495BC906126AEB2,
	RenderTexture_get_active_mA4434B3E79DEF2C01CAE0A53061598B16443C9E7,
	RenderTexture_set_active_m5EE8E2327EF9B306C1425014CC34C41A8384E7AB,
	RenderTexture_GetColorBuffer_mDCF981FDA18342C7D239932B7105CA0DB1798E4A,
	RenderTexture_GetDepthBuffer_mA3CA39776C8174D199AD26E868B3EB720C6B801B,
	RenderTexture_SetMipMapCount_m037EE54855938389E781C6CFA5A398641BC7CB83,
	RenderTexture_get_colorBuffer_mE043AF01C1B2FB73BDC9C82D78528A367089CDE0,
	RenderTexture_get_depthBuffer_mBBDFA14B3AC2AE4796795E89A0BCA59D54B859D5,
	RenderTexture_SetSRGBReadWrite_mBE116D34F4B919AC7E48814ABC9D623A9FE518C0,
	RenderTexture_Internal_Create_m9468E1F6533E5E80D6CDD6356472423FC6349BFE,
	RenderTexture_SetRenderTextureDescriptor_m498C0757E95407077AF11B034319DF7AC904BA18,
	RenderTexture_GetDescriptor_mB180407D5E1215BF1651A913AC146D25EAE9C6BC,
	RenderTexture_GetTemporary_Internal_m33027F4E37F8D03F8B8FB931D0A498096F590F63,
	RenderTexture_ReleaseTemporary_mEEF2C1990196FF06FDD0DC190928AD3A023EBDD2,
	RenderTexture__ctor_m731283139A246174F3F33B37991F9BFFBD29293D,
	RenderTexture__ctor_m1CBDB7F13C2CE49A31EE654081F01C4F874EA5E3,
	RenderTexture__ctor_mF6FCD7FA9976CC646BDF8715157EA198A992E75B,
	RenderTexture__ctor_m69A0AF5C6CCFFFB58D9F5A0C975D0272CA66684B,
	RenderTexture__ctor_m0C81127DE754F64FDD3E80E94BE11054B2791F98,
	RenderTexture__ctor_m583FCACDD5FCA4102329911331B6DC51660795F0,
	RenderTexture__ctor_m0F1316F315E35B4E305FE929604E8F489189C39D,
	RenderTexture__ctor_mD60FB2D8D9560774F2E21BAC0A0061CB17904EA3,
	RenderTexture__ctor_m68A1B9CAA1BE0B597C5F4895C296E21502D0C962,
	RenderTexture__ctor_m53215A8EDDE262932758186108347685F6A512C4,
	RenderTexture__ctor_m45EACC89DDF408948889586516B3CA7AA8B73BFA,
	RenderTexture__ctor_m7E177DB2DEF7CD2EEB812EEB262081E923BEF2AC,
	RenderTexture_Initialize_m924B8245CB23DF4FECCF5A68B158A393DC0401D0,
	RenderTexture_GetDepthStencilFormatLegacy_mC4C41354858D1CE6275439361C1763854E42E2C5,
	RenderTexture_GetDepthStencilFormatLegacy_m7903427680451A0E045B90B765E8B365988F5A4B,
	RenderTexture_GetDepthStencilFormatLegacy_mEA344F7BCAAFD3415A95032EBB1F5AED8C0926A1,
	RenderTexture_GetDepthStencilFormatLegacy_m5062F40BEAE6F266A5B9109335DFDC6DA9B59417,
	RenderTexture_get_descriptor_m2FABD5CF6CCF410D1311FCBC7C9D9ECDEE9C7CC2,
	RenderTexture_ValidateRenderTextureDesc_m9A67530FAD20FE54836E62B6B45AF9A4375C3340,
	RenderTexture_GetDefaultColorFormat_m3E5847AB5B7C4EF634A571F032A74BB4F1E1AF71,
	RenderTexture_GetDefaultDepthStencilFormat_mB411FD8C581A112B308352312EF07BDF7FECD08E,
	RenderTexture_GetCompatibleFormat_mF23017BDD92678EBD0B8B861BA6163B04EF55DBB,
	RenderTexture_GetTemporary_mA8C827B80D3C07D0B8CDF7F5270FB5D3E53DD235,
	RenderTexture_GetTemporaryImpl_m865E3CCF5F6D0125C51E8CC6B2D86811A4F7F71A,
	RenderTexture_GetTemporary_mED96CF4304FE2CAFCC1ECBC08F6AC01EAE26B07B,
	RenderTexture_GetTemporary_m8B161095886EFC9B592F153D6C1D933C067DF809,
	RenderTexture_GetTemporary_m82011491839499F6F3263CD9982B13292142C51D,
	RenderTexture_GetTemporary_m925A4E25416A9FF58E9CC0CA67BBEB3A971DDFC3,
	RenderTexture_GetTemporary_mA6619EA324AAE80B6892107C6968092F6F1B4C45,
	RenderTexture_GetTemporary_mDAD0D2A673F07BEC3B1A9555863E24A479E9BB11,
	RenderTexture_GetColorBuffer_Injected_m1AC1564038AB3F39ECFDB8C98AC573F4803BEE68,
	RenderTexture_GetDepthBuffer_Injected_m5EA9252E3F0DFAEDE76303614E7683CC549B05F4,
	RenderTexture_SetRenderTextureDescriptor_Injected_m92390C2D26840E69E7B8F3DF5DA44FCB078F363F,
	RenderTexture_GetDescriptor_Injected_m37E30C68CAB7F283BDD1FA2F580066C4BA80A085,
	RenderTexture_GetTemporary_Internal_Injected_m5CF82A63F62919ACA725691BCA5B4412B906F454,
	RenderTextureDescriptor_get_width_mB159E4EB08B23B19CCCFADB465864361FB840BFF,
	RenderTextureDescriptor_set_width_m3B2494007BFE3AD4D14403407C9B24F5045E7E10,
	RenderTextureDescriptor_get_height_m1006F9AA45029715C552C8A8C2F102F63D3A91EC,
	RenderTextureDescriptor_set_height_m1FE41111472DAA9B5E80FFAF3445004D72A3CFA5,
	RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22,
	RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4,
	RenderTextureDescriptor_get_volumeDepth_m528818299E101F1B285B08BE12FAC2F9A871BA36,
	RenderTextureDescriptor_set_volumeDepth_mEF9610D1C14182417A01B7243DEE6B559A13B34D,
	RenderTextureDescriptor_set_mipCount_mDCC85ED7D97BD64A290A21DB91BC5CB1C4BA95EF,
	RenderTextureDescriptor_get_graphicsFormat_m50F25A4F179EA318C8D3B0D8685F9C5F59F7DEC0,
	RenderTextureDescriptor_set_graphicsFormat_m037DA25F9A8B956D830C7B7E5C6E258DC1133A13,
	RenderTextureDescriptor_get_depthStencilFormat_m360929BE5BD10E9C3D8C936AA6B44B1D11C119CB,
	RenderTextureDescriptor_set_depthStencilFormat_m7EC335006743B59AAD1895AD06B2DD74333F9A03,
	RenderTextureDescriptor_get_depthBufferBits_mC095E36F9803B2E68E258C03E48ACD0B0E678953,
	RenderTextureDescriptor_set_depthBufferBits_mA3710C0D6E485BA6465B328CD8B1954F0E4C5819,
	RenderTextureDescriptor_get_dimension_mA23ABB2CA03249DCE3A21F5123524A825C33E31B,
	RenderTextureDescriptor_set_dimension_mCE9A4A08454BB2D9DFE3E505EC336FD480078F39,
	RenderTextureDescriptor_set_shadowSamplingMode_m4B4CE918DFFF1CC5E3AF981456E186F15FC5DB93,
	RenderTextureDescriptor_set_vrUsage_m994CB3D4B250A70BE005D9FDFD24D868E07A52F0,
	RenderTextureDescriptor_set_memoryless_m9ECE149930C0E2629A5CD9DA1CD0EA2A01FFE1B2,
	RenderTextureDescriptor__ctor_m8B0D32DC550540B5546891C2F6300F384D6FE692,
	RenderTextureDescriptor__ctor_m8F8897C63F614AEA4348A95293C911C1293DA3A4,
	RenderTextureDescriptor_SetOrClearRenderTextureCreationFlag_m4C08C7A3F715426EBECA2B983361908D097C6424,
	RenderTextureDescriptor_set_createdFromScript_mEE28DED1D3C20DA025A0C44E1C2A531685194F23,
	RenderTextureDescriptor_set_useDynamicScale_m9335866C8754D51235D391E84F8972C4C518844A,
	Hash128__ctor_m0B61E717B3FF7D7BBD8FF12C8C8327C18A2AAAF3,
	Hash128_CompareTo_mFFE3648A372A7F3202897B266E14A6E2521BFE0C,
	Hash128_ToString_m35513B62830C8CE59346AF87AC2B587FA1570DCE,
	Hash128_Parse_mA918076817FF18F2172F1427AFA472C0A8321E47,
	Hash128_Hash128ToStringImpl_m351D7595F82E3287DDE4D5D97245140D98849A42,
	Hash128_Equals_m28FADCC2F9A565AF152A53BCEEF88F798716B104,
	Hash128_Equals_mF6BED87E0744D6DFFF8026614BDA8F4E6712063D,
	Hash128_GetHashCode_m22816EE33CD973D11CD1917DEF7A0E0EC229E1D8,
	Hash128_CompareTo_m1D249BA0DD01AF06E5B96B186E8A5F181BB30758,
	Hash128_op_Equality_m870F4E857699627E5235BBD0698022E023C8C8F5,
	Hash128_op_LessThan_mD2B90720E6C871528BB20F19FED34883A5BC8A43,
	Hash128_op_GreaterThan_m7AAFA151BF17A7A54856E529F8B358F566508237,
	Hash128_Parse_Injected_m000B782285D88C832BA0130E5BC3E52BC60D79FA,
	Hash128_Hash128ToStringImpl_Injected_m3235EBF9B6966B9720D13EAEC3AFDC2824AFF332,
	Cursor_SetCursor_m00781B5A9FDF84759E2D8869F08E6120062FCE86,
	Cursor_get_lockState_m9AD145AFB215DFEDA0074AC1F129EF02CCE5B632,
	Cursor_SetCursor_Injected_mCAFC0AE81FA6AE6C9575CA82E64AC48A94883CC8,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Logger__ctor_m3155E21A68AA616431A260A3FCBB4B074DF6FAA2,
	Logger_get_logHandler_m4FAA2028695BD9FBA134E836AD52480984E82215,
	Logger_set_logHandler_mD2A80ADC7D4155C91B68EAE1FDBD7ECBF6DB49D3,
	Logger_get_logEnabled_m0A868820FAADBF477B46963F5050AFCBBC46AF0B,
	Logger_set_logEnabled_m3FBDDB7D1C5D9854C0F32ED6F7AB0348F286F52E,
	Logger_get_filterLogType_mCE9711AF596B77EAD158590247FA5FC855F54A54,
	Logger_set_filterLogType_m8EF119582F4975FE3D297EE71F9D3E1CFEA7AB74,
	Logger_IsLogTypeAllowed_mFE76B00210BF4431747A69A28A15EE2BF1A0D586,
	Logger_GetString_m2965E4936E7B1C763CE7A3FF6AACE9590DA7D7BE,
	Logger_Log_mEA3D39763D610E92491AA479BA653ECFEE3E9E5C,
	Logger_Log_mF8C7E8A8CC31E04732044D73D2CB551D7CCB8995,
	Logger_LogError_m4612980842D15256036F4EB16BADF13FD49F38F5,
	Logger_LogException_m591AF39F0886DA44666068EDBBD3CCF07623CFBB,
	Logger_LogFormat_m5A31966B8AA13AC1FFEC1DED42F56FA966459093,
	Logger_LogFormat_m776A546E755F914039AB8591E23D08510308DB4C,
	Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C,
	Color__ctor_mCD6889CDE39F18704CD6EA8E2EFBFA48BA3E13B0,
	Color_ToString_m0018DE2184B3377CCA6FBD72D5D47886DC669147,
	Color_ToString_m70AEF3634C556F6AA01FC3236226C3D27C277229,
	Color_GetHashCode_m2981EEA1DEFE55254945D7D03BE64D4F56BA58D0,
	Color_Equals_m24E409BF329F25774C6577F18EFD3DE1253684D6,
	Color_Equals_mD297CAFFEBE9352C940873862FDF9A28F1F02435,
	Color_op_Multiply_mD0296202733CB2D5342FB7C82B48AEDA63036758,
	Color_op_Multiply_m379B20A820266ACF82A21425B9CAE8DCD773CFBB,
	Color_op_Equality_mB2BDC39B0B367BA15AA8DF22F8CB0D02D20BDC71,
	Color_op_Inequality_mF1C733BA10E60B086AB950A71143678AE76C4D92,
	Color_Lerp_mE79F87889843ECDC188E4CB5B5E1F1B2256E5EBE,
	Color_LerpUnclamped_m91027D026E64424B71959149B942F706FC16B1A2,
	Color_RGBMultiplied_m4B3BAE4310EA98451D608E0300331012AFFF1B01,
	Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D,
	Color_get_green_mEB001F2CD8C68C6BBAEF9101990B779D3AA2A6EF,
	Color_get_blue_mF04A26CE61D6DA3C0D8B1C4720901B1028C7AB87,
	Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6,
	Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737,
	Color_get_yellow_m66637FA14383E8D74F24AE256B577CE1D55D469F,
	Color_get_clear_m02E023A7D396B973288B3915F6F24FBF7E0DC81D,
	Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C,
	Color_get_maxColorComponent_m97D2940D48767ACC21D76F8CCEAD6898B722529C,
	Color_op_Implicit_m9B3228DAFA8DC57A75DE00CBBF13ED4F1E7B01FF,
	Color_op_Implicit_mA8CF4745D766F4F610E1BE0A1ED2F4E5FE5D734C,
	Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E,
	Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70,
	Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E,
	Color32_ToString_mB1EFBF981F901A76ABF2FEA19EB290A2D8CAFC32,
	Color32_ToString_m263D34787852D176627FC2B910DFE9CABAF26696,
	Gradient_Init_m5BA62E19CAFB02B0EC3FEEF2247F0D7D79DB4A95,
	Gradient_Cleanup_m852D91204DE7A67197754BC5EEF35BBC340795E9,
	Gradient_Internal_Equals_m8499EFC381172FFC827366BE219C9CDA8A444D0C,
	Gradient__ctor_m5EC470BB063D4831774C7CDA5D471EBEB5CE7B54,
	Gradient_Finalize_m69475357E7933E7692476BB6B8952E06EC4F23CC,
	Gradient_Equals_m3CA73EEE2426924D75D835A69F00B9EB50D44294,
	Gradient_Equals_m77211B56445AB40DEF32AE890BD6B13E2410FB98,
	Gradient_GetHashCode_m031DD1B99829405A1654F69EC63CFF929139C94C,
	Matrix4x4_GetLossyScale_m3C19D2C6746BB211C8CB02478A60EB2D71D10FC7,
	Matrix4x4_get_lossyScale_mFB3D4CF6408D710D607CA1D2AF94B2E4E0B57EB7,
	Matrix4x4_TRS_mCC04FD47347234B451ACC6CCD2CE6D02E1E0E1E3,
	Matrix4x4_Inverse3DAffine_m7A7796EE699A2228A709611D11364541DE768AE6,
	Matrix4x4_Inverse_mFB2503F5D5FE76E7C56249700ED2E43DDA0F1939,
	Matrix4x4_get_inverse_m4F4A881CD789281EA90EB68CFD39F36C8A81E6BD,
	Matrix4x4__ctor_m6523044D700F15EC6BCD183633A329EE56AA8C99,
	Matrix4x4_GetHashCode_m313B1AF4FEA792BB7E4D1D239EBE3257F825914D,
	Matrix4x4_Equals_m35CFC5F304BB40EFFE011B92AA87B77CD8FF8F74,
	Matrix4x4_Equals_mDB0C4CCC58BE3E108F1A40BE8DBDCD62E284CC51,
	Matrix4x4_op_Multiply_m75E91775655DCA8DFC8EDE0AB787285BB3935162,
	Matrix4x4_GetColumn_m5CE079D7A69DE70E3144BADD20A1651C73A8D118,
	Matrix4x4_GetRow_m59C6981300C6F6927BEA17C5D095B2AD29629E9F,
	Matrix4x4_MultiplyPoint_m20E910B65693559BFDE99382472D8DD02C862E7E,
	Matrix4x4_MultiplyPoint3x4_mACCBD70AFA82C63DA88555780B7B6B01281AB814,
	Matrix4x4_Rotate_m015442530DFF5651458BBFDFB3CBC9180FC09D9E,
	Matrix4x4_get_identity_m6568A73831F3E2D587420D20FF423959D7D8AB56,
	Matrix4x4_ToString_mB310BE20B7CDE8AEA91D84FCA4E75BCACF7DFC86,
	Matrix4x4_ToString_mB8E1EFF14A37605ABA321708CC36021FFBCF84CF,
	Matrix4x4__cctor_mD8409F5527865C5B73AC1195893BD110330FDB92,
	Matrix4x4_GetLossyScale_Injected_mABC36BB3229BE212643B44F6B090C86CBF65D607,
	Matrix4x4_TRS_Injected_m5EF976A6FBB27DD3E44C0B51575EBEEF802EB6F1,
	Matrix4x4_Inverse3DAffine_Injected_mA108B0432274BE970746A528E27849AA6BD3EF05,
	Matrix4x4_Inverse_Injected_m666E1049EA37568CCC0C3636194F8A815E30DF66,
	Vector3_Lerp_m3A906D0530A94FAABB94F0F905E84D99BE85C3F8,
	Vector3_LerpUnclamped_m4109A459C1DB823310A10B8B1E80CB6877418347,
	Vector3_get_Item_m163510BFC2F7BFAD1B601DC9F3606B799CF199F2,
	Vector3_set_Item_m79136861DEC5862CE7EC20AB3B0EF10A3957CEC3,
	Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0,
	Vector3__ctor_m5F87930F9B0828E5652E2D9D01ED907C01122C86,
	Vector3_Cross_mF93A280558BCE756D13B6CC5DCD7DE8A43148987,
	Vector3_GetHashCode_mB08429DC931A85BD29CE11B9ABC77DE7E0E46327,
	Vector3_Equals_mB4BE43D5986864F5C22B919F2957E0309F10E3B4,
	Vector3_Equals_mEDEAF86793D229455BBF9BA5B30DDF438D6CABC1,
	Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F,
	Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07,
	Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52,
	Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B,
	Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8,
	Vector3_Min_m1CAC3499F14EA87366C0C3C1F501B4FB2863CDB4,
	Vector3_Max_m9B6D8FEE7F4CE32C0AAB682606FFBA59E1F37C74,
	Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39,
	Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02,
	Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A,
	Vector3_get_back_mCA5A84170E8DE5CE38C0551B4CCAD647BF215E57,
	Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA,
	Vector3_get_down_mF62B2AE7C5AC31EAC9CB62797C7190C90A7A8599,
	Vector3_get_left_m8C1116485A9E689760AEE1142F5977852278B7E1,
	Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0,
	Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067,
	Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828,
	Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915,
	Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353,
	Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41,
	Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB,
	Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479,
	Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF,
	Vector3_ToString_m6C24B9F0382D25D75B05C606E127CD14660574EE,
	Vector3_ToString_mA8DA39B6324392BB93203A4D4CB85AF87231CB62,
	Vector3__cctor_mBE529AB9D272468D923A964C5F2CE611422EADA8,
	Quaternion_Inverse_mD9C060AC626A7B406F4984AC98F8358DC89EF512,
	Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E,
	Quaternion_AngleAxis_mF37022977B297E63AA70D69EA1C4C922FF22CC80,
	Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8,
	Quaternion_get_identity_m7E701AE095ED10FD5EA0B50ABCFDE2EEFF2173A5,
	Quaternion_op_Multiply_mE1EBA73F9173432B50F8F17CE8190C5A7986FB8C,
	Quaternion_IsEqualUsingDot_m9C672201C918C2D1E739F559DBE4406F95997CBD,
	Quaternion_op_Equality_mE6F6B56FCED8478552BE02BBAF18C70B969217F9,
	Quaternion_op_Inequality_m4EC1EF263D0E42432A301F85CB52028D2973F5DA,
	Quaternion_Dot_mF9D3BE33940A47979DADA7E81650AEB356D5D12B,
	Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90,
	Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73,
	Quaternion_GetHashCode_m5F55C34C98E437376595E722BE4EB8A70434F049,
	Quaternion_Equals_mCF93B00BA4FCDDE6100918451343DB9A0583A0A0,
	Quaternion_Equals_m25B95D8412B79CC7F8B34062BFAE662BD99578BE,
	Quaternion_ToString_mC5BD5DEF60FCA4A38924462A5C4440ECFCF934C4,
	Quaternion_ToString_m9B592D577B3FDB892CA53ABF3457BC2EDE45DF8C,
	Quaternion__cctor_m0C8018E54211DA5CFB4CF107C12EE466951F6575,
	Quaternion_Inverse_Injected_m9BFD9E0A669FDB8227D7736F1B6E5795A97E4563,
	Quaternion_Internal_FromEulerRad_Injected_m2C2420C918664E16478C6055D5ED421B15D812B8,
	Quaternion_AngleAxis_Injected_mA80506B6DBE241FF55113EA65838923C64BDFAC5,
	Mathf_IsPowerOfTwo_m58172AEBE272F53FD34CC10641057847181E960A,
	Mathf_NextPowerOfTwo_mA1CE7F3EEF9B0B07AB2D586C030ED236D578F485,
	Mathf_GammaToLinearSpace_mEF9E26BAD322E55448B286ABDCDF4A2CC236547F,
	Mathf_CorrelatedColorTemperatureToRGB_m595A3D1E887CD42FE21CD2893D8E377B3F44153C,
	Mathf_Sin_m8498BAF996D13BEEE734F6D1B1BE4D7853BBF2C0,
	Mathf_Cos_mB9E0E085EE4433B820DC752E68B395A7C59014AA,
	Mathf_Tan_m02B11ED38704291D448322E57D396F9FAB4EE5B7,
	Mathf_Acos_m18B78367C77DC32AA04D2623FB860CF7DF587093,
	Mathf_Atan_m92C7BFD0B9C392FA72C8940EC4EA3F927907ABF5,
	Mathf_Atan2_m329EF9B5D191198E9FC08769FB0E1D27632192E3,
	Mathf_Sqrt_m42E8E54D522D4FA070A76AD1ED62BFFEEEB3317A,
	Mathf_Abs_m4778EE107161FAC49E51E7BEF3F4A4FB2F02A715,
	Mathf_Abs_mD945EDDEA0D62D21BFDBAB7B1C0F18DFF1CEC905,
	Mathf_Min_m747CA71A9483CDB394B13BD0AD048EE17E48FFE4,
	Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB,
	Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051,
	Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8,
	Mathf_Pow_m54654EC32D45E0679E06F367125EE3EE3BD086F1,
	Mathf_Log_m116F062EEBF1C53EC8D18C9B1748E999EF9424EF,
	Mathf_Log10_m807D2F7C24B7A9BBB8C4E5633F1E8FDACFB267C2,
	Mathf_Ceil_mDEE98B3A54FB2F82B3783DB30ECB31E2015C57D9,
	Mathf_Floor_mF5A5487B496B3DE8196ADABC733E45A895F422AA,
	Mathf_Round_m5C834461627B464609DF863457BB4ED2CD5FEA9E,
	Mathf_CeilToInt_mF2BF9F4261B3431DC20E10A46CFEEED103C48963,
	Mathf_FloorToInt_m2A39AE881CAEE6B6A4B3BFEF9CA1ED40625F5AB7,
	Mathf_RoundToInt_m60F8B66CF27F1FA75AA219342BD184B75771EB4B,
	Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A,
	Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF,
	Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B,
	Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14,
	Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5,
	Mathf_LerpUnclamped_mF35042F9D25A221BDD4B3FD6251224610AFC1F35,
	Mathf_Approximately_m1DADD012A8FC82E11FB282501AE2EBBF9A77150B,
	Mathf_SmoothDamp_mBE7ABB6B59D198BE8ABE42942452CC7B813A5248,
	Mathf_Repeat_m6F1560A163481BB311D685294E1B463C3E4EB3BA,
	Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D,
	Mathf_ClampToFloat_mFE6AEF79E95B21E93763BB1D4163CCBDCCB91D4D,
	Mathf_ClampToInt_mCFCFE5DEB4C6009EFF3CDDA8E934076588AA2C9E,
	Mathf_ClampToUInt_mC2D0AB113A813EA9B5C121399F18EB732DE7945C,
	Mathf_GetNumberOfDecimalsForMinimumDifference_m631B3AB68CB1A03827527AF355982ED7786F090C,
	Mathf_RoundBasedOnMinimumDifference_m5BE4283F3981DD32F74EFD8FFE4320FC21156DF9,
	Mathf_DiscardLeastSignificantDecimal_mA70D60612195263655D9791342C227B2888458AE,
	Mathf__cctor_m096B4CC2F751E87D1018F79D32E28182BDC5E652,
	Mathf_CorrelatedColorTemperatureToRGB_Injected_mE0B92B9F5833D54E73BCF44C715B98F4CAB74020,
	Vector2_get_Item_m18BC65BB0512B16A1F9C8BE4B83A3E7BBAD7064D,
	Vector2_set_Item_mEF309880B9B3B370B542AABEB32256EEC589DD03,
	Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548,
	Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C,
	Vector2_LerpUnclamped_mB0B50875D4509E21FF43F4D87610333D55E6A44F,
	Vector2_Scale_m8D61A8D2272549F1EF41256F7E8A206C6500EA6C,
	Vector2_ToString_mB47B29ECB21FA3A4ACEABEFA18077A5A6BBCCB27,
	Vector2_ToString_mC10F098442E56919947154402A77EDE28DC9B7BE,
	Vector2_GetHashCode_mED8670C0E122B7ED0DAB4C3381ADFF04B75E0B03,
	Vector2_Equals_mA4E81D6FCE503DBD502BA499708344410F60DA4E,
	Vector2_Equals_mDF84D5ED14E018609C6A9C9BAE016C1B33BCFF4C,
	Vector2_Dot_mC1E68FDB4FB462A279A303C043B8FD0AC11C8458,
	Vector2_get_magnitude_m5C59B4056420AEFDB291AD0914A3F675330A75CE,
	Vector2_get_sqrMagnitude_mA16336720C14EEF8BA9B55AE33B98C9EE2082BDC,
	Vector2_Min_m2D222BC18ACD4F965981EC93451DDD1D7ADFDDA0,
	Vector2_Max_m1E906743ECA6478A2EDCCFECD9D509898F66152B,
	Vector2_op_Addition_m8136742CE6EE33BA4EB81C5F584678455917D2AE,
	Vector2_op_Subtraction_m44475FCDAD2DA2F98D78A6625EC2DCDFE8803837,
	Vector2_op_Multiply_m6FFFFF6A9E069A4FBCA7E098D88AE39C2B80D442,
	Vector2_op_Division_m707994C12D356E65E453CEE8F767E652B60911BF,
	Vector2_op_UnaryNegation_mBA9FC53A2194EE3CC067A12D11879F695B34D6F9,
	Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE,
	Vector2_op_Multiply_mC53581E703768BA2512A7C65283657C331994353,
	Vector2_op_Division_m57A2DCD71E0CE7420851D705D1951F9238902AAB,
	Vector2_op_Equality_m6F2E069A50E787D131261E5CB25FC9E03F95B5E1,
	Vector2_op_Inequality_mBEA93B5A0E954FEFB863DC61CB209119980EC713,
	Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96,
	Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7,
	Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C,
	Vector2_get_one_m9097EB8DC23C26118A591AF16702796C3EF51DFB,
	Vector2_get_up_m41067879408BB378593EF7406AF2525F176F0ABF,
	Vector2_get_down_m7672D87B9C778FB2AEF7BB79758142D01166B493,
	Vector2_get_left_m851D1A435131CE336F60115E19FC8C21480284BC,
	Vector2_get_right_m99043ED6B3D5AEA5033313FE3DA9571F39D1B280,
	Vector2_get_negativeInfinity_mE3346BA420245D8529F57AAE8A28A3BB03C214C0,
	Vector2__cctor_m985790D0B5F5F80A0E3461F04F05BAB8CBAD0105,
	Vector2Int_get_x_mA2CACB1B6E6B5AD0CCC32B2CD2EDCE3ECEB50576,
	Vector2Int_set_x_m291ECF246536852F0B8EE049C4A3768E4999CDC8,
	Vector2Int_get_y_m48454163ECF0B463FB5A16A0C4FC4B14DB0768B3,
	Vector2Int_set_y_mF81881204EEE272BA409728C7EBFDE3A979DDF6A,
	Vector2Int__ctor_mC20D1312133EB8CB63EC11067088B043660F11CE,
	Vector2Int_op_Implicit_m5B9FB268943E6CAB6E40E13D30BA49A9AC7D2059,
	Vector2Int_FloorToInt_m11F1E02A791A7C690228999EA6E9F84FB0DF56FD,
	Vector2Int_op_Equality_mD80F6ED22EA1200C4F408440D02FE61388C7D6BA,
	Vector2Int_Equals_m6D91EFAA6B3254334436BD262A4547EA08281BA3,
	Vector2Int_Equals_m32811BA0576C096D5EB5C0CFD8231478F17229A6,
	Vector2Int_GetHashCode_mA3B6135FA770AF0C171319B50D9B913657230EB7,
	Vector2Int_ToString_m6F7E9B9B45A473FED501EB8B8B25BA1FE26DD5D4,
	Vector2Int_ToString_m44BA6941AEF41076A39848B95DDEFEA88A094B5E,
	Vector2Int__cctor_m1851D9EC457C9518497A216CC821CB4D27BAC2DD,
	Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C,
	Vector3Int_set_x_m8745C5976D035EBBAC6F6191B5838D58631D8685,
	Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72,
	Vector3Int_set_y_mA856F32D1BF187BD4091DDF3C6872FD01F7D3377,
	Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED,
	Vector3Int_set_z_m5782180F67C4257C505F124971985D99C3422F74,
	Vector3Int__ctor_mE06A86999D16FA579A7F2142B872AB7E3695C9E0,
	Vector3Int_op_Equality_mB10073AF3B08421C46BF678C8FF64AAD62C83617,
	Vector3Int_Equals_m419967067E76BF0381E4CD8FE14DF5ED46ACFB02,
	Vector3Int_Equals_mE4D179C5001B77DE05E3E4BC39DC9F6AE441EBD8,
	Vector3Int_GetHashCode_mFAA200CFE26F006BEE6F9A65AFD0AC8C49D730EA,
	Vector3Int_ToString_m49EB16DEA24181270D65A0F4ED39B3E8A46DB539,
	Vector3Int_ToString_m74C285E175F089CED3A53A678216CD15A0AD0067,
	Vector3Int__cctor_m1D349715BC46140220E3F8E867861CF9F568ED65,
	Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF,
	Vector4_set_Item_mF24782F861A16BB0436C2262FA916B4EE69998A6,
	Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813,
	Vector4_GetHashCode_m53F6FCA56A0051C9D1AB41AA3EAA2C86CDAA8B92,
	Vector4_Equals_mC2596CA0F441D25DE7A9419BE66A8FA2DA355CB9,
	Vector4_Equals_m73FAA65A1A565EE28D6C9385603829894B7D4392,
	Vector4_Dot_m40A3B2E258E53E4847583474E40AC29F68AF8BA3,
	Vector4_get_sqrMagnitude_m864A2908BCF9E060BA73DE3DD259EC06F47F913C,
	Vector4_get_zero_m3D61F5FA9483CD9C08977D9D8852FB448B4CE6D1,
	Vector4_op_Multiply_m02FE150FD7366546FC19A36B6928512376BF64E8,
	Vector4_op_Division_m9B1B8692D50C864CFA585BDF97FB6FBC18967D90,
	Vector4_op_Equality_mCEA0E5F229F4AE8C55152F7A8F84345F24F52DC6,
	Vector4_op_Inequality_mD6A1C6E862F3EFB1B222A2DDCB7A7237042DE142,
	Vector4_op_Implicit_m2ECA73F345A7AD84144133E9E51657204002B12D,
	Vector4_op_Implicit_m0217ADDC8CADDB93ACBABB17A50207698DAB0071,
	Vector4_op_Implicit_mB193CD8DA20DEB9E9F95CFEB5A2B1B9B3B7ECFEB,
	Vector4_op_Implicit_m6673D431FBCA5AFB6CF06CD9783D07A4C90CC2AA,
	Vector4_ToString_mFA0DDF34C1E394F75EF65E06764A1BE750E7F388,
	Vector4_ToString_m2BE67BEBBD3059C9CEE29BF34AD30E1D7057E914,
	Vector4__cctor_mE18131D0C5FFBF3305D29CB0F337057BF8CEB802,
	TypeDispatchData_Dispose_m5E70993B62AFC8BB19163A8D7711BD3B5CC9424C,
	TransformDispatchData_Dispose_m1C2E3B8C249D6E8785C19CC17C7ACBA4167A31BF,
	ObjectDispatcher__cctor_mA8CA2F8BC2ABD7806F5A05D683DB78208EDBDB9D,
	U3CU3Ec__cctor_m8766AEC9AA751EEF02B6EF76931FE6D163858575,
	U3CU3Ec__ctor_m664FC291635917D034E486E0BFE015135BAB01E4,
	U3CU3Ec_U3C_cctorU3Eb__54_0_mD6C58FAB3C4B1F6D05D271D4EB6BA99BDBCD4542,
	U3CU3Ec_U3C_cctorU3Eb__54_1_mE001C98479DA3376C0CA590F7761221C7D57BCD6,
	NumericFieldDraggerUtility_Acceleration_m68C7F9D2F37BF3F5658F30022507A438C4CD7BE1,
	NumericFieldDraggerUtility_NiceDelta_mF0E1FACF86DA1579771FD66E98B0B3099A5D88CB,
	NumericFieldDraggerUtility_CalculateFloatDragSensitivity_m3D2CA2BA1D321F49061EE76A82868CBF44A08BA7,
	NumericFieldDraggerUtility_CalculateFloatDragSensitivity_m15371E9C38110F5D47A63B328E1F8B91851F9331,
	NumericFieldDraggerUtility_CalculateIntDragSensitivity_m18783F2C97722F41968E8CB9F2D1A15ED347A6BA,
	NumericFieldDraggerUtility_CalculateIntDragSensitivity_m2151DB0891CAF25A9D460E5D6ADB05D1262112D4,
	NumericFieldDraggerUtility_CalculateIntDragSensitivity_mC168204AC43790918502BAE324E736E34B91DB01,
	NumericFieldDraggerUtility_CalculateIntDragSensitivity_m1B98B6FC83BA9B2234B93A578F253B01C45E7E22,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_SendMessage_m561CA89041EBAFF52314B0D35F7335AF03690EE2,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_TrySendMessage_mD5B897823668810AC92B7A4DB37D2AC469B5AD92,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_Poll_m81154E92307248D79B91A1C1EA75BEF458573D02,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_RegisterInternal_m75ADFE89F806D7CCDA39F4BEB6E13F98789EC3A4,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_UnregisterInternal_m52F310CFE97694C751D5B1FEC47D42C544CB221B,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_Initialize_m2DF230DCAD67005FD0517D836520E73944A6CF71,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_IsConnected_m6C83428E73F46078BA1407A17A69702BC8F34956,
	PlayerConnectionInternal_UnityEngine_IPlayerEditorConnectionNative_DisconnectAll_m6143241C82774D473C8979A05BE0CE3ADAF37F98,
	PlayerConnectionInternal_IsConnected_mB791A845711EE6CEB671A7E103E5A04FD4F48398,
	PlayerConnectionInternal_Initialize_mCE19DC91D92DF51D2A41F389BDD32E959AC1718A,
	PlayerConnectionInternal_RegisterInternal_m32D34DB4DDFEDEC14B79F692591E8C606F2F45C2,
	PlayerConnectionInternal_UnregisterInternal_m1289478D06B17D3EE7E0CF45DE0B93AD97D2BBD2,
	PlayerConnectionInternal_SendMessage_m7CD63211EED2FBC267D2FA47CBA8743CCB202620,
	PlayerConnectionInternal_TrySendMessage_m1181E4B71EB5AC3C5B885C875E3A59378E6E75D6,
	PlayerConnectionInternal_PollInternal_m8EF1E92D3DA699EB3BBB509820CB8B35EB3D2940,
	PlayerConnectionInternal_DisconnectAll_m48CA9F5E0FAA2FAB7A35838049289E063A78D6D8,
	PlayerConnectionInternal__ctor_m522FC54B8DAAD52F95A07B1CA9E5AF0E36B13F45,
	PropertyAttribute__ctor_m19247686E165101F140615C7306DC2DA3953D97D,
	TooltipAttribute__ctor_m9DA2380A6739B9D40E142C81E691BEBC7A79116F,
	SpaceAttribute__ctor_mD1981FDED1C938017DBD5557E292408BC8F2618E,
	SpaceAttribute__ctor_mAA6E6B2811F7585F504FB0C06D52ABF82F560290,
	RangeAttribute__ctor_mFB67BD2663AD5B0D79F5E2D7E494FA8FCB21C6FF,
	MinAttribute__ctor_mE569E9A31ED222B7128543394AAF44DA09839262,
	MultilineAttribute__ctor_m5BAEA5EB5EB078477AC5007100DE70EC06F95BBE,
	TextAreaAttribute__ctor_m5FEE25A73C001A99BC7A86895A0B88CF64FD6FA4,
	PropertyNameUtils_PropertyNameFromString_m41A281EA1DD3AE83448598B2FCFAD0ADAB648675,
	PropertyNameUtils_PropertyNameFromString_Injected_m616D5991D47A904E8098332548230DF6CF7CB37B,
	PropertyName__ctor_mFA341118B63F88B69464A6F1DF248B569686D778,
	PropertyName__ctor_m9F28B1DA9CEDDEAA6663C795456B9F5E95CAE92F,
	PropertyName_IsNullOrEmpty_m80390EB235EF6A983214067BF86BCD6DBA2D1AEB,
	PropertyName_op_Equality_m86CFB3121BF5927D1D4D425A8272980CAAB73DAC,
	PropertyName_GetHashCode_m7D4B878BA78625CDD78C9B045B5EADFDBF94C378,
	PropertyName_Equals_mFD87005C352B7BEB4279560B1A489409B0692143,
	PropertyName_Equals_m7D00F3B148E65210A82101C31F1F217C9527113E,
	PropertyName_op_Implicit_m45CBB51D96B85B596D3094636A335CD475E10F4F,
	PropertyName_ToString_mDE271855F7B9A516185A66E12D90FE6B6C9DF6C0,
	ResourcesAPIInternal_FindShaderByName_mAD5ED1470F0DE9CE9A95145187281879AE61B56D,
	ResourcesAPIInternal_Load_m80AAD6198709E0D3799797CA6D1F3995B1CA2EC2,
	ResourcesAPI_get_ActiveAPI_mF850D558242EC69D97A42BCAEC2270D71F3250A4,
	ResourcesAPI_get_overrideAPI_m2D010C9668878DA21ED92CADE587528848BB98E9,
	ResourcesAPI__ctor_m27A1A0B35DF74437840934A70F47DE1BAEAFECA0,
	ResourcesAPI_FindShaderByName_m9A6287AA24EC06DBF3B2630618015B5CB6F01315,
	ResourcesAPI_Load_m54EE8C7AFD4B386B751AFC8C161B62CB2C983844,
	ResourcesAPI__cctor_m29BD424C62AF8562DC203416CCD86EFDD8F588F4,
	NULL,
	Resources_Load_m6CD8FBBCCFFF22179FA0E7B1806B888103008D33,
	Resources_GetBuiltinResource_mD67A5C47BA93B04E0A9398611A735EA1803CAE5B,
	NULL,
	AsyncInstantiateOperationHelper_SetAsyncInstantiateOperationResult_mDF02114A7EA0900435DB628E06A2C0D740CC1D7B,
	AsyncOperation_InvokeCompletionEvent_m477EBEDE3FE8992BDC1DBFE02A313193CDA46DD4,
	AttributeHelperEngine_GetParentTypeDisallowingMultipleInclusion_m699E7A570CBD00B81273CDFB539E1437D8A0E7DC,
	AttributeHelperEngine_GetRequiredComponents_m40EADB1A403020D292A62108D295B67111F10738,
	AttributeHelperEngine_GetExecuteMode_m6863AD945E6A41B55DA50DB77C86773B088F86FF,
	AttributeHelperEngine_CheckIsEditorScript_m096C287F38FC9802504C6FBE079A28667358FF37,
	AttributeHelperEngine_GetDefaultExecutionOrderFor_mF88FB3280A2C5AF1FE51080021A616C3437CE053,
	NULL,
	AttributeHelperEngine__cctor_m24142033410C104D41A37E17A88149062046CA81,
	DisallowMultipleComponent__ctor_mCED73439170619124E9FE0303137D0A7130C03B2,
	RequireComponent__ctor_mB1C4FD7EA20C0ADA84C7956B948A96856B2465A9,
	AddComponentMenu__ctor_m0C9845C59ED5CB4BEDB86EBD14EC574E13240C1B,
	AddComponentMenu__ctor_mFC3F5A41F4F41587531E9352DEC243EE49A01C25,
	ExecuteInEditMode__ctor_mAA44FC0120898DDA33D063EF3692B7F0F22AD792,
	ExecuteAlways__ctor_m2792EFBEBCECA35F3C1EB12B3BE9290B734C4A46,
	HideInInspector__ctor_m6F39BCE00C935AB46163661E9D4B0B6A6B7976DE,
	HelpURLAttribute__ctor_m4671D9179DCF032E9F769A70A1665B1B60B233A7,
	DefaultExecutionOrder__ctor_mD02339C868E98633AB5836930A963A506CCC9D1D,
	DefaultExecutionOrder_get_order_m362E5F2AB40AAA5154301F88DE93B80F865A31AF,
	ExcludeFromPresetAttribute__ctor_m6BBE5C9A0F32E56C97AE0A9A6FB66CC7DDC8C93A,
	Behaviour_get_enabled_mAAC9F15E9EBF552217A5AE2681589CC0BFA300C1,
	Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A,
	Behaviour_get_isActiveAndEnabled_mEB4ECCE9761A7016BC619557CEFEA1A30D3BF28A,
	Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8,
	ClassLibraryInitializer_Init_m0B7E37DD365582370981A7697AB7273F7A20FF0A,
	ClassLibraryInitializer_InitStdErrWithHandle_m88C6AA53D2E42443B5456B72FC78F09E13DC2504,
	ClassLibraryInitializer_InitAssemblyRedirections_m5BA3935ED238E65FDACBE96E908377F37545CF54,
	U3CU3Ec__cctor_m90743B80C6B01F70F5DD18094ED9917A4D66D75C,
	U3CU3Ec__ctor_mAAF6A78995DFBFBEADE41ECEAFC4482A3354A1F8,
	U3CU3Ec_U3CInitAssemblyRedirectionsU3Eb__2_0_mED19C4287A4E13D96147812233377C55661C83EB,
	AssemblyVersion_op_Equality_m812148ACA50C9FD561C823DF1F6116A73711762F,
	AssemblyVersion_ToString_mEB86A8E88D80F7C7459258EDF519B208400FA74B,
	AssemblyVersion_Equals_m9FA3C12B7C0743C563E712CD08672EF9E99E1ADD,
	AssemblyVersion_GetHashCode_mC191CC77C4664CD9A3F59E18BEE76E6BB5DA336F,
	AssemblyFullName_Equals_mCCD71330EA2201DC5C3452AEEFF051AF203D0E9E,
	AssemblyFullName_GetHashCode_m470DD9F4F78B21172D3A438C139ABF5587B0C4D0,
	AssemblyFullName_ToString_m4D31288544EE855F460B3833AB0D51DF0AEADCEF,
	Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371,
	Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B,
	Component_GetComponent_m4352437B839B9601EB007CAC1007C8E1FB8A427D,
	NULL,
	Component_TryGetComponent_mC2472304301C1523E25A5FACD0BCD1CC471B7BD0,
	Component_GetComponentInChildren_m4050899166730E8F6629B895597CF4ECC894B597,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Component_GetComponentInParent_mA402B7F87D23E039BB045630E5EA3AC020D8A7C2,
	NULL,
	NULL,
	NULL,
	NULL,
	Component_GetComponentsForListInternal_m7B3A61AF88E5E6F845817EC28E22FD39944EFBC7,
	Component_GetComponents_mFD04B9CB0BD37505417054D614436B5844DC4365,
	NULL,
	NULL,
	Component__ctor_m4319162A6E6B02301078C1233F6E7F4A3E735486,
	Coroutine__ctor_m65324E0C6062163C0378CD4190E903B4C2ED75DD,
	Coroutine_Finalize_m83673D20AB464E4C866408F798D5CA1F0391808D,
	Coroutine_ReleaseCoroutine_mEEFBA6D7CABF4E6FAF7C115B53BF530B30208869,
	SetupCoroutine_InvokeMoveNext_m72FC77384CAC3133B6EE650E0581D055B34B2F5F,
	SetupCoroutine_InvokeMember_m988CC4721CD62977A75098C58C52A292457E9111,
	NULL,
	CustomYieldInstruction_get_Current_m6F1287B6C4A16C875913A556D66ACB1BF599E6AF,
	CustomYieldInstruction_MoveNext_m875CC6944F46765EB3BE14306E83E7C54CBEB2CF,
	CustomYieldInstruction_Reset_mA357794CF2260A3E48748612BDE01CB0B7CF8A8D,
	CustomYieldInstruction__ctor_mB64531EC09E871EF60BFAC16918A774C977C7B50,
	EnumDataUtility_GetCachedEnumData_m5A1FBC1D3D748CB99C3B76EDA6FD89E73777EBF6,
	EnumDataUtility_HandleInspectorOrderAttribute_mB4880BEC4F5D38D3CDF931A62D0B2312B2B3989D,
	EnumDataUtility_CheckObsoleteAddition_mDA292A1BB417654B3A42A3ABAC80D5A0889AB4F3,
	EnumDataUtility_EnumTooltipFromEnumField_m9A7A43EF79E0D58F5A5B12E85D4D7FE323954896,
	EnumDataUtility_EnumNameFromEnumField_m530D557811A64AECFB98038D8FC7762F7F825CF2,
	EnumDataUtility__cctor_mAE8CBD4D762193E934C4FEA724E2B21CCB632577,
	EnumDataUtility_U3CEnumNameFromEnumFieldU3Eg__NicifyNameU7C8_0_m06FD95EB39F7F5EEC3EF1B71D7AFBA2AD8F26743,
	U3CU3Ec__cctor_mAA1051B42F26135CC959F5EC7890436FAB2ED941,
	U3CU3Ec__ctor_m0DFD48E0843CFE7BBD1816E7FB17F1A92545B9F6,
	U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_5_m22CC7BA755DF7C90E69EBAE15CCD2B6F2357D768,
	U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_1_mFABC2F08601EDC5AB10DC6CBCD6870A533C78DF9,
	U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_2_m8D831A7EFA8414C67A47371867AA72BE854E81D5,
	U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_3_mFF89FAEBB1E42BD08E9A554D3BD8B5E8D0B58B5C,
	U3CU3Ec_U3CGetCachedEnumDataU3Eb__2_4_mB79B52279F2BE5BE38B132960B29AA87EACE5A2F,
	U3CU3Ec__DisplayClass2_0__ctor_mD6D5EFE439B60B998596D46FEC76C110A2B81DDB,
	U3CU3Ec__DisplayClass2_0_U3CGetCachedEnumDataU3Eb__0_m6FA97FBACA28AFB3C0353743AC9F747610619CAD,
	ExcludeFromObjectFactoryAttribute__ctor_m547CE65CC098EB4466C456476ECF2D78E2834FBB,
	ExtensionOfNativeClassAttribute__ctor_m64B864DD565CE1BF04DA861E6759163A1727F6A1,
	NULL,
	GameObject_GetComponent_m99E12753EA84947521DC711CA33F452B5E65B474,
	GameObject_GetComponentInChildren_m4A3692D1D93C726D5B02E588130C782A336961D5,
	NULL,
	NULL,
	GameObject_GetComponentInParent_m80F84FC4D405C1F9987C0E77385749814AD0027C,
	NULL,
	NULL,
	GameObject_GetComponentsInternal_m5D5FD903F9CB151AC9782E5840D397F422A82F95,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	GameObject_TryGetComponent_mC89200887E029C940C0E16EDC0BC7DE0CCDD0CF2,
	GameObject_TryGetComponentInternal_mF4570971BBC0A69178D89A298F67C1D60E3881E5,
	GameObject_Internal_AddComponentWithType_m2D986297A2133DD6896D9715F22C9627991D07FF,
	GameObject_AddComponent_mDF246771EC34613FA6AF0C98D443368FB43E9F36,
	NULL,
	GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56,
	GameObject_get_layer_m108902B9C89E9F837CE06B9942AA42307450FEAF,
	GameObject_set_layer_m6E1AF478A2CC86BD222B96317BEB78B7D89B18D0,
	GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92,
	GameObject_get_activeSelf_m4F3E5240E138B66AAA080EA30759A3D0517DA368,
	GameObject_get_activeInHierarchy_m49250F4F168DCC5388D5BE4F6A5681386907B109,
	GameObject_SendMessage_m452B3418BE240EC79550C15E1F2EAE61488F06DF,
	GameObject__ctor_m37D512B05D292F954792225E6C6EEE95293A9B88,
	GameObject__ctor_m7D0340DE160786E6EFA8DABD39EC3B694DA30AAD,
	GameObject__ctor_m721D643351E55308EA4F5F41B67D5446D11C61F0,
	GameObject_Internal_CreateGameObject_mA49D858513048B4CF239D4201C89903F4ECE8498,
	InspectorOrderAttribute_get_m_inspectorSort_mB97F50A2118A606A94869ADFB548F8C747EDD14D,
	InspectorOrderAttribute_get_m_sortDirection_mC0FE63CA21C2158B7FD0C09659418A69F50D1313,
	LayerMask_op_Implicit_m7F5A5B9D079281AC445ED39DEE1FCFA9D795810D,
	LayerMask_op_Implicit_m01C8996A2CB2085328B9C33539C43139660D8222,
	ManagedStreamHelpers_ValidateLoadFromStream_mF6E14BAD1BC52711F99076381E5A57DA650B4C1A,
	ManagedStreamHelpers_ManagedStreamRead_mB44F01233FDB11E124CC64899912608FB5ECEEE5,
	ManagedStreamHelpers_ManagedStreamSeek_m97EDF5D08F83234C056003A9890C2C3478EECC4B,
	ManagedStreamHelpers_ManagedStreamLength_mF75367CF96464E01EDF84070FF038125649AEDFD,
	MonoBehaviour_get_destroyCancellationToken_mBA72081B6235F4E1D10E9BA6D60005DD48730A10,
	MonoBehaviour_RaiseCancellation_mFCD352361B8F5B9C9BA028967C2E49A22034712F,
	MonoBehaviour_IsInvoking_mF7CF0A2ABF31B61FC67A75E9210C16683E5020A0,
	MonoBehaviour_CancelInvoke_m177BCBDFCEA3E09C02E3E444BF4FBA648FAE3CFA,
	MonoBehaviour_Invoke_mF724350C59362B0F1BFE26383209A274A29A63FB,
	MonoBehaviour_InvokeRepeating_mF208501E0E4918F9168BBBA5FC50D8F80D01514D,
	MonoBehaviour_CancelInvoke_m268FFD58AFF64C07FD4C9B9B8B85F58BD86F3A01,
	MonoBehaviour_IsInvoking_m9CD08C2F7F5E83660FFE3B5A373B202CCBDB3708,
	MonoBehaviour_StartCoroutine_m10C4B693B96175C42B0FD00911E072701C220DB4,
	MonoBehaviour_StartCoroutine_mD754B72714F15210DDA429A096D853852FF437AB,
	MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812,
	MonoBehaviour_StartCoroutine_Auto_m97F469F18612A2208D2EFDB3274DF0B4E3C9F4E6,
	MonoBehaviour_StopCoroutine_mF9E93B82091E804595BE13AA29F9AB7517F7E04A,
	MonoBehaviour_StopCoroutine_mB0FC91BE84203BD8E360B3FBAE5B958B4C5ED22A,
	MonoBehaviour_StopCoroutine_m1DA0B9343DCDB53221A6CD707CBF0827A6FFF17F,
	MonoBehaviour_StopAllCoroutines_m872033451D42013A99867D09337490017E9ED318,
	MonoBehaviour_get_useGUILayout_mBCD040C678BF8521BFBAB8FD59BC566B1F5BED89,
	MonoBehaviour_set_useGUILayout_m56F0C62F4B6889D7472074ECCB56EBA462285134,
	MonoBehaviour_print_m9E6FF71C673B651F35DD418C293CFC50C46803B6,
	MonoBehaviour_Internal_CancelInvokeAll_mCBF8624858F884C064BC83740A0EF97A59461E83,
	MonoBehaviour_Internal_IsInvokingAll_mCA9B8812ED7656429C2298F272AC1F654C40C3DA,
	MonoBehaviour_InvokeDelayed_mBD74A4D793E6836BEC5A0FABDA7FD573A87ACD4C,
	MonoBehaviour_CancelInvoke_m794BA6F7C1040DDA0D62D37AF97883E23627DC12,
	MonoBehaviour_IsInvoking_m05A8A914FA3563CF28F1016095BA3A735F319A99,
	MonoBehaviour_IsObjectMonoBehaviour_mC2F75720102B56F81F3D1329BE96C2C7B336B615,
	MonoBehaviour_StartCoroutineManaged_m014E764B40594337F2B5AA03BFFC87DD8D4B063B,
	MonoBehaviour_StartCoroutineManaged2_m55C19C5C5C65E9883E12101A46F37AB1172C73E8,
	MonoBehaviour_StopCoroutineManaged_m35C1C524554F9B058538E41E0650FA71373F292D,
	MonoBehaviour_StopCoroutineFromEnumeratorManaged_m81B57000F7ACB16B333800D66E8C74E7481E20B8,
	MonoBehaviour_GetScriptClassName_m428B33342B759D78A638B6E383F0510F294DE285,
	MonoBehaviour_OnCancellationTokenCreated_m9D65DA2F7558F7B428D28713891BA05116719179,
	MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E,
	NULL,
	NULL,
	NoAllocHelpers_SafeLength_m4EC7245A2215693CE163E3F6ECFB105D742087DF,
	NULL,
	NoAllocHelpers_Internal_ResizeList_mC468D22C6FEE802726262F9B7CFC3DC23A5DD40C,
	NoAllocHelpers_ExtractArrayFromList_m3207C25A27FDFB2B9A96D4BB34ECB7C26AAAED24,
	RangeInt_get_end_m5835FBEB410CB8AC0928AEFD95728A9AD57F6C63,
	RangeInt__ctor_m3CB91E79C7B5AED97E564581025B2F66778B7CBE,
	RuntimeInitializeOnLoadMethodAttribute__ctor_mA563B1C8896C6490F88C060E13950DB600577352,
	RuntimeInitializeOnLoadMethodAttribute_set_loadType_mCB29F9D9D69AB012FB797AB433AD3CFCE696AF10,
	ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF,
	ScriptableObject_CreateInstance_mE015043D7EC19654FDFB830A9393B3914FF5FC24,
	NULL,
	ScriptableObject_CreateScriptableObject_m63730D18415FC4EC61F77C57A4D184064E82D2C0,
	ScriptableObject_CreateScriptableObjectInstanceFromType_mDA90002F7BB98734237184C1E160C7763B5A0AD6,
	ScriptingRuntime_GetAllUserAssemblies_mA90D59F0C8D15BA303AD3EA52F992DFB3BE1C7F2,
	ScriptingUtility_IsManagedCodeWorking_m023FD7C1DFD42CA5AA1F40219DDED8033F0BC3B6,
	SelectionBaseAttribute__ctor_m6B586E58B9F8CF4D6ADF3802179F67174301F063,
	StackTraceUtility_SetProjectFolder_m60DBA844239D34388650DB96CE47ED18835EE486,
	StackTraceUtility_ExtractStackTrace_mFDB05BC4CA207364FFC93F44E29F35A271DF5200,
	StackTraceUtility_ExtractStringFromExceptionInternal_m57F173812EF87153715A42DCE44D2F069F528FF3,
	StackTraceUtility_ExtractFormattedStackTrace_m3857524451432DB6A0A5445E57B3DFC30369B0BB,
	StackTraceUtility__cctor_mA6A35A8164A7DB8BE1BF989E02D5F0B2067ACAFB,
	UnityException__ctor_m2FFB2690639A7421DA5DBFC40091AB23D87EA90F,
	UnityException__ctor_mF8A65C9C71A1E0DE6A3224467040765901959312,
	MissingReferenceException__ctor_m9A8445A472E11453B6C6C93319A314179C363E5A,
	MissingReferenceException__ctor_m38AAD807B4CA850C73C5CAEC30ACE86628D94291,
	TextAsset_get_bytes_m244B31755642C9623B570FC96B9A04523B1E5178,
	TextAsset_Internal_CreateInstance_m9EB31EDB11B4AC96E3CBF5EC3E8FE018D71EE0AD,
	TextAsset_get_text_m36846042E3CF3D9DD337BF3F8B2B1902D10C8FD9,
	TextAsset_ToString_m6A506652E11BF9679E6BE931EA2844035FF68923,
	TextAsset__ctor_m27C97A718A563E124A07FAE057A69D4D44A93595,
	TextAsset__ctor_mC8EEBC230157B4C459E87D591C722D8803FB5846,
	TextAsset_DecodeString_m3345EAF0DE3FE4AAF99F105269E0B83C528198D9,
	EncodingUtility__cctor_m15A59251290A2D2B0CD00F635C7C30F3B033A40B,
	UnhandledExceptionHandler_RegisterUECatcher_mDBA57543CC57B4265B7847FE9F4E51125FE5F61F,
	U3CU3Ec__cctor_m98D2C4143206DF565508D9D1A8FB0C38851DA96A,
	U3CU3Ec__ctor_mFC700DE43CA4803F7317D49C21D6010E21E4C40D,
	U3CU3Ec_U3CRegisterUECatcherU3Eb__0_0_mD21DF44CEC3DA35516DA5FC75CA8A2FF67E4E382,
	Object_GetInstanceID_m554FF4073C9465F3835574CC084E68AAEEC6CC6A,
	Object_GetHashCode_m3FA03DBF8CFF6584BCD22BCFDD257AED8DEB5872,
	Object_Equals_m086D5CEE892DA62DEE463ACFBA493174C56EDAD0,
	Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A,
	Object_CompareBaseObjects_m0686B5F55766F49B84E0AE0D4F70DFB9BEA3861C,
	Object_IsNativeObjectAlive_m477E072492E19666F582F31A101C991BA107014F,
	Object_GetCachedPtr_m3B66BC474B4F735F68184F00248385BAF9AF650B,
	Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392,
	Object_set_name_mC79E6DC8FFD72479C90F0C4CC7F42A0FEAF5AE47,
	NULL,
	Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436,
	Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB,
	Object_DestroyImmediate_m737E5829FEEAE70EE7A004D172042D52E336E1E3,
	Object_DestroyImmediate_m6336EBC83591A5DB64EC70C92132824C6E258705,
	Object_get_hideFlags_mA08F5E41671B8C6B5073C6B9E2799BCE6E0DF7F3,
	Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4,
	Object_CheckNullArgument_m4D03BBBD975CCCCB3F9438864E3E8BF54E1E3F26,
	Object_ToString_m590B13E2C40DB814E3CF9C3615952B3CC7C4B36C,
	Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605,
	Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602,
	Object_GetOffsetOfInstanceIDInCPlusPlusObject_m45636617A1C7BC9D5D3064F8FBF18785126F22F8,
	Object_Internal_CloneSingle_m24ECA1416702930DF5C316EA8B70D575315B636A,
	Object_ToString_mC776F339F5986CCB1F1D7901F335B9A61BDC0C9B,
	Object_GetName_m514CD5F3EA9F7BC4DFD914C98D4AA91C45DB3AD9,
	Object_SetName_m284935BE1D41FFDFDEC73986CB228B1CCAED7EC5,
	Object_FindObjectFromInstanceID_m977F314530A838CAB5497C8F5D0D8DA134B92E0C,
	Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6,
	Object__cctor_mED93981CFB29C99D893E0C7ADB4B5BCB2A6C7903,
	UnitySynchronizationContext__ctor_m4BA1C7C04C4B82783FDC935C2C50639211B11F5C,
	UnitySynchronizationContext__ctor_m3CC3D812A97540AB584CECA78B735D68FF30E4F0,
	UnitySynchronizationContext_Send_mB69AAB0638FC77BD51BFA5BF7D2B0D568BBEB7F4,
	UnitySynchronizationContext_Post_mD36839040EBAB66116699E68558BB8FDFF0FD834,
	UnitySynchronizationContext_CreateCopy_m11E8C66F575BC1DF3F34A614E6B00D040745301A,
	UnitySynchronizationContext_Exec_m7E6352CBA70E4AB14F7D50A919C3F8C22F2E977A,
	UnitySynchronizationContext_HasPendingTasks_m5B62CCDDC05BCC946CB158FECC5FA6422DE1C960,
	UnitySynchronizationContext_InitializeSynchronizationContext_mF6CADD1D15642024F450D918CE1DEA1A4128818A,
	UnitySynchronizationContext_ExecuteTasks_m3034D59292339809C6D7FA5932ACC365892B8CA1,
	UnitySynchronizationContext_ExecutePendingTasks_m30855D2E24B36B23076FDDF03D6DE93F0433E4F9,
	WorkRequest__ctor_m78DC33ED88BF9BA29E05D2180B2ADC439132F1F5,
	WorkRequest_Invoke_mBFEC6A3799BAFDE7ED840A0523D3D56160C03921,
	WaitForEndOfFrame__ctor_m4AF7E576C01E6B04443BB898B1AE5D645F7D45AB,
	WaitForSecondsRealtime_get_waitTime_m385602BA6C66A6169695FE9AD78F2B24F4FF8573,
	WaitForSecondsRealtime_set_waitTime_m8317E2B7A2B2DC4E4A1B0CD0F9D4479294B51FB5,
	WaitForSecondsRealtime_get_keepWaiting_m175C5DD180DFA38A323FA6C4C8EB058DBFC0A7CA,
	WaitForSecondsRealtime__ctor_mBFC1E4F0E042D5EC6E7EEB211A2FE5193A8F6D6F,
	WaitForSecondsRealtime_Reset_m44618BC7268A087CA316629EDF87282D37B6EAA4,
	YieldInstruction__ctor_m23280B9DFE9C3E80554A656B4E7125BC9B2C027B,
	SerializeField__ctor_mD3D7B72B71C1F3F70060E2830710F48F965C753E,
	NULL,
	NULL,
	ComputeShader_FindKernel_m3BA5C50794FA6AF4C432E55FBBD7CB266532F659,
	LowerResBlitTexture_LowerResBlitTextureDontStripMe_mEECB7042CAE059B760ABFEC12902030249248A8F,
	PreloadData_PreloadDataDontStripMe_mB4D9C8C36672A400FBE7E024C47E0FD152BABA02,
	SystemInfo_get_operatingSystemFamily_mC8B13A7D2F34699EDDA4F7629F117C76F0C865FA,
	SystemInfo_IsValidEnumValue_mF380A352BF2023F7D9B278873ECFCD56A4EE2F32,
	SystemInfo_SupportsTextureFormat_m833B0ABED13B5B8D0D4BCF082F3EFA51A3B5C860,
	SystemInfo_get_maxTextureSize_mEE557C09643222591C6F4D3F561D7A60CD403991,
	SystemInfo_get_maxRenderTextureSize_mD9AB6274BEAC0CDCF9AF26B3DC19CD57E548A6FE,
	SystemInfo_GetOperatingSystemFamily_mE40096D3196E10CC8940F79B729CA985D1CD4396,
	SystemInfo_SupportsTextureFormatNative_m47D5DEAD487F9046B4F77B8E9D880D97DD59ACEB,
	SystemInfo_GetMaxTextureSize_m69CD656F42B72D3E49A923F174C701712B9C94EC,
	SystemInfo_GetMaxRenderTextureSize_mFDD81060C8E82A4D1F567BE54DB81EC3CC529115,
	SystemInfo_IsFormatSupported_m412D2A8B391BDBCD1EDB5C17ADAB724CDB123499,
	SystemInfo_GetCompatibleFormat_m3A1DEC64F2C85F1D7C45005009D93EFA33C8419B,
	SystemInfo_GetGraphicsFormat_mF4A09D38BA91B8F783C9189B5D744BA943292E0E,
	Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865,
	Time_get_unscaledTime_mAF4040B858903E1325D1C65B8BF1AC61460B2503,
	Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5,
	Time_get_realtimeSinceStartup_m73B3CB73175D79A44333D59BB70F9EDE55EC9510,
	TouchScreenKeyboard_Internal_Destroy_m83A7EDD06362B64AF552170085E3598D31625F70,
	TouchScreenKeyboard_Destroy_m8B3C649CE02C77170A0F41D8EEF6E7465F8B9CAD,
	TouchScreenKeyboard_Finalize_mCAFC1635D50B4AF9EEBFB0A5DC1D5027A2FEAA6B,
	TouchScreenKeyboard__ctor_mA0C6FA07182A13B8F3F62731B94CAAD83F340861,
	TouchScreenKeyboard_TouchScreenKeyboard_InternalConstructorHelper_m14B4B7D80E368896C0CC49EA76D2B3208DC7D0F1,
	TouchScreenKeyboard_get_isSupported_mCFAC95CA6CAA06B4E21F42E3C40A39668D6B438E,
	TouchScreenKeyboard_get_disableInPlaceEditing_m063C253E629D35E9C1DEC911EA785AA796D35252,
	TouchScreenKeyboard_get_isInPlaceEditingAllowed_mE57CDF32437DD1DCDC665072457C91DD31EB93D0,
	TouchScreenKeyboard_IsInPlaceEditingAllowed_m457215121F1DEBEEE4D9665358C13F7E3D041AE7,
	TouchScreenKeyboard_Open_m2E1E3A71919EE30C51539DF8A037B74BE2D78065,
	TouchScreenKeyboard_Open_mE24AE590A477F9D027537FD170B1DC02627C4D7E,
	TouchScreenKeyboard_get_text_m74593E81B017446204A9DB1F7359922A2C005B1E,
	TouchScreenKeyboard_set_text_m0A8AA05F4D9D27E8764955F0041452145B6C6FBB,
	TouchScreenKeyboard_set_hideInput_m8FDDB21FB7E9B06B791649BBE369E4EA0F5F5299,
	TouchScreenKeyboard_get_active_mB22402FB9E56D3F652DA785F01E504A530FF8775,
	TouchScreenKeyboard_set_active_m4382D57F87E4C86B59864E86BE93A8A2A474B7C2,
	TouchScreenKeyboard_get_status_mCC466FDEC7E1913D8585ABA7F048FC198CB19504,
	TouchScreenKeyboard_set_characterLimit_mCD8F3BC047EF2515272A689368CF3678A419B854,
	TouchScreenKeyboard_get_canGetSelection_m340ACEFDB9609DEED4FE7D451A4DCCC1024F767A,
	TouchScreenKeyboard_get_canSetSelection_m6CD6C069A9FEF91CC8014B877EB057ECF598EDF9,
	TouchScreenKeyboard_get_selection_m1D44C9A8D4EA91F61706F048ED318E537DC46AB2,
	TouchScreenKeyboard_set_selection_mC27C2948118086822A151118C379FAAF692DB2DF,
	TouchScreenKeyboard_GetSelection_mFE603E50B5D189F96A3E3D93FA5F70E7B2DA9C5A,
	TouchScreenKeyboard_SetSelection_m589290680988DFA5FBABA7336F58907C62D182DA,
	UINumericFieldsUtils_TryConvertStringToDouble_m2F7B52D176293C6C7EE5406D14F92E328F8D7A64,
	UINumericFieldsUtils_TryConvertStringToDouble_m68C49071E5A3ED34D2320458D386C6668AA6AA49,
	UINumericFieldsUtils_TryConvertStringToFloat_m9D49DF90FF6724632ADF8EC96A096D094B2FCBBD,
	UINumericFieldsUtils_TryConvertStringToLong_m9052E90D297DDA0EC1D836B5068D1C6247E8B179,
	UINumericFieldsUtils_TryConvertStringToLong_m3BBC27998E58F710ED5451565A03191D4F137E91,
	UINumericFieldsUtils_TryConvertStringToULong_m1F2F0AFF1E15B2D72AB71B2A9148C969D959DBFC,
	UINumericFieldsUtils_TryConvertStringToULong_mCB90A43CDC68458BE764A5766EFC0C576C13A850,
	UINumericFieldsUtils_TryConvertStringToInt_m8E5245B57793548361F48C6674E35E84B1A272F3,
	UINumericFieldsUtils_TryConvertStringToUInt_m08EE01CCF8950657039C54A46C3F9215E1C65EF2,
	UINumericFieldsUtils__cctor_m97714D6085F635BF543C6CDCB026862FA2B19BF4,
	DrivenRectTransformTracker_Add_mC0CE417831BF58E6DA81770CE5E2A99B142EEFEC,
	DrivenRectTransformTracker_Clear_m9A7F5130E4007F70B14AB1FF13A2997C073A64EE,
	RectTransform_add_reapplyDrivenProperties_m92F5BD1B2500C408447F882F378CB52647AB6E72,
	RectTransform_remove_reapplyDrivenProperties_m265B69E542E3AABF284ED76EDF974C3A83B2D242,
	RectTransform_get_rect_mC82A60F8C3805ED9833508CCC233689641207488,
	RectTransform_get_anchorMin_mD85363930BE38EC188F933B9F4D58320CAB72F03,
	RectTransform_set_anchorMin_m931442ABE3368D6D4309F43DF1D64AB64B0F52E3,
	RectTransform_get_anchorMax_mEF870BE2A134CEB9C2326930A71D3961271297DB,
	RectTransform_set_anchorMax_m52829ABEDD229ABD3DA20BCA676FA1DCA4A39B7D,
	RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680,
	RectTransform_set_anchoredPosition_mF903ACE04F6959B1CD67E2B94FABC0263068F965,
	RectTransform_get_sizeDelta_m822A8493F2035677384F1540A2E9E5ACE63010BB,
	RectTransform_set_sizeDelta_mC9A980EA6036E6725EF24CEDF3EE80A9B2B50EE5,
	RectTransform_get_pivot_mA8334AF05AA7FF09A173A2430F2BB9E85E5CBFFF,
	RectTransform_set_pivot_m79D0177D383D432A93C2615F1932B739B1C6E146,
	RectTransform_get_offsetMin_mD02BE5256DADAF02CEEF99214C4E80478CD5287B,
	RectTransform_set_offsetMin_m07F38B4105C7CA9CC9FBDC9ED0DB008602880AB9,
	RectTransform_get_offsetMax_m6A51C62A2C69780EFD879D3CFE4EE2CBF4AD3D73,
	RectTransform_set_offsetMax_m5514D09D86516F2C0E25FA6D11A3A4274D3D002D,
	RectTransform_GetLocalCorners_m18B3E5ED5EB24AD46279199A134CD7F218D3DD11,
	RectTransform_GetWorldCorners_m6E15303C3B065B2F65E0A7F0E0217695564C2E09,
	RectTransform_SetSizeWithCurrentAnchors_m53A04549B7687A1DEA2C7484E56D89809390CE44,
	RectTransform_SendReapplyDrivenProperties_mCBDC6A65528E92A7E7D5DDC6EB94A058050CFF8D,
	RectTransform_GetParentSize_m462044ABB7282640CCC3929A7BC3FC5609C30C42,
	RectTransform__ctor_m9E23A4767F17F806A633E34487CF311EEBBB0542,
	RectTransform_get_rect_Injected_m2CAD7BD1A157125337831D705F8953CB43EFCA22,
	RectTransform_get_anchorMin_Injected_mA00B0680244E546BE0C7F715CD27BF15708F33F0,
	RectTransform_set_anchorMin_Injected_m062070AA530E9DB918780A05DA5D1A7813594696,
	RectTransform_get_anchorMax_Injected_m33D2FB73C038385395F18D4B75C090476D08F5E0,
	RectTransform_set_anchorMax_Injected_mFD5947257451DAF1230D993A794DA690915443A5,
	RectTransform_get_anchoredPosition_Injected_m101611A28C40B0E8ABE9983AE9ADC26940A2F9D7,
	RectTransform_set_anchoredPosition_Injected_m9B719BB4C1D0E4EDE750CB93E19251DA94573F8A,
	RectTransform_get_sizeDelta_Injected_mB107881782C0DFF99C0EC61A4D423FB3C91D5405,
	RectTransform_set_sizeDelta_Injected_m3690D9F545C7C324591F81934046370DF7F1702E,
	RectTransform_get_pivot_Injected_m1D193D87AE52D7BCF41990B13ADF1A807928289F,
	RectTransform_set_pivot_Injected_m9693DF374A51072B4804362B6E7E065BD4A5B48C,
	ReapplyDrivenProperties__ctor_mC06AEE119C82C068873EE368C7C8DBE9CAD28949,
	ReapplyDrivenProperties_Invoke_m3440A5F41B8B52D671A1C26356CB20CF8E7AC39A,
	Transform__ctor_mB597BB13F66ADC3B8A3D45A2ABDEC8C02B421B93,
	Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1,
	Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95,
	Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134,
	Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F,
	Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C,
	Transform_get_localRotation_mD53D37611A5DAE93EC6C7BBCAC337408C5CACA77,
	Transform_set_localRotation_mAB4A011D134BA58AB780BECC0025CA65F16185FA,
	Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F,
	Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633,
	Transform_get_parent_m65354E28A4C94EC00EBCF03532F7B0718380791E,
	Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234,
	Transform_get_parentInternal_mADE30238BECA9CDE15FB84E208FB5C6FDC31C14E,
	Transform_set_parentInternal_m7AC9FA19DF51534D5E83CB8B495CDC11BA17E979,
	Transform_GetParent_m0DDE14F6AA36850AAEB05B13AB8B18CB02BA875B,
	Transform_SetParent_m6677538B60246D958DD91F931C50F969CCBB5250,
	Transform_SetParent_m9BDD7B7476714B2D7919B10BDC22CE75C0A0A195,
	Transform_get_worldToLocalMatrix_mB633C122A01BCE8E51B10B8B8CB95F580750B3F1,
	Transform_get_localToWorldMatrix_m5D35188766856338DD21DE756F42277C21719E6D,
	Transform_TransformPoint_m05BFF013DB830D7BFE44A007703694AE1062EE44,
	Transform_InverseTransformPoint_m18CD395144D9C78F30E15A5B82B6670E792DBA5D,
	Transform_get_childCount_mE9C29C702AB662CC540CA053EDE48BDAFA35B4B0,
	Transform_SetAsFirstSibling_mBE0D0E76099F829466DC2FBD71ACFCF3C8EC03BD,
	Transform_get_lossyScale_mFF740DA4BE1489C6882CD2F3A37B7321176E5D07,
	Transform_IsChildOf_mFE071BE1E775E825401FE0A9B9BE49E39D256CEA,
	Transform_set_hasChanged_mCE980898F6D52F81E7E6B772DCA89E13A15870AE,
	Transform_GetEnumerator_mA7E1C882ACA0C33E284711CD09971DEA3FFEF404,
	Transform_GetChild_mE686DF0C7AAC1F7AEF356967B1C04D8B8E240EAF,
	Transform_get_position_Injected_mC69C78EAC69F2912B161B5710E69F7D3DC30C815,
	Transform_get_localPosition_Injected_m4EA29DEBBB27D41E17E6F7469217B50B7594DF22,
	Transform_set_localPosition_Injected_mB0AC6C9E6298454212FD390CB010ED9F5B0B075A,
	Transform_get_rotation_Injected_m6532D51417F17B804F56FC809B833F3BE524F0FC,
	Transform_get_localRotation_Injected_m1597F48A85A1F4FFDC4597B66906CC45A0F3A1A1,
	Transform_set_localRotation_Injected_m7F19BC3A69E968FF240368F65E095082A9253B53,
	Transform_get_localScale_Injected_m39985DA5D17EF46D4E8858AA98AF498BCC8A99D9,
	Transform_set_localScale_Injected_m4ECD0F3FCC76D0B0FE5CE13E8B4BBB06489C44F2,
	Transform_get_worldToLocalMatrix_Injected_mDBC9452FF28DF93AA1F7DF84AF1526F08E160963,
	Transform_get_localToWorldMatrix_Injected_m17F83E29C6BC4F3C9891D9F7FF6F2059E09BB21D,
	Transform_TransformPoint_Injected_mC39341E3D842D1AEAF3DE2496BD9CCDE6087D65E,
	Transform_InverseTransformPoint_Injected_m6F7266D1FD453F86081B7286C0848DF8F32593A6,
	Transform_get_lossyScale_Injected_mDCE0BE28BAC6A6A89056535B481A6F84C36E8F68,
	Enumerator__ctor_m83A956CC6A8C1F2318C7660553BAD2D2B25AD71A,
	Enumerator_get_Current_m4732420B5A80C615B7CA373E750542E7F91D93DF,
	Enumerator_MoveNext_mCC9D1D19DCDF3F54E1FFC2798D073C01C202D984,
	Enumerator_Reset_m91311F4F60075A13C6B8A27BC2856B37DC18A63E,
	SpriteRenderer_InvokeSpriteChanged_m105236F9C9637A421F96627823413A679319AFB8,
	Sprite__ctor_m14DB77A0955914B9B8D8189BB1A6B190B3CCF919,
	Sprite_GetPackingRotation_mE9EBA99C7A1F3BCED26BCFB086C136CC1A4358E9,
	Sprite_GetPacked_m020A62AD57E08E1E3AF4F9FF20A77A7E80052684,
	Sprite_GetInnerUVs_m68A07E15B8D8F07E33559998000B524B54E1951A,
	Sprite_GetOuterUVs_m0239F5571EA1AE399B426DE9362EEAC73A3ECC42,
	Sprite_GetPadding_mF346EAFF67C810A108E64366EB5CB3CB2E01D066,
	Sprite_get_bounds_m042F847F6C5118E6B14A3F79A1E1C53E7DFBF452,
	Sprite_get_rect_m2D85032EBEDC505E346E49291B8816BDB18DF625,
	Sprite_get_border_m024C8361A808BF597EC6E1849AADDA9C756B459F,
	Sprite_get_texture_mEEA6640C1B5D38F84CB64C775B201D7D9F48E045,
	Sprite_get_pixelsPerUnit_m5A5984BC298062DF4CD2CB3E8534443FFCF31826,
	Sprite_get_associatedAlphaSplitTexture_mBD0221BDF9855712C681C4DDCAD4EFA6EE614FDB,
	Sprite_get_pivot_mDFC0A205317DB2F3B6C720B8A5BE1C27D01C1D44,
	Sprite_get_packed_m6B6B98A3891B350711499532C07F8D393AAB868E,
	Sprite_get_packingRotation_m3FD3489CC43013B2D5228AEB1ED8099E6C5B1D10,
	Sprite_get_vertices_m2584A7F59A8E8362F16E0DDA4CC25A1EDF8D1D31,
	Sprite_get_triangles_m5D2A3D916814891FF5DF236D8A2D72C89A66EFD4,
	Sprite_get_uv_mAD4DAE6EAEC23340B69D0991FA4D8E72C6CA62FF,
	Sprite_GetInnerUVs_Injected_mC34F24437EB70A80781267A638BD909D68A091EA,
	Sprite_GetOuterUVs_Injected_m27113B4C012B99AF083390AD49203D329C15544A,
	Sprite_GetPadding_Injected_m12B39280FA7B844ADFEAB0BA1355F654755A67E9,
	Sprite_get_bounds_Injected_m987E852C2009674973F2E254460636CF859F411A,
	Sprite_get_rect_Injected_mF254371640E3209C35452CD7CFFD61A06AD4EA97,
	Sprite_get_border_Injected_m9D59B69C43462D8DBB6186B858264F4E84C3E200,
	Sprite_get_pivot_Injected_m084D8C724F7754DDFB1D7DDF2A5E48DB10C585B9,
	APIUpdaterRuntimeHelpers_GetMovedFromAttributeDataForType_m572E9998D4C574A15D5D044699A572DEBC3FC1E3,
	APIUpdaterRuntimeHelpers_GetObsoleteTypeRedirection_m135466CB807CB14025653915037D56D246F0F46C,
	DataUtility_GetInnerUV_m0CEE9FB4108D7A880A4807F2F5BD5FA98C9917ED,
	DataUtility_GetOuterUV_m408EFF91CB39DE165F8D00D42603DF1C49C57CF2,
	DataUtility_GetPadding_mB8E2E01509BFABEEACC45EA11D6A0FCF05F809C6,
	DataUtility_GetMinSize_m927FDAD3190433E999165BD16CE81677EA9C0896,
	SpriteAtlasManager_RequestAtlas_mE6D9398ADA0A5CB1A76407B59511779F2DAA2593,
	SpriteAtlasManager_add_atlasRegistered_mA46A6A347F25B2E03DB4FD8044B93B4FD8ED50A5,
	SpriteAtlasManager_remove_atlasRegistered_m67E745D3503463E3DB9CC12C157ABB4F469ABE79,
	SpriteAtlasManager_PostRegisteredAtlas_mEBA789EAA074F9C6450ADF50722343BEF4509110,
	SpriteAtlasManager_Register_m44D4E9341918EA32BEC92A77E851FF9A3BC21505,
	SpriteAtlas_CanBindTo_mB4326EC04E7C2CC9D43AE04AEE9B91171F3BFA01,
	UnityEventTools_TidyAssemblyTypeName_m744218193DC58EB2F113300DDE351170683797F7,
	ArgumentCache_get_unityObjectArgument_mEA22BE8C25CDC789963C2DABF068E88147A66C69,
	ArgumentCache_get_unityObjectArgumentAssemblyTypeName_m85640B88D8DA790019005A4ADD948E036ED79694,
	ArgumentCache_get_intArgument_m7515338C0F3B5843E40CC48C303D2EFC02D9C19C,
	ArgumentCache_get_floatArgument_mDED33C174CAD9DFAD58F9D6DF482557C0FC20D38,
	ArgumentCache_get_stringArgument_m4CA65BC60FC1FDCE88779C009ED0E1DC4BED2D9A,
	ArgumentCache_get_boolArgument_mB7A56994202FCB50BA04A6DBED9BAC45871F700A,
	ArgumentCache_OnBeforeSerialize_mF01AF8DE34554D86AEC843FEB41D14F3172D481F,
	ArgumentCache_OnAfterDeserialize_mD1C2E914447C2F69B43850F15AB19B62AF49DE96,
	ArgumentCache__ctor_m8410B763CA027E30237E5954888A7F508800A331,
	BaseInvokableCall__ctor_mD64C67D6FFB881F98555408743D7BB5CA7217B39,
	BaseInvokableCall__ctor_m7633F06F55DFC3324C46A7C3DD6C55DC949FA0FE,
	NULL,
	NULL,
	BaseInvokableCall_AllowInvoke_m7BBC3A3F424104A84947708ECF8EEF74707F7661,
	NULL,
	InvokableCall_add_Delegate_m5AD40C6D21D67A44980DF3B99946C4A2F17D9A10,
	InvokableCall_remove_Delegate_mB8464CD88899199AAA70CD5EA4E02DCFB16045E1,
	InvokableCall__ctor_mF3F94B432C977EE2DE7834EC2936E90D271C0464,
	InvokableCall__ctor_m4FA1428E3A33219B2C8C5C571A705AC6B862FA70,
	InvokableCall_Invoke_m874703DD260A64342495E79986B31EDA8D06C1F4,
	InvokableCall_Invoke_m6F4828FD2B3E3BBB7AA6EECC2C37FB08538363F4,
	InvokableCall_Find_mC76E5065AEEFC89956540199A4CB92E953E4B32F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PersistentCall_get_target_mA12C3C9A21F6F5335761CB0EB3C7C965D8C393AE,
	PersistentCall_get_targetAssemblyTypeName_m303DE56BDE5CD469D0210E1EA73F54B4C88228BE,
	PersistentCall_get_methodName_mFD7F88289C9EF5DE6D7EFD3FEF1A5C640CBAF088,
	PersistentCall_get_mode_m3FFA4D4FC3DA0C38106323CD33ABBFA53ED01928,
	PersistentCall_get_arguments_mA3B29A1F4E7328523674ADC6FC0C7332BA053410,
	PersistentCall_IsValid_mD63347854BC781710D4CC9B5FC3C3996E84A325D,
	PersistentCall_GetRuntimeCall_m0DDE14D286D9995CCE65D2DFF27D57E4D476F072,
	PersistentCall_GetObjectCall_mF7C9F7A24733E6B440637285FF76AF47AFDD021D,
	PersistentCall_OnBeforeSerialize_mD36FE363489E1A7C338AC7392F0DA13094825872,
	PersistentCall_OnAfterDeserialize_m52A9B0536D334B3C89A0A9E4D923AA212201F485,
	PersistentCall__ctor_m6EE5F241C45D97046ECAFCF45FB0DE96E7827142,
	PersistentCallGroup__ctor_m1B17318026E3D419B2C194F66882E3BED6C4200A,
	PersistentCallGroup_Initialize_m937649041F14D0D20F959B07BA099246EC32BCCB,
	InvokableCallList_AddPersistentInvokableCall_mFB82EE201D90D84E0E25934EA879067BD666C0C1,
	InvokableCallList_AddListener_m279B8BAED30DA27C305ADDF241F05CD2BC59625A,
	InvokableCallList_RemoveListener_m5C78FE9ECE5990F29636216E879139D5863F36C8,
	InvokableCallList_ClearPersistent_m9A776CBBC13667875F1765B32B469BC12AFD4192,
	InvokableCallList_PrepareInvoke_m0CF5EBCDF4913AFC13CBE09F6CFB687D0F771301,
	InvokableCallList__ctor_mE70F25915B775E7258A12670B76C7F7B3C36BF1A,
	UnityEventBase__ctor_mB1F958EAC1A7C4B31253F2E1FED173A628725DEC,
	UnityEventBase_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mC47C72ED57A467E299925019E7DB9645D0F631F9,
	UnityEventBase_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m835BA25C9F342C93FB9DF774C0894A82C4F049CB,
	NULL,
	NULL,
	UnityEventBase_FindMethod_m0B00339CC16B63CF1C50714D018A87948FC0B23B,
	UnityEventBase_FindMethod_mE417FEA28EC49921FA28EBDAB1214B0E6EC7E91B,
	UnityEventBase_DirtyPersistentCalls_m356D77B4849FC63501507E4D3F1054BD86D6B1CF,
	UnityEventBase_RebuildPersistentCallsIfNeeded_m981B8A6658A88F620345D2C7F4ADCD0D788B0266,
	UnityEventBase_AddCall_mA78C058ED530789A28F42347B653190FEC84DBBC,
	UnityEventBase_RemoveListener_mFF8F8FAD5F18BA872C3CE005DC134B6828E1AD3B,
	UnityEventBase_PrepareInvoke_m4D04FA5D7025C093047DCD3DFEEFB9DF48764FC2,
	UnityEventBase_ToString_mE86F29D699C7537CACCAF3945F797EE659CE6522,
	UnityEventBase_GetValidMethodInfo_mCFA9547C470F2F90619A1514108BCE0F49F9B0CD,
	UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131,
	UnityAction_Invoke_m5CB9EE17CCDF64D00DE5D96DF3553CDB20D66F70,
	UnityEvent__ctor_m03D3E5121B9A6100351984D0CE3050B909CD3235,
	UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302,
	UnityEvent_FindMethod_Impl_m62E3D2795BACFF1BA2ED6A431ABD5FB2C7D3D681,
	UnityEvent_GetDelegate_m6665C6282D3668BC57F2702FD0C3B108F4CFD226,
	UnityEvent_GetDelegate_mBD5D37CFB826CB3329477A509A62BF7CE26A9EF8,
	UnityEvent_Invoke_mFBF80D59B03C30C5FE6A06F897D954ACADE061D2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FormerlySerializedAsAttribute__ctor_mD7361D52007EF623D139A726F892198E089F3C9D,
	PreserveAttribute__ctor_mF9E65066D9317F56C6F4AE274A1E2D55D6A62F96,
	MovedFromAttributeData_Set_m155005FB8BCE0569C40B02D75FFADB3FBDB7EEDD,
	MovedFromAttribute__ctor_m1B944ED92ED635A19DD4E82765BC0200C0BF3E4D,
	MovedFromAttribute__ctor_mFDCCBD975A9BCC410529DB2B7C01659ACF8005CC,
	Scene_get_handle_mD508BE60333C6168610E12CECAB12E9B11C25E53,
	Scene_GetHashCode_m74ACBFB8C656D5620A9A1E62D04E55ACBF63ADC9,
	Scene_Equals_mB973481492F291BF8EAF1AD66B9F7FA3ACF3928D,
	SceneManagerAPI_get_ActiveAPI_m2040A088481EEBB6D6B9B1B2A4090CBA1129FFA3,
	SceneManagerAPI_get_overrideAPI_m636C24986664B542327F2528A1128B7738DEF6E6,
	SceneManagerAPI__ctor_m697F6B718DCE9B5E6CA4D58BBFB0CA275E003307,
	SceneManagerAPI_LoadFirstScene_mA4217ED8AD09C74E82DA9C10A0558AF830228E6F,
	SceneManagerAPI__cctor_mEF0EF066433E952992273F1624DE367B9FCD52A6,
	SceneManager_LoadFirstScene_Internal_mBD991BBA665DF9A290C6525A09B6D0D275CC5259,
	SceneManager_Internal_SceneLoaded_m929615D240C989E0EA73E3141C8AAAA73BCB51D4,
	SceneManager_Internal_SceneUnloaded_m8F6929B480F6398033CF78E654F01D9FDDBA0321,
	SceneManager_Internal_ActiveSceneChanged_mD1207F7C46CFBA85FA58A4ACCF7C326A8AEE55DC,
	SceneManager__cctor_m130A0393C55C3A0553E24DC895939CC7184D91F9,
	PlayerLoopSystem_ToString_m259B8533D2C64C15D381B16F32C710A0018684A0,
	UpdateFunction__ctor_m0D1D766F22D02176396DA6745A60EA046EE8227B,
	UpdateFunction_Invoke_m9BCEE4E5BEE924EB804DA64314B78D0E831C179B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PoolManager_Register_m0AF905E5CD87059815F22468F3BA73706DCA4958,
	PoolManager__cctor_m97C7011FB89B21685C0ABF9F8C0FE8C1BC7AA835,
	MessageEventArgs__ctor_m66E6239501EEE8FCE1DA218E8189F46FB0669A45,
	PlayerConnection_get_instance_mF08ABE514910AD8B105D42561CF97CBF135ABD3B,
	PlayerConnection_get_isConnected_mFDFB30D1AD4A7BFC66207092270E86B8D467209D,
	PlayerConnection_CreateInstance_m7903C5C28DAA3A2FF48EF6593E03A5E5A6DF7777,
	PlayerConnection_OnEnable_m3BE74152671ABA48E7AD707773B93F57BDA53002,
	PlayerConnection_GetConnectionNativeApi_mDF3BB4FF695E3ED19E525951FEED123C52FB4781,
	PlayerConnection_Register_m8760D786583FD5793A2FCE5EEB8DDA17C649CF86,
	PlayerConnection_Unregister_m46303AD2C27A3A71B6569376E5C461AF1D0DFCE1,
	PlayerConnection_RegisterConnection_mF9F575D16B80B1299D0E058E5F4A8DBAA907784F,
	PlayerConnection_RegisterDisconnection_m2D2554B55821F11EB89442D54125A5C2984E0EC2,
	PlayerConnection_UnregisterConnection_m0213376CEDCAAF5AF7C49226660C2F2182F2E1FD,
	PlayerConnection_UnregisterDisconnection_mB2E0A093BA8F75B183A2774CA49FD5762B4E8A6E,
	PlayerConnection_Send_mBFE41D75C28DF0885455CE809D40C489818F91B7,
	PlayerConnection_TrySend_m6D589698ADF1E9A0E4CC208651F6CC86AD572423,
	PlayerConnection_BlockUntilRecvMsg_mBDA2944F58B7984DDFA6A998CA558FC43B64896F,
	PlayerConnection_DisconnectAll_mDC4057C27894D4216DF83A9EF9107C86AA3C5D03,
	PlayerConnection_MessageCallbackInternal_mABE4761961414EE58E31F8D06F9870AFD1BBD1BC,
	PlayerConnection_ConnectedCallbackInternal_m64117185C66F8947BFB1ED0E41C6D35A9AC35177,
	PlayerConnection_DisconnectedCallback_mC66E0C59A86620D43E7C1418F8EAA006B63690A2,
	PlayerConnection__ctor_m03E1218C1A0210FFDC4E2B2E2F79B7416E9D8F11,
	U3CU3Ec__DisplayClass12_0__ctor_m07EBBD06394F8590D2B8DFD50B6D1EE94ABB0CBB,
	U3CU3Ec__DisplayClass12_0_U3CRegisterU3Eb__0_mAC55D4E3203F06ED7F9872599F1CF02DF2430859,
	U3CU3Ec__DisplayClass13_0__ctor_m6AA357ABA48CA75462E938084C7953E820CB18AA,
	U3CU3Ec__DisplayClass13_0_U3CUnregisterU3Eb__0_m929D945DEF8430CA1E953420F9C28F43198F9FF5,
	U3CU3Ec__DisplayClass20_0__ctor_m75B603A2B5F9AB50F7A52CCD844186FE1A6981AA,
	U3CU3Ec__DisplayClass20_0_U3CBlockUntilRecvMsgU3Eb__0_mE65B4F1DEAC961650E79C59F69302B4AC63CE3F1,
	PlayerEditorConnectionEvents_InvokeMessageIdSubscribers_m67F47A7AA6EC55A672AD1020F329C4332BEEFFAE,
	PlayerEditorConnectionEvents_AddAndCreate_mB3B1355A8FDC43E5974CC3DE5BB17CB0C4328308,
	PlayerEditorConnectionEvents_UnregisterManagedCallback_m61A82E77AA85A903BE0E534FEE601CABF9DE7809,
	PlayerEditorConnectionEvents__ctor_mA1F7A1F05DA196CDC66A85361C5589F504C557B3,
	MessageEvent__ctor_mD99E3A2C521C8B2B544F5DF2376258E9613D08EE,
	ConnectionChangeEvent__ctor_mEEB8C3ABC40815A0BBC2EEEEAD77ACAE1A6A0554,
	MessageTypeSubscribers_get_MessageTypeId_m782C2CCF400D0C5A0FB343FBE1B66C9C09971449,
	MessageTypeSubscribers_set_MessageTypeId_m4FA721221C0D7812EA1AFB6C406C0620FF06761B,
	MessageTypeSubscribers__ctor_mFC17A12701D7DB8739ABF17DB1D5EC41531CFED6,
	U3CU3Ec__DisplayClass6_0__ctor_m2EE8CF87D7C3816A57E4D6238DEA0660DB2C7303,
	U3CU3Ec__DisplayClass6_0_U3CInvokeMessageIdSubscribersU3Eb__0_mAB457C850FBAB1CF66C7EFAAE9F57EAD0FED6CFF,
	U3CU3Ec__DisplayClass7_0__ctor_mC3BBDE08CDDEF0DA0811796906BA4A74C287CC2E,
	U3CU3Ec__DisplayClass7_0_U3CAddAndCreateU3Eb__0_mD687F2BC0FE80692E5F88480DC5B861FE122F82E,
	U3CU3Ec__DisplayClass8_0__ctor_mEFD5A284D56758F6EA5F97C12FDE739E7E1C2C93,
	U3CU3Ec__DisplayClass8_0_U3CUnregisterManagedCallbackU3Eb__0_m3906587CC8F4CF5B752383CE6EA540B27E67055D,
	DefaultValueAttribute__ctor_mC1104F2F0A2CD67CE308CD7E5C1CCE74482C1BB4,
	DefaultValueAttribute_get_Value_m8FC7A517D291CDDB7B5D17E811F5CB11FCF59D24,
	DefaultValueAttribute_Equals_m6E5B33344C4FD2FC4775A657481B63F82F18B925,
	DefaultValueAttribute_GetHashCode_m6F99553C7E76E711DCA6368417F66898F5AF6359,
	ExcludeFromDocsAttribute__ctor_mF280A16634D72D09D550C2BA2294D9234D50C771,
	VertexAttributeDescriptor_get_attribute_m2F7C7084857741A39E9FF0D4BABF98F9C7018AAA,
	VertexAttributeDescriptor_set_attribute_m526ED099EEA12DCB2FCB1BC88445FBF921E850A2,
	VertexAttributeDescriptor_get_format_mF8EF98F3E047BC26D177C3A008BCDAF979E52C79,
	VertexAttributeDescriptor_set_format_mCA82263EBD7802621D9ECE42D0F44D4AAECD46C5,
	VertexAttributeDescriptor_get_dimension_mAB440DDFA08BF61D717547EC8B9C43C3DFC56FB8,
	VertexAttributeDescriptor_set_dimension_mEF4325AE27221C732B214354A2DEB044ACFC69EE,
	VertexAttributeDescriptor_get_stream_mAA4D41E2B4B9024161B176700100ADB27855E4FF,
	VertexAttributeDescriptor_set_stream_m9A3991AA0365A1DCEDF80A61990F7B48913A1C0A,
	VertexAttributeDescriptor__ctor_m713B31395FB13FDEB2665F5C4C31572D5875A43A,
	VertexAttributeDescriptor_ToString_m45F7E7D6715E000C617173246F2C9BE23DF3FC8F,
	VertexAttributeDescriptor_GetHashCode_mCB8C5F6AD422FE6A410C5406843FCE4B0ED5F2F8,
	VertexAttributeDescriptor_Equals_m6FBFEE42E1BAAAC2D38434EDF0906C5B76D56BE5,
	VertexAttributeDescriptor_Equals_mF328DE139864C201987238048AC79F8925CA435D,
	GraphicsSettings_get_lightsUseLinearIntensity_m74D1A18837CB7E9D3A9BF20D44212F94DD1F67B9,
	OnDemandRendering_get_renderFrameInterval_mC9E8FC6225DCCB074FD3D7BD128734DA321A2CCD,
	OnDemandRendering_GetRenderFrameInterval_m2E7629CD00648D6DEC5D78E631510ACF1350A931,
	OnDemandRendering__cctor_mED0B1BA868A046D26D214B1DD1C0BAAD6147E9BB,
	BatchID_GetHashCode_m3F4DFD3D722865BBC47F74C5E452E985B6BBBA3A,
	BatchID_Equals_m953C8EE9ABF083CFAA3D7210F1085C9FD7EED71D,
	BatchID_Equals_m4207BA1B44B5BF1DA2847571B6D9A981F5AE3CFF,
	BatchID__cctor_m64258A0EFBDACA106FC41F54A1CF9936A884A4B7,
	BatchMaterialID_GetHashCode_mE28980612614E66848ADABB7825CA73FFA39DD09,
	BatchMaterialID_Equals_m776A152CA1957E92FCD24B4E2EDAF8A6D78E953E,
	BatchMaterialID_Equals_m073A3DECD4CDE73F0CC314024B89399982E5BA83,
	BatchMaterialID__cctor_mCAFD39D3BA87D50321EA76B09E7CD4B8028E9A35,
	BatchMeshID_GetHashCode_m62EDD33030F375FAAD44A23FAD7901A2BE1D08A9,
	BatchMeshID_Equals_mA501F8E992E7B32BD8985DD68D5D0B360A104A42,
	BatchMeshID_Equals_m985986D473BB12E9F43C59B41846660884B15C06,
	BatchMeshID__cctor_mC7DF8454B495B27D09F84E06E8D42B5F4224043A,
	BatchPackedCullingViewID_GetHashCode_m588865495A1A1F2AC79A62D76B2DDC91D743F4A5,
	BatchPackedCullingViewID_Equals_mD7B8D7EFB1678D80FC11B9FA1171988669CD0C7E,
	BatchPackedCullingViewID_Equals_m2E5BC66A5B8D6737BABE5D6A0019F901192D2A9F,
	BatchCullingContext__ctor_mB26CB3EE7FD392F94B9816FCA04D76B8AAB6FDF3,
	BatchRendererGroup_InvokeOnPerformCulling_mCBE957A7C05B4023359DE8EADB3B17D93956CCC3,
	OnPerformCulling__ctor_mFB91FB8D6D713AC39AFB921AF1C23A50EC80529B,
	OnPerformCulling_Invoke_m9DA6498101531B96227E97025316476D1EB79DF2,
	LODParameters_Equals_mAD45601A9E881620B23A7922D8AA5AFBC91E0B9F,
	LODParameters_Equals_mF956A067D82497A00AC8234322CD848E6FB7BADE,
	LODParameters_GetHashCode_mECFCEAAAC935759A99C30C69BBC99A1148F46F40,
	NULL,
	NULL,
	RenderPipeline_Render_m41E15C2221365D92636651792FBDAD1A04E8AF1A,
	RenderPipeline_InternalRender_mCB77395158F4572F348D8227BA9127ABF1C9C5BE,
	NULL,
	RenderPipeline_get_disposed_mF0D4B88DF44249E0FC2371697B913DD5A81B3850,
	RenderPipeline_set_disposed_mB7EC4BD04C80015CBCC0B92A65A6DE615F2828A8,
	RenderPipeline_Dispose_mDF8A62A6B7D3A00128C9341921C036D015C41179,
	RenderPipeline_Dispose_m581E2B33EF0CCAF2C3E7BD71AE32B4974D259286,
	RenderPipelineAsset_InternalCreatePipeline_m662E6A3D3B14C19D5318E172A4AF81FEF71C6252,
	RenderPipelineAsset_get_renderingLayerMaskNames_mC0978F38B2EADD1462593AC6D8395E612CB1329D,
	RenderPipelineAsset_get_prefixedRenderingLayerMaskNames_m89254E02D74CC6FC9320104B8A1748534FE55F6B,
	RenderPipelineAsset_get_defaultMaterial_mCF112570E7B47208A0B700BA97B33CAE6713D323,
	RenderPipelineAsset_get_autodeskInteractiveShader_m261A0788B4F73AFC5A50953878552BC3BFF8B674,
	RenderPipelineAsset_get_autodeskInteractiveTransparentShader_m6E2A2439FCB378F64DC46CB6DB517A1661A306F8,
	RenderPipelineAsset_get_autodeskInteractiveMaskedShader_m42DB9A70B290E217B91616E9274C9B7E58D54362,
	RenderPipelineAsset_get_terrainDetailLitShader_m74F2FA112CD18493E7003F6F711AA09A8C930136,
	RenderPipelineAsset_get_terrainDetailGrassShader_mBE126045BF9048B59CE56D1BD9C69C3DEB4CDD96,
	RenderPipelineAsset_get_terrainDetailGrassBillboardShader_mE233539926B92ADB7A057C34CBCD7E51823C75A2,
	RenderPipelineAsset_get_defaultParticleMaterial_mC68CA6787815E00CF501F268F34DA0B3C83B3013,
	RenderPipelineAsset_get_defaultLineMaterial_mB4DD122B043417F3810FEAAA74E79B369A26B484,
	RenderPipelineAsset_get_defaultTerrainMaterial_m129D75CFE9689112452AA911C24BEE4939ADB520,
	RenderPipelineAsset_get_defaultUIMaterial_mBB9F6694EEEB97AC6140C094A48743660A0DAF04,
	RenderPipelineAsset_get_defaultUIOverdrawMaterial_m19832E76629C0AFC18F0D61C5C1CC47533200038,
	RenderPipelineAsset_get_defaultUIETC1SupportedMaterial_m2D1DC4B89F59283C8AE3101F3DC05D88C10D4C10,
	RenderPipelineAsset_get_default2DMaterial_m2418C7E1A0974B2A06CF3BE81590C7B0DF1DB113,
	RenderPipelineAsset_get_default2DMaskMaterial_mEE67105F80768BDFD1C93E8709FEBF2D4561E14E,
	RenderPipelineAsset_get_defaultShader_m633AD6A97B2D1D436E42920D766B1283C1FED858,
	RenderPipelineAsset_get_defaultSpeedTree7Shader_mCEF3795C9E36F0A9D9A3CE29824790B172637252,
	RenderPipelineAsset_get_defaultSpeedTree8Shader_mAEB3D02FC0A0016BAC5C22BC1967827403E5C17A,
	RenderPipelineAsset_get_renderPipelineShaderTag_m1F68B8E1EC2EB1BA57542EDF80505125B2BE4D34,
	NULL,
	RenderPipelineAsset_OnValidate_mD160C7BDEA559BAF3DDA48B4819307E07B377F52,
	RenderPipelineAsset_OnDisable_mE99CEED707BDC901AD37DC976FA3A3A313E7E00C,
	RenderPipelineAsset__ctor_mC45BECAED54BEDC4555AF010323EABF49BA7B78A,
	RenderPipelineManager_get_currentPipeline_m270A15A23593BAD8D6A20CE2E328210FF9AA692D,
	RenderPipelineManager_set_currentPipeline_m1C8CDEC2FD862823D0C4DC007CB57DBAFB1CDB3F,
	RenderPipelineManager_OnActiveRenderPipelineTypeChanged_m7BCA10285B43964387C7DA20EFC7C0C6A47C4E24,
	RenderPipelineManager_OnActiveRenderPipelineAssetChanged_m377430D7BE5CE854DA6034968CF1095AAB15ED1C,
	RenderPipelineManager_HandleRenderPipelineChange_m8B7BA0E0ADC1AD84C4A2E119E08C225EF519482E,
	RenderPipelineManager_CleanupRenderPipeline_m1104AEE59AFE2EC1F1AD15DCF862802230466F99,
	RenderPipelineManager_GetCurrentPipelineAssetType_m78BBEBD08F2BCB77FA23BDCD54C10A4102115EF5,
	RenderPipelineManager_DoRenderLoop_Internal_mB646C8738F4A9859101F3BE94809E2E10BBDB1FB,
	RenderPipelineManager_PrepareRenderPipeline_m342C65E962B20B547DB2BAF537EA870457736108,
	RenderPipelineManager_IsPipelineRequireCreation_m97B62DE27F930ADB3B8DE6831DDF1185837E826F,
	RenderPipelineManager__cctor_m8A9308C1CD1B4F533F5D9855ECB44D6B71620807,
	ScriptableRenderContext_GetCameras_Internal_m852016B3544E3ED5FEFB9695EC175622A5B6A8C8,
	ScriptableRenderContext__ctor_m10159F14BB69F555C375E13BB77A1898FDB42FA5,
	ScriptableRenderContext_GetCameras_m9B2329F79132EE49B719560AD739FD3601C44189,
	ScriptableRenderContext_Equals_mBFDA5815F2B6ABA9B16503DA906B8BA42078718D,
	ScriptableRenderContext_Equals_m99E5A233945DFC3B9A786F2413ECE68E5019AB88,
	ScriptableRenderContext_GetHashCode_mA1EE09239F1ACFC29A2ADB027D5E76E690510207,
	ScriptableRenderContext__cctor_m0C385A2E228FAAEBACE8E912E26F05147525E8B1,
	ScriptableRenderContext_GetCameras_Internal_Injected_mFA0C805805CE2CC5BB6AD8DB0075733BF194FEE7,
	ShaderTagId__ctor_m4191968F1D2CE19F9092253EC10F83734A9CFF5B,
	ShaderTagId_Equals_m02826F7AFC63AA3AE5DB14F7A891F8F173FD9A33,
	ShaderTagId_Equals_m932EFCC38C276EEB2784BBC866330F4C595F52E0,
	ShaderTagId_GetHashCode_mF5E3A1F96CBDFDCEFABE1B56125EBBA6E3B9EFEF,
	StencilState_set_enabled_m6DC861C699D1044E896E833D2DAE69B82F796564,
	StencilState_set_readMask_m1BA8F9033413889D4E77DA343DC0029566A9BB9B,
	StencilState_set_writeMask_m94471C671E03D42F36DA61436B1068B362375D65,
	StencilState_set_compareFunctionFront_m1388C37901DAB6AF9D23C0F01946DCCE19BC9BFC,
	StencilState_set_passOperationFront_m1F15CC29366DAEAA6CCE1DB0622C70D6ECC5A3EB,
	StencilState_set_failOperationFront_mADCECAE5D2E75ABAE51650F1F314E661D09C2CD6,
	StencilState_set_zFailOperationFront_mC7D8F0A08B9AEC4203BD6B352CB795E8011EFBB6,
	StencilState_set_compareFunctionBack_m8AF73F4E8FC95A46D33E3192C50702D2AA15D61D,
	StencilState_set_passOperationBack_m4B1395FE21F5B5C809DC6F31D5824A90E05ED220,
	StencilState_set_failOperationBack_mD279271DD1F99EE5B8BC19F5AE60988E6C6F0E4A,
	StencilState_set_zFailOperationBack_mC092ABD8A5EA87245640A10E54C6A1990C4F6864,
	StencilState_Equals_m9FFB8A41D8838FD128875CB2D4DAA760C6DF1051,
	StencilState_Equals_mEA45A5D2BF2223B15EE0FB8BCEDBA9CB534ADF4B,
	StencilState_GetHashCode_mB4A02DEE780377C853D16FFF49CCB9D9F4F711A5,
	SupportedRenderingFeatures_get_active_m09012C98E24D5B2E8C09F6657FC5CD19B2AF3054,
	SupportedRenderingFeatures_set_active_mB2A1A6137C816592E6526CD3DA7405260EAEA8AE,
	SupportedRenderingFeatures_get_defaultMixedLightingModes_m7F9FDF9012EC41E36466613C8A9103D74666CBC4,
	SupportedRenderingFeatures_get_mixedLightingModes_m929C0CE80A4990993EBCAB8B46C1E273A0829137,
	SupportedRenderingFeatures_get_lightmapBakeTypes_m1311A5AD5BE1A6BA3251238C893D7D340358C156,
	SupportedRenderingFeatures_get_lightmapsModes_m925D670110EF7109A26BE1B228066E1201FAAE38,
	SupportedRenderingFeatures_get_enlightenLightmapper_mF7C756BBD4E605DD047BD502DFF8569C4CEE8F27,
	SupportedRenderingFeatures_get_enlighten_m6F973FEB7CCF0BB1B7A2F25317EADC5F6FD95ED6,
	SupportedRenderingFeatures_get_rendersUIOverlay_m657FFFC5B360F7BCE9964EF50E7449779224AEFC,
	SupportedRenderingFeatures_get_autoAmbientProbeBaking_m42E98E922511B1CF790FC414C9A85D70DFACA2C8,
	SupportedRenderingFeatures_get_autoDefaultReflectionProbeBaking_mFDB934E6645FA5CA95E1F0BEF4A12345A1025207,
	SupportedRenderingFeatures_get_overridesLightProbeSystem_m62C56592C44FDD2FC89EAA4ADE71435CDE65EB64,
	SupportedRenderingFeatures_FallbackMixedLightingModeByRef_mEC3BA55F7A145AFCD9762F1FD752CA4D587D0F88,
	SupportedRenderingFeatures_IsMixedLightingModeSupported_m11E0ADD90300D5396090ED9B2EFD9924524D50AF,
	SupportedRenderingFeatures_IsMixedLightingModeSupportedByRef_mE48C26F367ABEC734BDB7A8A66FEB8796A401AAF,
	SupportedRenderingFeatures_IsLightmapBakeTypeSupported_mE8EE2ACBE267FDAEFA6229CD67298C9D3A16C691,
	SupportedRenderingFeatures_IsLightmapBakeTypeSupportedByRef_mABE7E2BC06CA25519079DF4EEC89667EB51B8460,
	SupportedRenderingFeatures_IsLightmapsModeSupportedByRef_m99BF1104DDA5C0EB37E529F76670E7F10E6319E8,
	SupportedRenderingFeatures_IsLightmapperSupportedByRef_m891045E2F4FF9AC667E6FC75F6119D5AB546FBDB,
	SupportedRenderingFeatures_IsUIOverlayRenderedBySRP_m15CF763E502A362DC677DEBBEC12A9B45CEEB458,
	SupportedRenderingFeatures_IsAutoAmbientProbeBakingSupported_mBC9E03F60617335C1E8D4DE88FD57F591CDA6C50,
	SupportedRenderingFeatures_IsAutoDefaultReflectionProbeBakingSupported_mD59A4D48FC5A6095035739F187C9BCF4D0DC67C5,
	SupportedRenderingFeatures_OverridesLightProbeSystem_mB8D7CE26D5DA36E58988A09F399227549F3B7B08,
	SupportedRenderingFeatures_FallbackLightmapperByRef_mBA3E826098F185845769A99830E50C43294304FF,
	SupportedRenderingFeatures__ctor_mF7728980F81142B7BD45FBB25AB001B17A4BF0ED,
	SupportedRenderingFeatures__cctor_mD97BB2123C1AC4DAEF2B5D8D1DC8219C31968416,
	SortingGroup_get_invalidSortingGroupID_mEA453186185B225FA2410988959180BAEC604310,
	SortingGroup_GetSortingGroupByIndex_mC4CFB06D8C4B0EA27FC4BDC60AA05F4E0B28426B,
	SortingGroup_get_sortingLayerID_m37C70CEA78DA6E28F405C67EDE95D5C2191F0055,
	SortingGroup_get_sortingOrder_mC80606E6BB9A6D5E3AB61A0451C48203A0C1AB6A,
	SortingGroup__ctor_m0F2104D49BFE2551466B75F1A75E20EFBD0E350B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Playable_get_Null_m0D0BB32BC23983C490A363AD6AAC47D8B72864F2,
	Playable__ctor_mD2EB35E024816AEED68795D0124EAB30E05BF6C4,
	Playable_GetHandle_m39356D23E849DC5428B262092657662C064E04F8,
	Playable_Equals_mD72D3DB892B8867A0E7BAC032A16C08616EEFF86,
	Playable__cctor_m079282CFC9FB3C8D02C0F6F9FF45C167DF583459,
	NULL,
	PlayableAsset_get_duration_m4668A767DDB780565E6506E63B4797B820405CFE,
	PlayableAsset_get_outputs_m5760B1B5EE08B0327FA7D90AE92C94227B1C993C,
	PlayableAsset_Internal_CreatePlayable_m8D90055AAB62B51D1F73B58F30715C4512100714,
	PlayableAsset_Internal_GetPlayableAssetDuration_mD80D4032B1E5DECC8710CB63A49E815F21EFDBDA,
	PlayableAsset__ctor_m36B842356F02DF323B356BAAF6E3DC59BA9E1AB8,
	PlayableBehaviour__ctor_mA6202DCD846F0DDFC5963764A404EE8AFABEA23A,
	PlayableBehaviour_OnGraphStart_mB5944807796239EFB530022539674C4E4D185D65,
	PlayableBehaviour_OnGraphStop_mF80DFC8A3C2D2CA9299011D9E871ED6A8A9586CA,
	PlayableBehaviour_OnPlayableCreate_m22B0F0051A677A523C5702AABC6B1C9D358E90B8,
	PlayableBehaviour_OnPlayableDestroy_m3DB0AF7BD9689DA1BCCBBFD19BDD544143027C3C,
	PlayableBehaviour_OnBehaviourPlay_m05F6FCCBC6E8FB4BA0BE2690045AF28BF95C6FE2,
	PlayableBehaviour_OnBehaviourPause_m431A7BD2EE99C1862563FEA37E20C365B753930B,
	PlayableBehaviour_PrepareFrame_m33FED1E870D350D8276712A2CD75118FEFAA86BD,
	PlayableBehaviour_ProcessFrame_mB80DDB2AB5D7EC0D3B9A466D37BE8556F6BBD2A0,
	PlayableBehaviour_Clone_m6A5B052F4ECA2ADED5937A4843777F52CCD33EE8,
	PlayableBinding__cctor_m3055AFB9F43633F1353C40FC9E1B2A4492732AF1,
	CreateOutputMethod__ctor_m5A339017CD8ECB0140EB936FD2A5B589B20166B4,
	CreateOutputMethod_Invoke_mEC7DC5D9A9325BFFB17C248AE9738637704B89CC,
	NULL,
	PlayableHandle_get_Null_mF44FE0A71C67054D272F454C91F7E08CBF14A975,
	PlayableHandle_op_Equality_m0E6C48A28F75A870AC22ADE3BD42F7F70A43C99C,
	PlayableHandle_Equals_m60AD76B7D38CA989AE84501B2E9F9ED5CB5F9670,
	PlayableHandle_Equals_m81BA0E127133DFF3E45DA61D185FDF48E16BCF45,
	PlayableHandle_GetHashCode_m10FB32ECDC0B9D7BDAEA9E3B76BDDF4614F4EF4F,
	PlayableHandle_CompareVersion_m228CA864DC2BCAA0E03B4C74EC9F2A7B529526D9,
	PlayableHandle_IsValid_m07631D12846BAAF2CC302E69A28A44BFE9EB5098,
	PlayableHandle_GetPlayableType_mD9750F1B85DF086F52641D6AB85789601486B686,
	PlayableHandle__cctor_mE31857278AA27F9CF449BD99AC79EC5E295A5278,
	PlayableHandle_IsValid_Injected_m9D662778C1A39FD8AEC18AEF053AE5C3DBD7B4B9,
	PlayableHandle_GetPlayableType_Injected_mD71007C85AACC8A18E2A5296B88FD8EA47349A9D,
	PlayableOutput__ctor_m55FBB20EC479F67641835EA48D84A1AB3DF39747,
	PlayableOutput_GetHandle_m12FF2889D15884CBEB92A6CB376827EBA5A607BF,
	PlayableOutput_Equals_m4CC730818751114DC5643600B5FE20243F4B7121,
	PlayableOutput__cctor_m02CBFEB6C9DB324655B9D354B32C268EED13749A,
	PlayableOutputHandle_get_Null_m656E8D2549FA031DA8A2EA5B39CE3B33D75B69F8,
	PlayableOutputHandle_GetHashCode_mC2FAF756D71026E3AF4492157EDAE7186429B079,
	PlayableOutputHandle_op_Equality_m116A314100562913DD28474B8D7DA5FBFCA9CD3C,
	PlayableOutputHandle_Equals_mB9106CB9333E0BF4C893E43AD7A23B64471CC21A,
	PlayableOutputHandle_Equals_mF5C23882B2A007186F00EB3D23E9BD6664E8DAE6,
	PlayableOutputHandle_CompareVersion_mAB102578900E20BB3B4273F94D1A6AFDB6E20FFD,
	PlayableOutputHandle__cctor_mBA610D820061BDA36802735EEC57A83B0985CFC2,
	LinearColor_get_red_m376617B8E3156420835055189BB28D953FE46A2A,
	LinearColor_set_red_m0ACFCEDDD205A6F235BE95936816E92898B01B52,
	LinearColor_get_green_mCCE90A662234EE3605368F3AEC14E51572665AE5,
	LinearColor_set_green_mBD9C7EA6415DC54B3F6B643C3CD02B71565F0694,
	LinearColor_get_blue_mAFAEA5D5590DD14CFC48BC18DF4BFEBBDCB0A99A,
	LinearColor_set_blue_m3FEEAF946772BB177733B67D9DA4B72D84874375,
	LinearColor_Convert_m0E220E18AC54A8040BAD7FFEB0D81538639F9BBA,
	LinearColor_Black_mF5AEFA40487500C1683D14FFA58554BF4D7B1A42,
	LightDataGI_Init_m112DEBB76EC57AC52E6384C97A3E8B2EAA867207,
	LightDataGI_Init_mACE06E00CC639CA89F3847E9DB55FD0F00812A7A,
	LightDataGI_Init_m0A999D118CDCBDA99B9E24231ED057D943C9C67B,
	LightDataGI_Init_mDC887CA8191C6CADE1DB585D7FEB46B080B25038,
	LightDataGI_Init_mB2D1C73EDFEA6815E39A0FE3ED2F7BF9A7117632,
	LightDataGI_InitNoBake_mBDF2EFB22D4BEE63B6F25F4EE9F1522D2866ED43,
	LightmapperUtils_Extract_m936FF4E20F593777EABF072404B37D0C1EB3AF5D,
	LightmapperUtils_ExtractIndirect_m5776341FC44CD3BBB634828E668732C2A490BB78,
	LightmapperUtils_ExtractInnerCone_m8B2B838A7D49A49D64813232503D5C3CA8957C5E,
	LightmapperUtils_ExtractColorTemperature_mEA79654385184193BC807A191696BE14B04ABEAA,
	LightmapperUtils_ApplyColorTemperature_m5286438BDED2F10292887505A26B1E33C714C325,
	LightmapperUtils_Extract_m44511C1C63663F51CD77ABF24CC4B34B9A826F0F,
	LightmapperUtils_Extract_m47570BBE32168BBEA4C823D83C8A94A4CBF03AE2,
	LightmapperUtils_Extract_m9F0C60CB137D268694B8CB324C73E799E1CE73F9,
	LightmapperUtils_Extract_m3B3FFE050376D624857D5D67413BD532518949F1,
	LightmapperUtils_Extract_mA319A386DA025BF5F0B7D9C398ACD3BE3AF65ABB,
	LightmapperUtils_Extract_mF6521637E4DD97C8BBD71696B5A61C7B7B8C83D4,
	Lightmapping_SetDelegate_m8BEF0FE5035180FF94119860CD15BBE2BE90129D,
	Lightmapping_GetDelegate_m073E4FFA73169C20833F77984024BD328003258A,
	Lightmapping_ResetDelegate_m8D4AAF4F08C8697953B3CB110DD4E6CD130371D9,
	Lightmapping_RequestLights_m1967533AFFB328B3386E7E0D1EC414105E509B80,
	Lightmapping__cctor_m6AEDE40A651280EC1A7944E9CFD161AFB78802B3,
	RequestLightsDelegate__ctor_mFFCE8681C67A169A04BEA2201C393E1FC84CAB7D,
	RequestLightsDelegate_Invoke_m01792B793691E6471596FF9B30E4D6F8EA18227E,
	U3CU3Ec__cctor_m766681161796D9912477BF60542C4DD1EB0D2096,
	U3CU3Ec__ctor_m3FBD26AEC83F79DACB13A7EF6FE5F539A71F0902,
	U3CU3Ec_U3C_cctorU3Eb__7_0_m3DE1C9F0E58017EDCEAFA5FEC90132A153B492F6,
	CameraPlayable_GetHandle_mA04469CA50B43AF6219F9967B8AEB310CB5455BD,
	CameraPlayable_Equals_mD0FA195F3EA6511043E8F0AA1680CEB7E0E2E2CF,
	MaterialEffectPlayable_GetHandle_m748319E116317E9ADD1EA36A4EDA488338471058,
	MaterialEffectPlayable_Equals_mC55640B5D29F90360F9743549FABD43C5AA320EC,
	TextureMixerPlayable_GetHandle_mB75CF651C6BDDF347ED6938D0F1DE4BED92BB7CD,
	TextureMixerPlayable_Equals_m6838329B39779020FC3309B7406B8A0418F44FE7,
	BuiltinRuntimeReflectionSystem_TickRealtimeProbes_m0CD6423541B0FCB022D55498C348A013E06E5F39,
	BuiltinRuntimeReflectionSystem_Dispose_m2CDBD30196F65463B8E86AC97DA2370A4D68762D,
	BuiltinRuntimeReflectionSystem_Dispose_m6B57B7E11B7A095063597FBCB0C6EE7036003F6B,
	BuiltinRuntimeReflectionSystem_BuiltinUpdate_m833B3EB0E69D46FDAB5A890FECB417C4D9D5D941,
	BuiltinRuntimeReflectionSystem_Internal_BuiltinRuntimeReflectionSystem_New_mA100197AC7CA73600AAD55A67E43039EEF8D2C27,
	BuiltinRuntimeReflectionSystem__ctor_mC85D8357332DEC8325E27837409E463208ACE0E5,
	NULL,
	ScriptableRuntimeReflectionSystemSettings_set_Internal_ScriptableRuntimeReflectionSystemSettings_system_mA216659518CF27854FB65C184B10197AB74AFBF7,
	ScriptableRuntimeReflectionSystemSettings_get_Internal_ScriptableRuntimeReflectionSystemSettings_instance_mED3776EB64E9E6BF61705125F20F6893C2098E03,
	ScriptableRuntimeReflectionSystemSettings_ScriptingDirtyReflectionSystemInstance_mCDA2744C4AD02637B40F84222084C18F0FC369EB,
	ScriptableRuntimeReflectionSystemSettings__cctor_m8225B0B8673C3B8B4529F9BFDAC91D417F1DB083,
	ScriptableRuntimeReflectionSystemWrapper_get_implementation_m1AFA781CCFEFE334D758AC43A9FAB9E0FB0F5C40,
	ScriptableRuntimeReflectionSystemWrapper_set_implementation_mF1552E093F0F437DF191D7CBB0CF7981C36744D8,
	ScriptableRuntimeReflectionSystemWrapper_Internal_ScriptableRuntimeReflectionSystemWrapper_TickRealtimeProbes_mDC08C9639CAF2D13623E82B3A9C51689D2FED2B3,
	ScriptableRuntimeReflectionSystemWrapper__ctor_mCF4DB3AC3AEB1FC08CB03DD0C1733E9BDED4DF8D,
	GraphicsFormatUtility_GetGraphicsFormat_mE38154E9B9C810EDAF2FAD3E1F1CD856FFC13F3C,
	GraphicsFormatUtility_GetGraphicsFormat_Native_TextureFormat_m4A193B562F6F81CE2C1C755B26B67564C2F65319,
	GraphicsFormatUtility_GetGraphicsFormat_mB9E291EB1EC96594074112E54A7B9CAC20FC7BFA,
	GraphicsFormatUtility_GetGraphicsFormat_Native_RenderTextureFormat_m27057B9C12BF2ADFF0C8A39BD7D03A9615304942,
	GraphicsFormatUtility_GetGraphicsFormat_m3DD7EAFBC4F60FA47453B93DAA7B392AEC818BD5,
	GraphicsFormatUtility_GetDepthStencilFormatFromBitsLegacy_Native_m6C8C3D62D09CAA5333599E53ED45700AEE76E06B,
	GraphicsFormatUtility_GetDepthStencilFormat_m76EEE7255F874FD3AC8E149830EE48F345DF8425,
	GraphicsFormatUtility_GetDepthBits_mA3ED2245DC3C1C593668C2F152A0DA42052CEE94,
	GraphicsFormatUtility_GetDepthStencilFormat_m963D66601AD1C71D4E90483076BCDB175F958321,
	GraphicsFormatUtility_IsSRGBFormat_mF3A393D43D68789A16087FF64CA2C050A8485C53,
	GraphicsFormatUtility_IsCompressedFormat_Native_TextureFormat_m3CE0D5ED90F6323D412C876460BE13792C8CCC0C,
	GraphicsFormatUtility_IsCompressedFormat_mE23F74E1C0D8EF955FB7D776C17FD6955FB700DD,
	GraphicsFormatUtility_CanDecompressFormat_mDC3A7D8AC07ABAC875EACD11F48C5B571E22CEC6,
	GraphicsFormatUtility_CanDecompressFormat_m443675E54409D934EE0DC0FDA5CF6D56DE9C4282,
	GraphicsFormatUtility_IsDepthStencilFormat_mB9AC0AC27E959CF7C23FDE141E3A4D3561FAC616,
	GraphicsFormatUtility_IsPVRTCFormat_m7B1CF5EAD3BAEF83A7B5B198C16F54FC9C081D13,
	GraphicsFormatUtility_IsCrunchFormat_mEEE165E8F2D82A469181DA2C4A5C227CCF585DAB,
	GraphicsFormatUtility__cctor_mA72A657D7B9FB8670567D2CB6B6FA3A1419980D1,
	Assert_Fail_mCC76351EF5EAA85F4012330D498CB802861B41BA,
	Assert_IsTrue_mE42C53B7220324D1FBAFB7AE48A7D8DD7796A663,
	Assert_IsTrue_m390B3F48332F46CE76AB45491A60ACDCCF521AAE,
	Assert_IsFalse_mC11212C418E6B5009F6474AE90FFB24734482E56,
	NULL,
	NULL,
	NULL,
	Assert_AreEqual_mA4CD725133015119B0219ABAA6337650814B803B,
	NULL,
	NULL,
	Assert_IsNull_mB7DAD5AA378E4A36BC1D27B88CFADD31023530BB,
	NULL,
	NULL,
	Assert_IsNotNull_mB889D13B7935F20A9CE32629C00434099E30D931,
	Assert_AreEqual_m718BB4BD31FA3176A3A236F96BC5405EB750D6CF,
	Assert__cctor_mC2C4583746B3D6E98224F4692EAE5EF17B5F9CC8,
	AssertionException__ctor_m01CD9ADC1B0067C20CDC2A0697BBF3969E67FEB6,
	AssertionException_get_Message_m4320D1607BDF97D451569837340C8E4A04461089,
	AssertionMessageUtil_GetMessage_m5AD26DAEC5DCCEB15C198EF6B70FED9E32BF299C,
	AssertionMessageUtil_GetMessage_m0785AB2BEBDA81CFE63F87428268C91D63685EB3,
	AssertionMessageUtil_GetEqualityMessage_m64D77BB9CA4284DD9561C238BB1F97B566830DBB,
	AssertionMessageUtil_NullFailureMessage_mECBDB36C0C5433898BC4D3CF0AE55CEFBBCC9A50,
	AssertionMessageUtil_BooleanFailureMessage_m1390F2418023DC1717341A836F0F40FBC5801FB4,
};
extern void JobHandle_Complete_mDCED35A125AAB37EDDAB2E31C805B4904B614A4A_AdjustorThunk (void);
extern void JobHandle_Equals_mD29DF760383603EABB2E626975DE8EB2F2A6234D_AdjustorThunk (void);
extern void JobScheduleParameters__ctor_m5FFED3B28A1FA2C3EC7D1C50A7D7E788C411CE04_AdjustorThunk (void);
extern void ProfilerCategory__ctor_m59B0D65E2CE7D723F30A4FAA5796A1CBE105B298_AdjustorThunk (void);
extern void ProfilerCategory_get_Name_mFED02A355294B8B0365E03D12BC1299E37442894_AdjustorThunk (void);
extern void ProfilerCategory_ToString_m091164890366F89EFBFC0FF811B897C234B67541_AdjustorThunk (void);
extern void ProfilerMarker__ctor_mDD68B0A8B71E0301F592AF8891560150E55699C8_AdjustorThunk (void);
extern void ProfilerMarker__ctor_m5958260A54C3A7F358A71AACDF47BA28178A5AB7_AdjustorThunk (void);
extern void ProfilerMarker_Auto_m133FA724EB95D16187B37D2C8A501D7E989B1F8D_AdjustorThunk (void);
extern void AutoScope__ctor_m7F63A273E382CB6328736B6E7F321DDFA40EA9E3_AdjustorThunk (void);
extern void AutoScope_Dispose_mED763F3F51261EF8FB79DB32CD06E0A3F6C40481_AdjustorThunk (void);
extern void DebugScreenCapture_set_RawImageDataReference_m935F402BCD29599C153DF8B982FAAE26FC1F9F24_AdjustorThunk (void);
extern void DebugScreenCapture_set_ImageFormat_mEB839CF83D4271BEBDE907158D8F2FBC9CB88FFE_AdjustorThunk (void);
extern void DebugScreenCapture_set_Width_m4C018D3EECDEDCA9EED150FE159218A403210451_AdjustorThunk (void);
extern void DebugScreenCapture_set_Height_m079B049644D69F95F746269B437D8DEBEC3701E7_AdjustorThunk (void);
extern void NativeArrayDispose_Dispose_mC90E863B89568E988E0F8FB8E79596A72C5EE061_AdjustorThunk (void);
extern void NativeArrayDisposeJob_Execute_m2F3CAEB5BFACF52C44D2A2485554D88018650A7F_AdjustorThunk (void);
extern void ApplicationMemoryUsageChange_set_memoryUsage_m331F962287453AC69EEE1222C0F11D222F7B2957_AdjustorThunk (void);
extern void ApplicationMemoryUsageChange__ctor_mD1B7299FCDDF14B479AF66AFDDFC2D710AC6A3C0_AdjustorThunk (void);
extern void PcgRandom__ctor_m60661E44F818E77DABECCC669F8802633503DF1E_AdjustorThunk (void);
extern void PcgRandom_GetUInt_mE5C9062173864BB7154CA25573D2835FA70589CA_AdjustorThunk (void);
extern void PcgRandom_Step_m0C0C0E3BED534CFC75FC9DF507A708043B1278B0_AdjustorThunk (void);
extern void Bounds__ctor_mAF7B238B9FBF90C495E5D7951760085A93119C5A_AdjustorThunk (void);
extern void Bounds_GetHashCode_m59C79B529D33866FE45FEFC0C69FBD3B4AC7E172_AdjustorThunk (void);
extern void Bounds_Equals_m93E0B9D24C73E57A6FABB9D312101D48183C88CC_AdjustorThunk (void);
extern void Bounds_Equals_m615135524315743D29633C33B6C8B16B754266DB_AdjustorThunk (void);
extern void Bounds_get_center_m5B05F81CB835EB6DD8628FDA24B638F477984DC3_AdjustorThunk (void);
extern void Bounds_set_center_m891869DD5B1BEEE2D17907BBFB7EB79AAE44884B_AdjustorThunk (void);
extern void Bounds_get_size_m0699A53A55A78B3201D7270D6F338DFA91B6FAD4_AdjustorThunk (void);
extern void Bounds_set_size_m950CFB68CDD1BF409E770509A38B958E1AE68128_AdjustorThunk (void);
extern void Bounds_get_extents_mFE6DC407FCE2341BE2C750CB554055D211281D25_AdjustorThunk (void);
extern void Bounds_set_extents_m09496358547B86A93EFE7BE6371E7A6FE937C46F_AdjustorThunk (void);
extern void Bounds_get_min_m465AC9BBE1DE5D8E8AD95AC19B9899068FEEBB13_AdjustorThunk (void);
extern void Bounds_get_max_m6446F2AB97C1E57CA89467B9DE52D4EB61F1CB09_AdjustorThunk (void);
extern void Bounds_SetMinMax_mB5F7DDF18EDB7F3F25FA6D2B36824F28978C540F_AdjustorThunk (void);
extern void Bounds_Encapsulate_m1FCA57C58536ADB67B85A703470C6F5BFB837C2F_AdjustorThunk (void);
extern void Bounds_ToString_m1BCCCC8C6455A77DE5C964968C33305EF7A4A0D2_AdjustorThunk (void);
extern void Bounds_ToString_m085531A8E800327829FCD48DEA671A4A0B8D21CA_AdjustorThunk (void);
extern void BoundsInt_get_position_m0A58811AA258865B63CCFEDD693E278367411B4B_AdjustorThunk (void);
extern void BoundsInt_set_position_m72954A6270A27FCC62B2B32290CE32D5784A837E_AdjustorThunk (void);
extern void BoundsInt_get_size_mE7C4A0C3BF45CEA7A28ABF98E2C15CB69EF3A32C_AdjustorThunk (void);
extern void BoundsInt_set_size_m518DA559D9E67DE136B3CCB37470E147FA088CE1_AdjustorThunk (void);
extern void BoundsInt__ctor_m93F7EDF326B3BA01465FA229F6CEED0ED48D32FF_AdjustorThunk (void);
extern void BoundsInt_ToString_mACB0BAF0A766690D30CA39FF52EA783583653B3F_AdjustorThunk (void);
extern void BoundsInt_ToString_m0505A1F9CB063D0F588D7212D695894D5B1460D8_AdjustorThunk (void);
extern void BoundsInt_Equals_m4C99DB2D3AD7DD9E0A75562F0AE24A14AE63587D_AdjustorThunk (void);
extern void BoundsInt_Equals_m143E0673DA604FDEBBF40115D50BE078E343F1E6_AdjustorThunk (void);
extern void BoundsInt_GetHashCode_m9740EA5B8C8E9B4DD47D9D6E619D61F5B99115CC_AdjustorThunk (void);
extern void Plane_get_normal_mA161D94E6F7327BC111007C880B76E1731729EFB_AdjustorThunk (void);
extern void Plane__ctor_m2BFB65EBFF51123791878684ECC375B99FAD10A2_AdjustorThunk (void);
extern void Plane_Raycast_mC6D25A732413A2694A75CB0F2F9E75DEDDA117F0_AdjustorThunk (void);
extern void Plane_ToString_mF0A98DAF2E4FA36A98B68F015A4DE507D8BB3B5A_AdjustorThunk (void);
extern void Plane_ToString_mE12B74C757E52A84BE921DF2E758A36E97A11DDA_AdjustorThunk (void);
extern void Ray__ctor_mE298992FD10A3894C38373198385F345C58BD64C_AdjustorThunk (void);
extern void Ray_get_origin_m97604A8F180316A410DCD77B7D74D04522FA1BA6_AdjustorThunk (void);
extern void Ray_get_direction_m21C2D22D3BD4A683BD4DC191AB22DD05F5EC2086_AdjustorThunk (void);
extern void Ray_GetPoint_mAF4E1D38026156E6434EF2BED2420ED5236392AF_AdjustorThunk (void);
extern void Ray_ToString_m06274331D92120539B4C6E0D3747EE620DB468E5_AdjustorThunk (void);
extern void Ray_ToString_mA76F7B86876505F674F3E20C18C8258103622C10_AdjustorThunk (void);
extern void Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_AdjustorThunk (void);
extern void Rect__ctor_m503705FE0E4E413041E3CE7F09270489F401C675_AdjustorThunk (void);
extern void Rect__ctor_m5665723DD0443E990EA203A54451B2BB324D8224_AdjustorThunk (void);
extern void Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB_AdjustorThunk (void);
extern void Rect_set_x_mAB91AB71898A20762BC66FD0723C4C739C4C3406_AdjustorThunk (void);
extern void Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49_AdjustorThunk (void);
extern void Rect_set_y_mDE91F4B98A6E8623EFB1250FF6526D5DB5855629_AdjustorThunk (void);
extern void Rect_get_position_m9B7E583E67443B6F4280A676E644BB0B9E7C4E38_AdjustorThunk (void);
extern void Rect_set_position_m9CD8AA25A83A7A893429C0ED56C36641202C3F05_AdjustorThunk (void);
extern void Rect_get_center_mAA9A2E1F058B2C9F58E13CC4822F789F42975E5C_AdjustorThunk (void);
extern void Rect_get_min_mD0D1BABF9C955D2D9CCA86E257B0783ACDEE69AC_AdjustorThunk (void);
extern void Rect_set_min_m6557D7D73C6F115CA7C92E38C88EA9E95FC89253_AdjustorThunk (void);
extern void Rect_get_max_m60149158D9A01113214BB417AA48CEF774899167_AdjustorThunk (void);
extern void Rect_set_max_mAD2D6D5DC1F5A6E69A0A0BD7E34C209F91C381F0_AdjustorThunk (void);
extern void Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9_AdjustorThunk (void);
extern void Rect_set_width_m93B6217CF3EFF89F9B0C81F34D7345DE90B93E5A_AdjustorThunk (void);
extern void Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_AdjustorThunk (void);
extern void Rect_set_height_mD00038E6E06637137A5626CA8CD421924005BF03_AdjustorThunk (void);
extern void Rect_get_size_mFB990FFC0FE0152179C8C74A59E4AC258CB44267_AdjustorThunk (void);
extern void Rect_set_size_m346E4F7077E5A1C0F4E21966232CD726CB9E6BAA_AdjustorThunk (void);
extern void Rect_get_xMin_mE89C40702926D016A633399E20DB9501E251630D_AdjustorThunk (void);
extern void Rect_set_xMin_mA873FCFAF9EABA46A026B73CA045192DF1946F19_AdjustorThunk (void);
extern void Rect_get_yMin_mB19848FB25DE61EDF958F7A22CFDD86DE103062F_AdjustorThunk (void);
extern void Rect_set_yMin_m9F780E509B9215A9E5826178CF664BD0E486D4EE_AdjustorThunk (void);
extern void Rect_get_xMax_m2339C7D2FCDA98A9B007F815F6E2059BA6BE425F_AdjustorThunk (void);
extern void Rect_set_xMax_m97C28D468455A6D19325D0D862E80A093240D49D_AdjustorThunk (void);
extern void Rect_get_yMax_mBC37BEE1CD632AADD8B9EAF9FE3BA143F79CAF8E_AdjustorThunk (void);
extern void Rect_set_yMax_mCF452040E0068A4B3CB15994C0B4B6AD4D78E04B_AdjustorThunk (void);
extern void Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B_AdjustorThunk (void);
extern void Rect_Contains_mB1160CD465F3E9616AA4EED72AFFD611BD8D2B6B_AdjustorThunk (void);
extern void Rect_Overlaps_m5A540A24DAD3327006A3A2E209CC17992173B572_AdjustorThunk (void);
extern void Rect_Overlaps_m3F0BA2C8BB81491978B21EB21C8A6D3BBED02E41_AdjustorThunk (void);
extern void Rect_GetHashCode_m8E55539476EA6B7A6E0CEC5F980227CD15B778F1_AdjustorThunk (void);
extern void Rect_Equals_mD7EB2046512E4A46524A7ED929F1C38A32C408F8_AdjustorThunk (void);
extern void Rect_Equals_mE725DE506D3F1DB92F58B876BDA42AACD4D991B5_AdjustorThunk (void);
extern void Rect_ToString_m7BF74645745862DA4751965D0899F94376F77F10_AdjustorThunk (void);
extern void Rect_ToString_mA9EB8EC6A2E940240E0D0ECFA103B9B7EFB3D532_AdjustorThunk (void);
extern void RectInt_get_x_mA1E7EF6DEAD2E900D7D56B7A3957C05081EBA9CA_AdjustorThunk (void);
extern void RectInt_set_x_m2D2F3A87E9899A29444DBDD0BB11CB19F13AA075_AdjustorThunk (void);
extern void RectInt_get_y_m440422264E6FCAA91E01F81486A78037AC29D878_AdjustorThunk (void);
extern void RectInt_set_y_m45D5C1D817698266BED66D41A705956A1571858D_AdjustorThunk (void);
extern void RectInt_get_width_m6B7B2FB764EAE83B7F63E7F77FA33973606761A7_AdjustorThunk (void);
extern void RectInt_set_width_mCD96AA9D096114147F8411A340CE4AD3476DCD4C_AdjustorThunk (void);
extern void RectInt_get_height_mE25FB938714942D7A3BA0B3C21BC5CB913D5709C_AdjustorThunk (void);
extern void RectInt_set_height_m823A353A80B8B5180AEDF968A6E85B9D9B75C1C6_AdjustorThunk (void);
extern void RectInt_get_xMin_mA5FB5AF1133380E080CF750D21327DE27EADEE1B_AdjustorThunk (void);
extern void RectInt_get_yMin_m6914C2254158DF797E20E381626DC08A2700147B_AdjustorThunk (void);
extern void RectInt_get_xMax_mBA05CE52847E3D3CB8295055706B3E0D4350E9F0_AdjustorThunk (void);
extern void RectInt_get_yMax_mAE5D758A1241F7722F8FB9B46861583F76C8FE44_AdjustorThunk (void);
extern void RectInt__ctor_m6E8B3A6C7EE11257A6B438E36274116FE39B5B42_AdjustorThunk (void);
extern void RectInt_Overlaps_m9E82E1C8BFDA3297221E5FDC8B8773AD3F50C4EE_AdjustorThunk (void);
extern void RectInt_ToString_m7EC8BB4830459B8CF5BF3032E9A526A6EE18D386_AdjustorThunk (void);
extern void RectInt_ToString_m7EAE8CA8D77B7D6DDD46B61D670C71046006B92F_AdjustorThunk (void);
extern void RectInt_Equals_mE9EA164664CA30C1C099EFB658D691F55A793B96_AdjustorThunk (void);
extern void RefreshRate_get_value_m7F8BB0D20DAB1EF882F1FC97E0C7618FCD319561_AdjustorThunk (void);
extern void RefreshRate_Equals_m16184432DA438F6BAF730078987262C7DE97913C_AdjustorThunk (void);
extern void RefreshRate_CompareTo_mD74AD821FF0DA633F9719E2B824C84E77E443D66_AdjustorThunk (void);
extern void RefreshRate_ToString_m59B00D8F20B6DAB001CD394F23653AC693DF8047_AdjustorThunk (void);
extern void Resolution_ToString_m058CE120CC83F314D0C8D4A706F9AA068BC9CF34_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_width_mB159E4EB08B23B19CCCFADB465864361FB840BFF_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_width_m3B2494007BFE3AD4D14403407C9B24F5045E7E10_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_height_m1006F9AA45029715C552C8A8C2F102F63D3A91EC_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_height_m1FE41111472DAA9B5E80FFAF3445004D72A3CFA5_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_volumeDepth_m528818299E101F1B285B08BE12FAC2F9A871BA36_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_volumeDepth_mEF9610D1C14182417A01B7243DEE6B559A13B34D_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_mipCount_mDCC85ED7D97BD64A290A21DB91BC5CB1C4BA95EF_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_graphicsFormat_m50F25A4F179EA318C8D3B0D8685F9C5F59F7DEC0_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_graphicsFormat_m037DA25F9A8B956D830C7B7E5C6E258DC1133A13_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_depthStencilFormat_m360929BE5BD10E9C3D8C936AA6B44B1D11C119CB_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_depthStencilFormat_m7EC335006743B59AAD1895AD06B2DD74333F9A03_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_depthBufferBits_mC095E36F9803B2E68E258C03E48ACD0B0E678953_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_depthBufferBits_mA3710C0D6E485BA6465B328CD8B1954F0E4C5819_AdjustorThunk (void);
extern void RenderTextureDescriptor_get_dimension_mA23ABB2CA03249DCE3A21F5123524A825C33E31B_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_dimension_mCE9A4A08454BB2D9DFE3E505EC336FD480078F39_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_shadowSamplingMode_m4B4CE918DFFF1CC5E3AF981456E186F15FC5DB93_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_vrUsage_m994CB3D4B250A70BE005D9FDFD24D868E07A52F0_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_memoryless_m9ECE149930C0E2629A5CD9DA1CD0EA2A01FFE1B2_AdjustorThunk (void);
extern void RenderTextureDescriptor__ctor_m8B0D32DC550540B5546891C2F6300F384D6FE692_AdjustorThunk (void);
extern void RenderTextureDescriptor__ctor_m8F8897C63F614AEA4348A95293C911C1293DA3A4_AdjustorThunk (void);
extern void RenderTextureDescriptor_SetOrClearRenderTextureCreationFlag_m4C08C7A3F715426EBECA2B983361908D097C6424_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_createdFromScript_mEE28DED1D3C20DA025A0C44E1C2A531685194F23_AdjustorThunk (void);
extern void RenderTextureDescriptor_set_useDynamicScale_m9335866C8754D51235D391E84F8972C4C518844A_AdjustorThunk (void);
extern void Hash128__ctor_m0B61E717B3FF7D7BBD8FF12C8C8327C18A2AAAF3_AdjustorThunk (void);
extern void Hash128_CompareTo_mFFE3648A372A7F3202897B266E14A6E2521BFE0C_AdjustorThunk (void);
extern void Hash128_ToString_m35513B62830C8CE59346AF87AC2B587FA1570DCE_AdjustorThunk (void);
extern void Hash128_Equals_m28FADCC2F9A565AF152A53BCEEF88F798716B104_AdjustorThunk (void);
extern void Hash128_Equals_mF6BED87E0744D6DFFF8026614BDA8F4E6712063D_AdjustorThunk (void);
extern void Hash128_GetHashCode_m22816EE33CD973D11CD1917DEF7A0E0EC229E1D8_AdjustorThunk (void);
extern void Hash128_CompareTo_m1D249BA0DD01AF06E5B96B186E8A5F181BB30758_AdjustorThunk (void);
extern void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_AdjustorThunk (void);
extern void Color__ctor_mCD6889CDE39F18704CD6EA8E2EFBFA48BA3E13B0_AdjustorThunk (void);
extern void Color_ToString_m0018DE2184B3377CCA6FBD72D5D47886DC669147_AdjustorThunk (void);
extern void Color_ToString_m70AEF3634C556F6AA01FC3236226C3D27C277229_AdjustorThunk (void);
extern void Color_GetHashCode_m2981EEA1DEFE55254945D7D03BE64D4F56BA58D0_AdjustorThunk (void);
extern void Color_Equals_m24E409BF329F25774C6577F18EFD3DE1253684D6_AdjustorThunk (void);
extern void Color_Equals_mD297CAFFEBE9352C940873862FDF9A28F1F02435_AdjustorThunk (void);
extern void Color_RGBMultiplied_m4B3BAE4310EA98451D608E0300331012AFFF1B01_AdjustorThunk (void);
extern void Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_AdjustorThunk (void);
extern void Color_get_maxColorComponent_m97D2940D48767ACC21D76F8CCEAD6898B722529C_AdjustorThunk (void);
extern void Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_AdjustorThunk (void);
extern void Color32_ToString_mB1EFBF981F901A76ABF2FEA19EB290A2D8CAFC32_AdjustorThunk (void);
extern void Color32_ToString_m263D34787852D176627FC2B910DFE9CABAF26696_AdjustorThunk (void);
extern void Matrix4x4_GetLossyScale_m3C19D2C6746BB211C8CB02478A60EB2D71D10FC7_AdjustorThunk (void);
extern void Matrix4x4_get_lossyScale_mFB3D4CF6408D710D607CA1D2AF94B2E4E0B57EB7_AdjustorThunk (void);
extern void Matrix4x4_get_inverse_m4F4A881CD789281EA90EB68CFD39F36C8A81E6BD_AdjustorThunk (void);
extern void Matrix4x4__ctor_m6523044D700F15EC6BCD183633A329EE56AA8C99_AdjustorThunk (void);
extern void Matrix4x4_GetHashCode_m313B1AF4FEA792BB7E4D1D239EBE3257F825914D_AdjustorThunk (void);
extern void Matrix4x4_Equals_m35CFC5F304BB40EFFE011B92AA87B77CD8FF8F74_AdjustorThunk (void);
extern void Matrix4x4_Equals_mDB0C4CCC58BE3E108F1A40BE8DBDCD62E284CC51_AdjustorThunk (void);
extern void Matrix4x4_GetColumn_m5CE079D7A69DE70E3144BADD20A1651C73A8D118_AdjustorThunk (void);
extern void Matrix4x4_GetRow_m59C6981300C6F6927BEA17C5D095B2AD29629E9F_AdjustorThunk (void);
extern void Matrix4x4_MultiplyPoint_m20E910B65693559BFDE99382472D8DD02C862E7E_AdjustorThunk (void);
extern void Matrix4x4_MultiplyPoint3x4_mACCBD70AFA82C63DA88555780B7B6B01281AB814_AdjustorThunk (void);
extern void Matrix4x4_ToString_mB310BE20B7CDE8AEA91D84FCA4E75BCACF7DFC86_AdjustorThunk (void);
extern void Matrix4x4_ToString_mB8E1EFF14A37605ABA321708CC36021FFBCF84CF_AdjustorThunk (void);
extern void Vector3_get_Item_m163510BFC2F7BFAD1B601DC9F3606B799CF199F2_AdjustorThunk (void);
extern void Vector3_set_Item_m79136861DEC5862CE7EC20AB3B0EF10A3957CEC3_AdjustorThunk (void);
extern void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_AdjustorThunk (void);
extern void Vector3__ctor_m5F87930F9B0828E5652E2D9D01ED907C01122C86_AdjustorThunk (void);
extern void Vector3_GetHashCode_mB08429DC931A85BD29CE11B9ABC77DE7E0E46327_AdjustorThunk (void);
extern void Vector3_Equals_mB4BE43D5986864F5C22B919F2957E0309F10E3B4_AdjustorThunk (void);
extern void Vector3_Equals_mEDEAF86793D229455BBF9BA5B30DDF438D6CABC1_AdjustorThunk (void);
extern void Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_AdjustorThunk (void);
extern void Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_AdjustorThunk (void);
extern void Vector3_ToString_m6C24B9F0382D25D75B05C606E127CD14660574EE_AdjustorThunk (void);
extern void Vector3_ToString_mA8DA39B6324392BB93203A4D4CB85AF87231CB62_AdjustorThunk (void);
extern void Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_AdjustorThunk (void);
extern void Quaternion_GetHashCode_m5F55C34C98E437376595E722BE4EB8A70434F049_AdjustorThunk (void);
extern void Quaternion_Equals_mCF93B00BA4FCDDE6100918451343DB9A0583A0A0_AdjustorThunk (void);
extern void Quaternion_Equals_m25B95D8412B79CC7F8B34062BFAE662BD99578BE_AdjustorThunk (void);
extern void Quaternion_ToString_mC5BD5DEF60FCA4A38924462A5C4440ECFCF934C4_AdjustorThunk (void);
extern void Quaternion_ToString_m9B592D577B3FDB892CA53ABF3457BC2EDE45DF8C_AdjustorThunk (void);
extern void Vector2_get_Item_m18BC65BB0512B16A1F9C8BE4B83A3E7BBAD7064D_AdjustorThunk (void);
extern void Vector2_set_Item_mEF309880B9B3B370B542AABEB32256EEC589DD03_AdjustorThunk (void);
extern void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_AdjustorThunk (void);
extern void Vector2_ToString_mB47B29ECB21FA3A4ACEABEFA18077A5A6BBCCB27_AdjustorThunk (void);
extern void Vector2_ToString_mC10F098442E56919947154402A77EDE28DC9B7BE_AdjustorThunk (void);
extern void Vector2_GetHashCode_mED8670C0E122B7ED0DAB4C3381ADFF04B75E0B03_AdjustorThunk (void);
extern void Vector2_Equals_mA4E81D6FCE503DBD502BA499708344410F60DA4E_AdjustorThunk (void);
extern void Vector2_Equals_mDF84D5ED14E018609C6A9C9BAE016C1B33BCFF4C_AdjustorThunk (void);
extern void Vector2_get_magnitude_m5C59B4056420AEFDB291AD0914A3F675330A75CE_AdjustorThunk (void);
extern void Vector2_get_sqrMagnitude_mA16336720C14EEF8BA9B55AE33B98C9EE2082BDC_AdjustorThunk (void);
extern void Vector2Int_get_x_mA2CACB1B6E6B5AD0CCC32B2CD2EDCE3ECEB50576_AdjustorThunk (void);
extern void Vector2Int_set_x_m291ECF246536852F0B8EE049C4A3768E4999CDC8_AdjustorThunk (void);
extern void Vector2Int_get_y_m48454163ECF0B463FB5A16A0C4FC4B14DB0768B3_AdjustorThunk (void);
extern void Vector2Int_set_y_mF81881204EEE272BA409728C7EBFDE3A979DDF6A_AdjustorThunk (void);
extern void Vector2Int__ctor_mC20D1312133EB8CB63EC11067088B043660F11CE_AdjustorThunk (void);
extern void Vector2Int_Equals_m6D91EFAA6B3254334436BD262A4547EA08281BA3_AdjustorThunk (void);
extern void Vector2Int_Equals_m32811BA0576C096D5EB5C0CFD8231478F17229A6_AdjustorThunk (void);
extern void Vector2Int_GetHashCode_mA3B6135FA770AF0C171319B50D9B913657230EB7_AdjustorThunk (void);
extern void Vector2Int_ToString_m6F7E9B9B45A473FED501EB8B8B25BA1FE26DD5D4_AdjustorThunk (void);
extern void Vector2Int_ToString_m44BA6941AEF41076A39848B95DDEFEA88A094B5E_AdjustorThunk (void);
extern void Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_AdjustorThunk (void);
extern void Vector3Int_set_x_m8745C5976D035EBBAC6F6191B5838D58631D8685_AdjustorThunk (void);
extern void Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_AdjustorThunk (void);
extern void Vector3Int_set_y_mA856F32D1BF187BD4091DDF3C6872FD01F7D3377_AdjustorThunk (void);
extern void Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_AdjustorThunk (void);
extern void Vector3Int_set_z_m5782180F67C4257C505F124971985D99C3422F74_AdjustorThunk (void);
extern void Vector3Int__ctor_mE06A86999D16FA579A7F2142B872AB7E3695C9E0_AdjustorThunk (void);
extern void Vector3Int_Equals_m419967067E76BF0381E4CD8FE14DF5ED46ACFB02_AdjustorThunk (void);
extern void Vector3Int_Equals_mE4D179C5001B77DE05E3E4BC39DC9F6AE441EBD8_AdjustorThunk (void);
extern void Vector3Int_GetHashCode_mFAA200CFE26F006BEE6F9A65AFD0AC8C49D730EA_AdjustorThunk (void);
extern void Vector3Int_ToString_m49EB16DEA24181270D65A0F4ED39B3E8A46DB539_AdjustorThunk (void);
extern void Vector3Int_ToString_m74C285E175F089CED3A53A678216CD15A0AD0067_AdjustorThunk (void);
extern void Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_AdjustorThunk (void);
extern void Vector4_set_Item_mF24782F861A16BB0436C2262FA916B4EE69998A6_AdjustorThunk (void);
extern void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_AdjustorThunk (void);
extern void Vector4_GetHashCode_m53F6FCA56A0051C9D1AB41AA3EAA2C86CDAA8B92_AdjustorThunk (void);
extern void Vector4_Equals_mC2596CA0F441D25DE7A9419BE66A8FA2DA355CB9_AdjustorThunk (void);
extern void Vector4_Equals_m73FAA65A1A565EE28D6C9385603829894B7D4392_AdjustorThunk (void);
extern void Vector4_get_sqrMagnitude_m864A2908BCF9E060BA73DE3DD259EC06F47F913C_AdjustorThunk (void);
extern void Vector4_ToString_mFA0DDF34C1E394F75EF65E06764A1BE750E7F388_AdjustorThunk (void);
extern void Vector4_ToString_m2BE67BEBBD3059C9CEE29BF34AD30E1D7057E914_AdjustorThunk (void);
extern void TypeDispatchData_Dispose_m5E70993B62AFC8BB19163A8D7711BD3B5CC9424C_AdjustorThunk (void);
extern void TransformDispatchData_Dispose_m1C2E3B8C249D6E8785C19CC17C7ACBA4167A31BF_AdjustorThunk (void);
extern void PropertyName__ctor_mFA341118B63F88B69464A6F1DF248B569686D778_AdjustorThunk (void);
extern void PropertyName__ctor_m9F28B1DA9CEDDEAA6663C795456B9F5E95CAE92F_AdjustorThunk (void);
extern void PropertyName_GetHashCode_m7D4B878BA78625CDD78C9B045B5EADFDBF94C378_AdjustorThunk (void);
extern void PropertyName_Equals_mFD87005C352B7BEB4279560B1A489409B0692143_AdjustorThunk (void);
extern void PropertyName_Equals_m7D00F3B148E65210A82101C31F1F217C9527113E_AdjustorThunk (void);
extern void PropertyName_ToString_mDE271855F7B9A516185A66E12D90FE6B6C9DF6C0_AdjustorThunk (void);
extern void AssemblyVersion_ToString_mEB86A8E88D80F7C7459258EDF519B208400FA74B_AdjustorThunk (void);
extern void AssemblyVersion_Equals_m9FA3C12B7C0743C563E712CD08672EF9E99E1ADD_AdjustorThunk (void);
extern void AssemblyVersion_GetHashCode_mC191CC77C4664CD9A3F59E18BEE76E6BB5DA336F_AdjustorThunk (void);
extern void AssemblyFullName_Equals_mCCD71330EA2201DC5C3452AEEFF051AF203D0E9E_AdjustorThunk (void);
extern void AssemblyFullName_GetHashCode_m470DD9F4F78B21172D3A438C139ABF5587B0C4D0_AdjustorThunk (void);
extern void AssemblyFullName_ToString_m4D31288544EE855F460B3833AB0D51DF0AEADCEF_AdjustorThunk (void);
extern void RangeInt_get_end_m5835FBEB410CB8AC0928AEFD95728A9AD57F6C63_AdjustorThunk (void);
extern void RangeInt__ctor_m3CB91E79C7B5AED97E564581025B2F66778B7CBE_AdjustorThunk (void);
extern void WorkRequest__ctor_m78DC33ED88BF9BA29E05D2180B2ADC439132F1F5_AdjustorThunk (void);
extern void WorkRequest_Invoke_mBFEC6A3799BAFDE7ED840A0523D3D56160C03921_AdjustorThunk (void);
extern void DrivenRectTransformTracker_Add_mC0CE417831BF58E6DA81770CE5E2A99B142EEFEC_AdjustorThunk (void);
extern void DrivenRectTransformTracker_Clear_m9A7F5130E4007F70B14AB1FF13A2997C073A64EE_AdjustorThunk (void);
extern void MovedFromAttributeData_Set_m155005FB8BCE0569C40B02D75FFADB3FBDB7EEDD_AdjustorThunk (void);
extern void Scene_get_handle_mD508BE60333C6168610E12CECAB12E9B11C25E53_AdjustorThunk (void);
extern void Scene_GetHashCode_m74ACBFB8C656D5620A9A1E62D04E55ACBF63ADC9_AdjustorThunk (void);
extern void Scene_Equals_mB973481492F291BF8EAF1AD66B9F7FA3ACF3928D_AdjustorThunk (void);
extern void PlayerLoopSystem_ToString_m259B8533D2C64C15D381B16F32C710A0018684A0_AdjustorThunk (void);
extern void VertexAttributeDescriptor_get_attribute_m2F7C7084857741A39E9FF0D4BABF98F9C7018AAA_AdjustorThunk (void);
extern void VertexAttributeDescriptor_set_attribute_m526ED099EEA12DCB2FCB1BC88445FBF921E850A2_AdjustorThunk (void);
extern void VertexAttributeDescriptor_get_format_mF8EF98F3E047BC26D177C3A008BCDAF979E52C79_AdjustorThunk (void);
extern void VertexAttributeDescriptor_set_format_mCA82263EBD7802621D9ECE42D0F44D4AAECD46C5_AdjustorThunk (void);
extern void VertexAttributeDescriptor_get_dimension_mAB440DDFA08BF61D717547EC8B9C43C3DFC56FB8_AdjustorThunk (void);
extern void VertexAttributeDescriptor_set_dimension_mEF4325AE27221C732B214354A2DEB044ACFC69EE_AdjustorThunk (void);
extern void VertexAttributeDescriptor_get_stream_mAA4D41E2B4B9024161B176700100ADB27855E4FF_AdjustorThunk (void);
extern void VertexAttributeDescriptor_set_stream_m9A3991AA0365A1DCEDF80A61990F7B48913A1C0A_AdjustorThunk (void);
extern void VertexAttributeDescriptor__ctor_m713B31395FB13FDEB2665F5C4C31572D5875A43A_AdjustorThunk (void);
extern void VertexAttributeDescriptor_ToString_m45F7E7D6715E000C617173246F2C9BE23DF3FC8F_AdjustorThunk (void);
extern void VertexAttributeDescriptor_GetHashCode_mCB8C5F6AD422FE6A410C5406843FCE4B0ED5F2F8_AdjustorThunk (void);
extern void VertexAttributeDescriptor_Equals_m6FBFEE42E1BAAAC2D38434EDF0906C5B76D56BE5_AdjustorThunk (void);
extern void VertexAttributeDescriptor_Equals_mF328DE139864C201987238048AC79F8925CA435D_AdjustorThunk (void);
extern void BatchID_GetHashCode_m3F4DFD3D722865BBC47F74C5E452E985B6BBBA3A_AdjustorThunk (void);
extern void BatchID_Equals_m953C8EE9ABF083CFAA3D7210F1085C9FD7EED71D_AdjustorThunk (void);
extern void BatchID_Equals_m4207BA1B44B5BF1DA2847571B6D9A981F5AE3CFF_AdjustorThunk (void);
extern void BatchMaterialID_GetHashCode_mE28980612614E66848ADABB7825CA73FFA39DD09_AdjustorThunk (void);
extern void BatchMaterialID_Equals_m776A152CA1957E92FCD24B4E2EDAF8A6D78E953E_AdjustorThunk (void);
extern void BatchMaterialID_Equals_m073A3DECD4CDE73F0CC314024B89399982E5BA83_AdjustorThunk (void);
extern void BatchMeshID_GetHashCode_m62EDD33030F375FAAD44A23FAD7901A2BE1D08A9_AdjustorThunk (void);
extern void BatchMeshID_Equals_mA501F8E992E7B32BD8985DD68D5D0B360A104A42_AdjustorThunk (void);
extern void BatchMeshID_Equals_m985986D473BB12E9F43C59B41846660884B15C06_AdjustorThunk (void);
extern void BatchPackedCullingViewID_GetHashCode_m588865495A1A1F2AC79A62D76B2DDC91D743F4A5_AdjustorThunk (void);
extern void BatchPackedCullingViewID_Equals_mD7B8D7EFB1678D80FC11B9FA1171988669CD0C7E_AdjustorThunk (void);
extern void BatchPackedCullingViewID_Equals_m2E5BC66A5B8D6737BABE5D6A0019F901192D2A9F_AdjustorThunk (void);
extern void BatchCullingContext__ctor_mB26CB3EE7FD392F94B9816FCA04D76B8AAB6FDF3_AdjustorThunk (void);
extern void LODParameters_Equals_mAD45601A9E881620B23A7922D8AA5AFBC91E0B9F_AdjustorThunk (void);
extern void LODParameters_Equals_mF956A067D82497A00AC8234322CD848E6FB7BADE_AdjustorThunk (void);
extern void LODParameters_GetHashCode_mECFCEAAAC935759A99C30C69BBC99A1148F46F40_AdjustorThunk (void);
extern void ScriptableRenderContext_GetCameras_Internal_m852016B3544E3ED5FEFB9695EC175622A5B6A8C8_AdjustorThunk (void);
extern void ScriptableRenderContext__ctor_m10159F14BB69F555C375E13BB77A1898FDB42FA5_AdjustorThunk (void);
extern void ScriptableRenderContext_GetCameras_m9B2329F79132EE49B719560AD739FD3601C44189_AdjustorThunk (void);
extern void ScriptableRenderContext_Equals_mBFDA5815F2B6ABA9B16503DA906B8BA42078718D_AdjustorThunk (void);
extern void ScriptableRenderContext_Equals_m99E5A233945DFC3B9A786F2413ECE68E5019AB88_AdjustorThunk (void);
extern void ScriptableRenderContext_GetHashCode_mA1EE09239F1ACFC29A2ADB027D5E76E690510207_AdjustorThunk (void);
extern void ShaderTagId__ctor_m4191968F1D2CE19F9092253EC10F83734A9CFF5B_AdjustorThunk (void);
extern void ShaderTagId_Equals_m02826F7AFC63AA3AE5DB14F7A891F8F173FD9A33_AdjustorThunk (void);
extern void ShaderTagId_Equals_m932EFCC38C276EEB2784BBC866330F4C595F52E0_AdjustorThunk (void);
extern void ShaderTagId_GetHashCode_mF5E3A1F96CBDFDCEFABE1B56125EBBA6E3B9EFEF_AdjustorThunk (void);
extern void StencilState_set_enabled_m6DC861C699D1044E896E833D2DAE69B82F796564_AdjustorThunk (void);
extern void StencilState_set_readMask_m1BA8F9033413889D4E77DA343DC0029566A9BB9B_AdjustorThunk (void);
extern void StencilState_set_writeMask_m94471C671E03D42F36DA61436B1068B362375D65_AdjustorThunk (void);
extern void StencilState_set_compareFunctionFront_m1388C37901DAB6AF9D23C0F01946DCCE19BC9BFC_AdjustorThunk (void);
extern void StencilState_set_passOperationFront_m1F15CC29366DAEAA6CCE1DB0622C70D6ECC5A3EB_AdjustorThunk (void);
extern void StencilState_set_failOperationFront_mADCECAE5D2E75ABAE51650F1F314E661D09C2CD6_AdjustorThunk (void);
extern void StencilState_set_zFailOperationFront_mC7D8F0A08B9AEC4203BD6B352CB795E8011EFBB6_AdjustorThunk (void);
extern void StencilState_set_compareFunctionBack_m8AF73F4E8FC95A46D33E3192C50702D2AA15D61D_AdjustorThunk (void);
extern void StencilState_set_passOperationBack_m4B1395FE21F5B5C809DC6F31D5824A90E05ED220_AdjustorThunk (void);
extern void StencilState_set_failOperationBack_mD279271DD1F99EE5B8BC19F5AE60988E6C6F0E4A_AdjustorThunk (void);
extern void StencilState_set_zFailOperationBack_mC092ABD8A5EA87245640A10E54C6A1990C4F6864_AdjustorThunk (void);
extern void StencilState_Equals_m9FFB8A41D8838FD128875CB2D4DAA760C6DF1051_AdjustorThunk (void);
extern void StencilState_Equals_mEA45A5D2BF2223B15EE0FB8BCEDBA9CB534ADF4B_AdjustorThunk (void);
extern void StencilState_GetHashCode_mB4A02DEE780377C853D16FFF49CCB9D9F4F711A5_AdjustorThunk (void);
extern void Playable__ctor_mD2EB35E024816AEED68795D0124EAB30E05BF6C4_AdjustorThunk (void);
extern void Playable_GetHandle_m39356D23E849DC5428B262092657662C064E04F8_AdjustorThunk (void);
extern void Playable_Equals_mD72D3DB892B8867A0E7BAC032A16C08616EEFF86_AdjustorThunk (void);
extern void PlayableHandle_Equals_m60AD76B7D38CA989AE84501B2E9F9ED5CB5F9670_AdjustorThunk (void);
extern void PlayableHandle_Equals_m81BA0E127133DFF3E45DA61D185FDF48E16BCF45_AdjustorThunk (void);
extern void PlayableHandle_GetHashCode_m10FB32ECDC0B9D7BDAEA9E3B76BDDF4614F4EF4F_AdjustorThunk (void);
extern void PlayableHandle_IsValid_m07631D12846BAAF2CC302E69A28A44BFE9EB5098_AdjustorThunk (void);
extern void PlayableHandle_GetPlayableType_mD9750F1B85DF086F52641D6AB85789601486B686_AdjustorThunk (void);
extern void PlayableOutput__ctor_m55FBB20EC479F67641835EA48D84A1AB3DF39747_AdjustorThunk (void);
extern void PlayableOutput_GetHandle_m12FF2889D15884CBEB92A6CB376827EBA5A607BF_AdjustorThunk (void);
extern void PlayableOutput_Equals_m4CC730818751114DC5643600B5FE20243F4B7121_AdjustorThunk (void);
extern void PlayableOutputHandle_GetHashCode_mC2FAF756D71026E3AF4492157EDAE7186429B079_AdjustorThunk (void);
extern void PlayableOutputHandle_Equals_mB9106CB9333E0BF4C893E43AD7A23B64471CC21A_AdjustorThunk (void);
extern void PlayableOutputHandle_Equals_mF5C23882B2A007186F00EB3D23E9BD6664E8DAE6_AdjustorThunk (void);
extern void LinearColor_get_red_m376617B8E3156420835055189BB28D953FE46A2A_AdjustorThunk (void);
extern void LinearColor_set_red_m0ACFCEDDD205A6F235BE95936816E92898B01B52_AdjustorThunk (void);
extern void LinearColor_get_green_mCCE90A662234EE3605368F3AEC14E51572665AE5_AdjustorThunk (void);
extern void LinearColor_set_green_mBD9C7EA6415DC54B3F6B643C3CD02B71565F0694_AdjustorThunk (void);
extern void LinearColor_get_blue_mAFAEA5D5590DD14CFC48BC18DF4BFEBBDCB0A99A_AdjustorThunk (void);
extern void LinearColor_set_blue_m3FEEAF946772BB177733B67D9DA4B72D84874375_AdjustorThunk (void);
extern void LightDataGI_Init_m112DEBB76EC57AC52E6384C97A3E8B2EAA867207_AdjustorThunk (void);
extern void LightDataGI_Init_mACE06E00CC639CA89F3847E9DB55FD0F00812A7A_AdjustorThunk (void);
extern void LightDataGI_Init_m0A999D118CDCBDA99B9E24231ED057D943C9C67B_AdjustorThunk (void);
extern void LightDataGI_Init_mDC887CA8191C6CADE1DB585D7FEB46B080B25038_AdjustorThunk (void);
extern void LightDataGI_Init_mB2D1C73EDFEA6815E39A0FE3ED2F7BF9A7117632_AdjustorThunk (void);
extern void LightDataGI_InitNoBake_mBDF2EFB22D4BEE63B6F25F4EE9F1522D2866ED43_AdjustorThunk (void);
extern void CameraPlayable_GetHandle_mA04469CA50B43AF6219F9967B8AEB310CB5455BD_AdjustorThunk (void);
extern void CameraPlayable_Equals_mD0FA195F3EA6511043E8F0AA1680CEB7E0E2E2CF_AdjustorThunk (void);
extern void MaterialEffectPlayable_GetHandle_m748319E116317E9ADD1EA36A4EDA488338471058_AdjustorThunk (void);
extern void MaterialEffectPlayable_Equals_mC55640B5D29F90360F9743549FABD43C5AA320EC_AdjustorThunk (void);
extern void TextureMixerPlayable_GetHandle_mB75CF651C6BDDF347ED6938D0F1DE4BED92BB7CD_AdjustorThunk (void);
extern void TextureMixerPlayable_Equals_m6838329B39779020FC3309B7406B8A0418F44FE7_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[344] = 
{
	{ 0x06000017, JobHandle_Complete_mDCED35A125AAB37EDDAB2E31C805B4904B614A4A_AdjustorThunk },
	{ 0x0600001D, JobHandle_Equals_mD29DF760383603EABB2E626975DE8EB2F2A6234D_AdjustorThunk },
	{ 0x06000026, JobScheduleParameters__ctor_m5FFED3B28A1FA2C3EC7D1C50A7D7E788C411CE04_AdjustorThunk },
	{ 0x0600002B, ProfilerCategory__ctor_m59B0D65E2CE7D723F30A4FAA5796A1CBE105B298_AdjustorThunk },
	{ 0x0600002C, ProfilerCategory_get_Name_mFED02A355294B8B0365E03D12BC1299E37442894_AdjustorThunk },
	{ 0x0600002D, ProfilerCategory_ToString_m091164890366F89EFBFC0FF811B897C234B67541_AdjustorThunk },
	{ 0x06000030, ProfilerMarker__ctor_mDD68B0A8B71E0301F592AF8891560150E55699C8_AdjustorThunk },
	{ 0x06000031, ProfilerMarker__ctor_m5958260A54C3A7F358A71AACDF47BA28178A5AB7_AdjustorThunk },
	{ 0x06000032, ProfilerMarker_Auto_m133FA724EB95D16187B37D2C8A501D7E989B1F8D_AdjustorThunk },
	{ 0x06000033, AutoScope__ctor_m7F63A273E382CB6328736B6E7F321DDFA40EA9E3_AdjustorThunk },
	{ 0x06000034, AutoScope_Dispose_mED763F3F51261EF8FB79DB32CD06E0A3F6C40481_AdjustorThunk },
	{ 0x06000035, DebugScreenCapture_set_RawImageDataReference_m935F402BCD29599C153DF8B982FAAE26FC1F9F24_AdjustorThunk },
	{ 0x06000036, DebugScreenCapture_set_ImageFormat_mEB839CF83D4271BEBDE907158D8F2FBC9CB88FFE_AdjustorThunk },
	{ 0x06000037, DebugScreenCapture_set_Width_m4C018D3EECDEDCA9EED150FE159218A403210451_AdjustorThunk },
	{ 0x06000038, DebugScreenCapture_set_Height_m079B049644D69F95F746269B437D8DEBEC3701E7_AdjustorThunk },
	{ 0x0600005F, NativeArrayDispose_Dispose_mC90E863B89568E988E0F8FB8E79596A72C5EE061_AdjustorThunk },
	{ 0x06000060, NativeArrayDisposeJob_Execute_m2F3CAEB5BFACF52C44D2A2485554D88018650A7F_AdjustorThunk },
	{ 0x060000C4, ApplicationMemoryUsageChange_set_memoryUsage_m331F962287453AC69EEE1222C0F11D222F7B2957_AdjustorThunk },
	{ 0x060000C5, ApplicationMemoryUsageChange__ctor_mD1B7299FCDDF14B479AF66AFDDFC2D710AC6A3C0_AdjustorThunk },
	{ 0x0600011F, PcgRandom__ctor_m60661E44F818E77DABECCC669F8802633503DF1E_AdjustorThunk },
	{ 0x06000120, PcgRandom_GetUInt_mE5C9062173864BB7154CA25573D2835FA70589CA_AdjustorThunk },
	{ 0x06000123, PcgRandom_Step_m0C0C0E3BED534CFC75FC9DF507A708043B1278B0_AdjustorThunk },
	{ 0x06000128, Bounds__ctor_mAF7B238B9FBF90C495E5D7951760085A93119C5A_AdjustorThunk },
	{ 0x06000129, Bounds_GetHashCode_m59C79B529D33866FE45FEFC0C69FBD3B4AC7E172_AdjustorThunk },
	{ 0x0600012A, Bounds_Equals_m93E0B9D24C73E57A6FABB9D312101D48183C88CC_AdjustorThunk },
	{ 0x0600012B, Bounds_Equals_m615135524315743D29633C33B6C8B16B754266DB_AdjustorThunk },
	{ 0x0600012C, Bounds_get_center_m5B05F81CB835EB6DD8628FDA24B638F477984DC3_AdjustorThunk },
	{ 0x0600012D, Bounds_set_center_m891869DD5B1BEEE2D17907BBFB7EB79AAE44884B_AdjustorThunk },
	{ 0x0600012E, Bounds_get_size_m0699A53A55A78B3201D7270D6F338DFA91B6FAD4_AdjustorThunk },
	{ 0x0600012F, Bounds_set_size_m950CFB68CDD1BF409E770509A38B958E1AE68128_AdjustorThunk },
	{ 0x06000130, Bounds_get_extents_mFE6DC407FCE2341BE2C750CB554055D211281D25_AdjustorThunk },
	{ 0x06000131, Bounds_set_extents_m09496358547B86A93EFE7BE6371E7A6FE937C46F_AdjustorThunk },
	{ 0x06000132, Bounds_get_min_m465AC9BBE1DE5D8E8AD95AC19B9899068FEEBB13_AdjustorThunk },
	{ 0x06000133, Bounds_get_max_m6446F2AB97C1E57CA89467B9DE52D4EB61F1CB09_AdjustorThunk },
	{ 0x06000136, Bounds_SetMinMax_mB5F7DDF18EDB7F3F25FA6D2B36824F28978C540F_AdjustorThunk },
	{ 0x06000137, Bounds_Encapsulate_m1FCA57C58536ADB67B85A703470C6F5BFB837C2F_AdjustorThunk },
	{ 0x06000138, Bounds_ToString_m1BCCCC8C6455A77DE5C964968C33305EF7A4A0D2_AdjustorThunk },
	{ 0x06000139, Bounds_ToString_m085531A8E800327829FCD48DEA671A4A0B8D21CA_AdjustorThunk },
	{ 0x0600013A, BoundsInt_get_position_m0A58811AA258865B63CCFEDD693E278367411B4B_AdjustorThunk },
	{ 0x0600013B, BoundsInt_set_position_m72954A6270A27FCC62B2B32290CE32D5784A837E_AdjustorThunk },
	{ 0x0600013C, BoundsInt_get_size_mE7C4A0C3BF45CEA7A28ABF98E2C15CB69EF3A32C_AdjustorThunk },
	{ 0x0600013D, BoundsInt_set_size_m518DA559D9E67DE136B3CCB37470E147FA088CE1_AdjustorThunk },
	{ 0x0600013E, BoundsInt__ctor_m93F7EDF326B3BA01465FA229F6CEED0ED48D32FF_AdjustorThunk },
	{ 0x0600013F, BoundsInt_ToString_mACB0BAF0A766690D30CA39FF52EA783583653B3F_AdjustorThunk },
	{ 0x06000140, BoundsInt_ToString_m0505A1F9CB063D0F588D7212D695894D5B1460D8_AdjustorThunk },
	{ 0x06000141, BoundsInt_Equals_m4C99DB2D3AD7DD9E0A75562F0AE24A14AE63587D_AdjustorThunk },
	{ 0x06000142, BoundsInt_Equals_m143E0673DA604FDEBBF40115D50BE078E343F1E6_AdjustorThunk },
	{ 0x06000143, BoundsInt_GetHashCode_m9740EA5B8C8E9B4DD47D9D6E619D61F5B99115CC_AdjustorThunk },
	{ 0x06000144, Plane_get_normal_mA161D94E6F7327BC111007C880B76E1731729EFB_AdjustorThunk },
	{ 0x06000145, Plane__ctor_m2BFB65EBFF51123791878684ECC375B99FAD10A2_AdjustorThunk },
	{ 0x06000146, Plane_Raycast_mC6D25A732413A2694A75CB0F2F9E75DEDDA117F0_AdjustorThunk },
	{ 0x06000147, Plane_ToString_mF0A98DAF2E4FA36A98B68F015A4DE507D8BB3B5A_AdjustorThunk },
	{ 0x06000148, Plane_ToString_mE12B74C757E52A84BE921DF2E758A36E97A11DDA_AdjustorThunk },
	{ 0x06000149, Ray__ctor_mE298992FD10A3894C38373198385F345C58BD64C_AdjustorThunk },
	{ 0x0600014A, Ray_get_origin_m97604A8F180316A410DCD77B7D74D04522FA1BA6_AdjustorThunk },
	{ 0x0600014B, Ray_get_direction_m21C2D22D3BD4A683BD4DC191AB22DD05F5EC2086_AdjustorThunk },
	{ 0x0600014C, Ray_GetPoint_mAF4E1D38026156E6434EF2BED2420ED5236392AF_AdjustorThunk },
	{ 0x0600014D, Ray_ToString_m06274331D92120539B4C6E0D3747EE620DB468E5_AdjustorThunk },
	{ 0x0600014E, Ray_ToString_mA76F7B86876505F674F3E20C18C8258103622C10_AdjustorThunk },
	{ 0x0600014F, Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_AdjustorThunk },
	{ 0x06000150, Rect__ctor_m503705FE0E4E413041E3CE7F09270489F401C675_AdjustorThunk },
	{ 0x06000151, Rect__ctor_m5665723DD0443E990EA203A54451B2BB324D8224_AdjustorThunk },
	{ 0x06000154, Rect_get_x_mB267B718E0D067F2BAE31BA477647FBF964916EB_AdjustorThunk },
	{ 0x06000155, Rect_set_x_mAB91AB71898A20762BC66FD0723C4C739C4C3406_AdjustorThunk },
	{ 0x06000156, Rect_get_y_mC733E8D49F3CE21B2A3D40A1B72D687F22C97F49_AdjustorThunk },
	{ 0x06000157, Rect_set_y_mDE91F4B98A6E8623EFB1250FF6526D5DB5855629_AdjustorThunk },
	{ 0x06000158, Rect_get_position_m9B7E583E67443B6F4280A676E644BB0B9E7C4E38_AdjustorThunk },
	{ 0x06000159, Rect_set_position_m9CD8AA25A83A7A893429C0ED56C36641202C3F05_AdjustorThunk },
	{ 0x0600015A, Rect_get_center_mAA9A2E1F058B2C9F58E13CC4822F789F42975E5C_AdjustorThunk },
	{ 0x0600015B, Rect_get_min_mD0D1BABF9C955D2D9CCA86E257B0783ACDEE69AC_AdjustorThunk },
	{ 0x0600015C, Rect_set_min_m6557D7D73C6F115CA7C92E38C88EA9E95FC89253_AdjustorThunk },
	{ 0x0600015D, Rect_get_max_m60149158D9A01113214BB417AA48CEF774899167_AdjustorThunk },
	{ 0x0600015E, Rect_set_max_mAD2D6D5DC1F5A6E69A0A0BD7E34C209F91C381F0_AdjustorThunk },
	{ 0x0600015F, Rect_get_width_m620D67551372073C9C32C4C4624C2A5713F7F9A9_AdjustorThunk },
	{ 0x06000160, Rect_set_width_m93B6217CF3EFF89F9B0C81F34D7345DE90B93E5A_AdjustorThunk },
	{ 0x06000161, Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_AdjustorThunk },
	{ 0x06000162, Rect_set_height_mD00038E6E06637137A5626CA8CD421924005BF03_AdjustorThunk },
	{ 0x06000163, Rect_get_size_mFB990FFC0FE0152179C8C74A59E4AC258CB44267_AdjustorThunk },
	{ 0x06000164, Rect_set_size_m346E4F7077E5A1C0F4E21966232CD726CB9E6BAA_AdjustorThunk },
	{ 0x06000165, Rect_get_xMin_mE89C40702926D016A633399E20DB9501E251630D_AdjustorThunk },
	{ 0x06000166, Rect_set_xMin_mA873FCFAF9EABA46A026B73CA045192DF1946F19_AdjustorThunk },
	{ 0x06000167, Rect_get_yMin_mB19848FB25DE61EDF958F7A22CFDD86DE103062F_AdjustorThunk },
	{ 0x06000168, Rect_set_yMin_m9F780E509B9215A9E5826178CF664BD0E486D4EE_AdjustorThunk },
	{ 0x06000169, Rect_get_xMax_m2339C7D2FCDA98A9B007F815F6E2059BA6BE425F_AdjustorThunk },
	{ 0x0600016A, Rect_set_xMax_m97C28D468455A6D19325D0D862E80A093240D49D_AdjustorThunk },
	{ 0x0600016B, Rect_get_yMax_mBC37BEE1CD632AADD8B9EAF9FE3BA143F79CAF8E_AdjustorThunk },
	{ 0x0600016C, Rect_set_yMax_mCF452040E0068A4B3CB15994C0B4B6AD4D78E04B_AdjustorThunk },
	{ 0x0600016D, Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B_AdjustorThunk },
	{ 0x0600016E, Rect_Contains_mB1160CD465F3E9616AA4EED72AFFD611BD8D2B6B_AdjustorThunk },
	{ 0x06000170, Rect_Overlaps_m5A540A24DAD3327006A3A2E209CC17992173B572_AdjustorThunk },
	{ 0x06000171, Rect_Overlaps_m3F0BA2C8BB81491978B21EB21C8A6D3BBED02E41_AdjustorThunk },
	{ 0x06000174, Rect_GetHashCode_m8E55539476EA6B7A6E0CEC5F980227CD15B778F1_AdjustorThunk },
	{ 0x06000175, Rect_Equals_mD7EB2046512E4A46524A7ED929F1C38A32C408F8_AdjustorThunk },
	{ 0x06000176, Rect_Equals_mE725DE506D3F1DB92F58B876BDA42AACD4D991B5_AdjustorThunk },
	{ 0x06000177, Rect_ToString_m7BF74645745862DA4751965D0899F94376F77F10_AdjustorThunk },
	{ 0x06000178, Rect_ToString_mA9EB8EC6A2E940240E0D0ECFA103B9B7EFB3D532_AdjustorThunk },
	{ 0x06000179, RectInt_get_x_mA1E7EF6DEAD2E900D7D56B7A3957C05081EBA9CA_AdjustorThunk },
	{ 0x0600017A, RectInt_set_x_m2D2F3A87E9899A29444DBDD0BB11CB19F13AA075_AdjustorThunk },
	{ 0x0600017B, RectInt_get_y_m440422264E6FCAA91E01F81486A78037AC29D878_AdjustorThunk },
	{ 0x0600017C, RectInt_set_y_m45D5C1D817698266BED66D41A705956A1571858D_AdjustorThunk },
	{ 0x0600017D, RectInt_get_width_m6B7B2FB764EAE83B7F63E7F77FA33973606761A7_AdjustorThunk },
	{ 0x0600017E, RectInt_set_width_mCD96AA9D096114147F8411A340CE4AD3476DCD4C_AdjustorThunk },
	{ 0x0600017F, RectInt_get_height_mE25FB938714942D7A3BA0B3C21BC5CB913D5709C_AdjustorThunk },
	{ 0x06000180, RectInt_set_height_m823A353A80B8B5180AEDF968A6E85B9D9B75C1C6_AdjustorThunk },
	{ 0x06000181, RectInt_get_xMin_mA5FB5AF1133380E080CF750D21327DE27EADEE1B_AdjustorThunk },
	{ 0x06000182, RectInt_get_yMin_m6914C2254158DF797E20E381626DC08A2700147B_AdjustorThunk },
	{ 0x06000183, RectInt_get_xMax_mBA05CE52847E3D3CB8295055706B3E0D4350E9F0_AdjustorThunk },
	{ 0x06000184, RectInt_get_yMax_mAE5D758A1241F7722F8FB9B46861583F76C8FE44_AdjustorThunk },
	{ 0x06000185, RectInt__ctor_m6E8B3A6C7EE11257A6B438E36274116FE39B5B42_AdjustorThunk },
	{ 0x06000186, RectInt_Overlaps_m9E82E1C8BFDA3297221E5FDC8B8773AD3F50C4EE_AdjustorThunk },
	{ 0x06000187, RectInt_ToString_m7EC8BB4830459B8CF5BF3032E9A526A6EE18D386_AdjustorThunk },
	{ 0x06000188, RectInt_ToString_m7EAE8CA8D77B7D6DDD46B61D670C71046006B92F_AdjustorThunk },
	{ 0x06000189, RectInt_Equals_mE9EA164664CA30C1C099EFB658D691F55A793B96_AdjustorThunk },
	{ 0x060001AD, RefreshRate_get_value_m7F8BB0D20DAB1EF882F1FC97E0C7618FCD319561_AdjustorThunk },
	{ 0x060001AE, RefreshRate_Equals_m16184432DA438F6BAF730078987262C7DE97913C_AdjustorThunk },
	{ 0x060001AF, RefreshRate_CompareTo_mD74AD821FF0DA633F9719E2B824C84E77E443D66_AdjustorThunk },
	{ 0x060001B0, RefreshRate_ToString_m59B00D8F20B6DAB001CD394F23653AC693DF8047_AdjustorThunk },
	{ 0x060001D7, Resolution_ToString_m058CE120CC83F314D0C8D4A706F9AA068BC9CF34_AdjustorThunk },
	{ 0x0600032C, RenderTextureDescriptor_get_width_mB159E4EB08B23B19CCCFADB465864361FB840BFF_AdjustorThunk },
	{ 0x0600032D, RenderTextureDescriptor_set_width_m3B2494007BFE3AD4D14403407C9B24F5045E7E10_AdjustorThunk },
	{ 0x0600032E, RenderTextureDescriptor_get_height_m1006F9AA45029715C552C8A8C2F102F63D3A91EC_AdjustorThunk },
	{ 0x0600032F, RenderTextureDescriptor_set_height_m1FE41111472DAA9B5E80FFAF3445004D72A3CFA5_AdjustorThunk },
	{ 0x06000330, RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_AdjustorThunk },
	{ 0x06000331, RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4_AdjustorThunk },
	{ 0x06000332, RenderTextureDescriptor_get_volumeDepth_m528818299E101F1B285B08BE12FAC2F9A871BA36_AdjustorThunk },
	{ 0x06000333, RenderTextureDescriptor_set_volumeDepth_mEF9610D1C14182417A01B7243DEE6B559A13B34D_AdjustorThunk },
	{ 0x06000334, RenderTextureDescriptor_set_mipCount_mDCC85ED7D97BD64A290A21DB91BC5CB1C4BA95EF_AdjustorThunk },
	{ 0x06000335, RenderTextureDescriptor_get_graphicsFormat_m50F25A4F179EA318C8D3B0D8685F9C5F59F7DEC0_AdjustorThunk },
	{ 0x06000336, RenderTextureDescriptor_set_graphicsFormat_m037DA25F9A8B956D830C7B7E5C6E258DC1133A13_AdjustorThunk },
	{ 0x06000337, RenderTextureDescriptor_get_depthStencilFormat_m360929BE5BD10E9C3D8C936AA6B44B1D11C119CB_AdjustorThunk },
	{ 0x06000338, RenderTextureDescriptor_set_depthStencilFormat_m7EC335006743B59AAD1895AD06B2DD74333F9A03_AdjustorThunk },
	{ 0x06000339, RenderTextureDescriptor_get_depthBufferBits_mC095E36F9803B2E68E258C03E48ACD0B0E678953_AdjustorThunk },
	{ 0x0600033A, RenderTextureDescriptor_set_depthBufferBits_mA3710C0D6E485BA6465B328CD8B1954F0E4C5819_AdjustorThunk },
	{ 0x0600033B, RenderTextureDescriptor_get_dimension_mA23ABB2CA03249DCE3A21F5123524A825C33E31B_AdjustorThunk },
	{ 0x0600033C, RenderTextureDescriptor_set_dimension_mCE9A4A08454BB2D9DFE3E505EC336FD480078F39_AdjustorThunk },
	{ 0x0600033D, RenderTextureDescriptor_set_shadowSamplingMode_m4B4CE918DFFF1CC5E3AF981456E186F15FC5DB93_AdjustorThunk },
	{ 0x0600033E, RenderTextureDescriptor_set_vrUsage_m994CB3D4B250A70BE005D9FDFD24D868E07A52F0_AdjustorThunk },
	{ 0x0600033F, RenderTextureDescriptor_set_memoryless_m9ECE149930C0E2629A5CD9DA1CD0EA2A01FFE1B2_AdjustorThunk },
	{ 0x06000340, RenderTextureDescriptor__ctor_m8B0D32DC550540B5546891C2F6300F384D6FE692_AdjustorThunk },
	{ 0x06000341, RenderTextureDescriptor__ctor_m8F8897C63F614AEA4348A95293C911C1293DA3A4_AdjustorThunk },
	{ 0x06000342, RenderTextureDescriptor_SetOrClearRenderTextureCreationFlag_m4C08C7A3F715426EBECA2B983361908D097C6424_AdjustorThunk },
	{ 0x06000343, RenderTextureDescriptor_set_createdFromScript_mEE28DED1D3C20DA025A0C44E1C2A531685194F23_AdjustorThunk },
	{ 0x06000344, RenderTextureDescriptor_set_useDynamicScale_m9335866C8754D51235D391E84F8972C4C518844A_AdjustorThunk },
	{ 0x06000345, Hash128__ctor_m0B61E717B3FF7D7BBD8FF12C8C8327C18A2AAAF3_AdjustorThunk },
	{ 0x06000346, Hash128_CompareTo_mFFE3648A372A7F3202897B266E14A6E2521BFE0C_AdjustorThunk },
	{ 0x06000347, Hash128_ToString_m35513B62830C8CE59346AF87AC2B587FA1570DCE_AdjustorThunk },
	{ 0x0600034A, Hash128_Equals_m28FADCC2F9A565AF152A53BCEEF88F798716B104_AdjustorThunk },
	{ 0x0600034B, Hash128_Equals_mF6BED87E0744D6DFFF8026614BDA8F4E6712063D_AdjustorThunk },
	{ 0x0600034C, Hash128_GetHashCode_m22816EE33CD973D11CD1917DEF7A0E0EC229E1D8_AdjustorThunk },
	{ 0x0600034D, Hash128_CompareTo_m1D249BA0DD01AF06E5B96B186E8A5F181BB30758_AdjustorThunk },
	{ 0x0600036E, Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_AdjustorThunk },
	{ 0x0600036F, Color__ctor_mCD6889CDE39F18704CD6EA8E2EFBFA48BA3E13B0_AdjustorThunk },
	{ 0x06000370, Color_ToString_m0018DE2184B3377CCA6FBD72D5D47886DC669147_AdjustorThunk },
	{ 0x06000371, Color_ToString_m70AEF3634C556F6AA01FC3236226C3D27C277229_AdjustorThunk },
	{ 0x06000372, Color_GetHashCode_m2981EEA1DEFE55254945D7D03BE64D4F56BA58D0_AdjustorThunk },
	{ 0x06000373, Color_Equals_m24E409BF329F25774C6577F18EFD3DE1253684D6_AdjustorThunk },
	{ 0x06000374, Color_Equals_mD297CAFFEBE9352C940873862FDF9A28F1F02435_AdjustorThunk },
	{ 0x0600037B, Color_RGBMultiplied_m4B3BAE4310EA98451D608E0300331012AFFF1B01_AdjustorThunk },
	{ 0x06000383, Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_AdjustorThunk },
	{ 0x06000384, Color_get_maxColorComponent_m97D2940D48767ACC21D76F8CCEAD6898B722529C_AdjustorThunk },
	{ 0x06000387, Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_AdjustorThunk },
	{ 0x0600038A, Color32_ToString_mB1EFBF981F901A76ABF2FEA19EB290A2D8CAFC32_AdjustorThunk },
	{ 0x0600038B, Color32_ToString_m263D34787852D176627FC2B910DFE9CABAF26696_AdjustorThunk },
	{ 0x06000394, Matrix4x4_GetLossyScale_m3C19D2C6746BB211C8CB02478A60EB2D71D10FC7_AdjustorThunk },
	{ 0x06000395, Matrix4x4_get_lossyScale_mFB3D4CF6408D710D607CA1D2AF94B2E4E0B57EB7_AdjustorThunk },
	{ 0x06000399, Matrix4x4_get_inverse_m4F4A881CD789281EA90EB68CFD39F36C8A81E6BD_AdjustorThunk },
	{ 0x0600039A, Matrix4x4__ctor_m6523044D700F15EC6BCD183633A329EE56AA8C99_AdjustorThunk },
	{ 0x0600039B, Matrix4x4_GetHashCode_m313B1AF4FEA792BB7E4D1D239EBE3257F825914D_AdjustorThunk },
	{ 0x0600039C, Matrix4x4_Equals_m35CFC5F304BB40EFFE011B92AA87B77CD8FF8F74_AdjustorThunk },
	{ 0x0600039D, Matrix4x4_Equals_mDB0C4CCC58BE3E108F1A40BE8DBDCD62E284CC51_AdjustorThunk },
	{ 0x0600039F, Matrix4x4_GetColumn_m5CE079D7A69DE70E3144BADD20A1651C73A8D118_AdjustorThunk },
	{ 0x060003A0, Matrix4x4_GetRow_m59C6981300C6F6927BEA17C5D095B2AD29629E9F_AdjustorThunk },
	{ 0x060003A1, Matrix4x4_MultiplyPoint_m20E910B65693559BFDE99382472D8DD02C862E7E_AdjustorThunk },
	{ 0x060003A2, Matrix4x4_MultiplyPoint3x4_mACCBD70AFA82C63DA88555780B7B6B01281AB814_AdjustorThunk },
	{ 0x060003A5, Matrix4x4_ToString_mB310BE20B7CDE8AEA91D84FCA4E75BCACF7DFC86_AdjustorThunk },
	{ 0x060003A6, Matrix4x4_ToString_mB8E1EFF14A37605ABA321708CC36021FFBCF84CF_AdjustorThunk },
	{ 0x060003AE, Vector3_get_Item_m163510BFC2F7BFAD1B601DC9F3606B799CF199F2_AdjustorThunk },
	{ 0x060003AF, Vector3_set_Item_m79136861DEC5862CE7EC20AB3B0EF10A3957CEC3_AdjustorThunk },
	{ 0x060003B0, Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_AdjustorThunk },
	{ 0x060003B1, Vector3__ctor_m5F87930F9B0828E5652E2D9D01ED907C01122C86_AdjustorThunk },
	{ 0x060003B3, Vector3_GetHashCode_mB08429DC931A85BD29CE11B9ABC77DE7E0E46327_AdjustorThunk },
	{ 0x060003B4, Vector3_Equals_mB4BE43D5986864F5C22B919F2957E0309F10E3B4_AdjustorThunk },
	{ 0x060003B5, Vector3_Equals_mEDEAF86793D229455BBF9BA5B30DDF438D6CABC1_AdjustorThunk },
	{ 0x060003B7, Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_AdjustorThunk },
	{ 0x060003BA, Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_AdjustorThunk },
	{ 0x060003CD, Vector3_ToString_m6C24B9F0382D25D75B05C606E127CD14660574EE_AdjustorThunk },
	{ 0x060003CE, Vector3_ToString_mA8DA39B6324392BB93203A4D4CB85AF87231CB62_AdjustorThunk },
	{ 0x060003D3, Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_AdjustorThunk },
	{ 0x060003DC, Quaternion_GetHashCode_m5F55C34C98E437376595E722BE4EB8A70434F049_AdjustorThunk },
	{ 0x060003DD, Quaternion_Equals_mCF93B00BA4FCDDE6100918451343DB9A0583A0A0_AdjustorThunk },
	{ 0x060003DE, Quaternion_Equals_m25B95D8412B79CC7F8B34062BFAE662BD99578BE_AdjustorThunk },
	{ 0x060003DF, Quaternion_ToString_mC5BD5DEF60FCA4A38924462A5C4440ECFCF934C4_AdjustorThunk },
	{ 0x060003E0, Quaternion_ToString_m9B592D577B3FDB892CA53ABF3457BC2EDE45DF8C_AdjustorThunk },
	{ 0x06000411, Vector2_get_Item_m18BC65BB0512B16A1F9C8BE4B83A3E7BBAD7064D_AdjustorThunk },
	{ 0x06000412, Vector2_set_Item_mEF309880B9B3B370B542AABEB32256EEC589DD03_AdjustorThunk },
	{ 0x06000413, Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_AdjustorThunk },
	{ 0x06000417, Vector2_ToString_mB47B29ECB21FA3A4ACEABEFA18077A5A6BBCCB27_AdjustorThunk },
	{ 0x06000418, Vector2_ToString_mC10F098442E56919947154402A77EDE28DC9B7BE_AdjustorThunk },
	{ 0x06000419, Vector2_GetHashCode_mED8670C0E122B7ED0DAB4C3381ADFF04B75E0B03_AdjustorThunk },
	{ 0x0600041A, Vector2_Equals_mA4E81D6FCE503DBD502BA499708344410F60DA4E_AdjustorThunk },
	{ 0x0600041B, Vector2_Equals_mDF84D5ED14E018609C6A9C9BAE016C1B33BCFF4C_AdjustorThunk },
	{ 0x0600041D, Vector2_get_magnitude_m5C59B4056420AEFDB291AD0914A3F675330A75CE_AdjustorThunk },
	{ 0x0600041E, Vector2_get_sqrMagnitude_mA16336720C14EEF8BA9B55AE33B98C9EE2082BDC_AdjustorThunk },
	{ 0x06000435, Vector2Int_get_x_mA2CACB1B6E6B5AD0CCC32B2CD2EDCE3ECEB50576_AdjustorThunk },
	{ 0x06000436, Vector2Int_set_x_m291ECF246536852F0B8EE049C4A3768E4999CDC8_AdjustorThunk },
	{ 0x06000437, Vector2Int_get_y_m48454163ECF0B463FB5A16A0C4FC4B14DB0768B3_AdjustorThunk },
	{ 0x06000438, Vector2Int_set_y_mF81881204EEE272BA409728C7EBFDE3A979DDF6A_AdjustorThunk },
	{ 0x06000439, Vector2Int__ctor_mC20D1312133EB8CB63EC11067088B043660F11CE_AdjustorThunk },
	{ 0x0600043D, Vector2Int_Equals_m6D91EFAA6B3254334436BD262A4547EA08281BA3_AdjustorThunk },
	{ 0x0600043E, Vector2Int_Equals_m32811BA0576C096D5EB5C0CFD8231478F17229A6_AdjustorThunk },
	{ 0x0600043F, Vector2Int_GetHashCode_mA3B6135FA770AF0C171319B50D9B913657230EB7_AdjustorThunk },
	{ 0x06000440, Vector2Int_ToString_m6F7E9B9B45A473FED501EB8B8B25BA1FE26DD5D4_AdjustorThunk },
	{ 0x06000441, Vector2Int_ToString_m44BA6941AEF41076A39848B95DDEFEA88A094B5E_AdjustorThunk },
	{ 0x06000443, Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_AdjustorThunk },
	{ 0x06000444, Vector3Int_set_x_m8745C5976D035EBBAC6F6191B5838D58631D8685_AdjustorThunk },
	{ 0x06000445, Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_AdjustorThunk },
	{ 0x06000446, Vector3Int_set_y_mA856F32D1BF187BD4091DDF3C6872FD01F7D3377_AdjustorThunk },
	{ 0x06000447, Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_AdjustorThunk },
	{ 0x06000448, Vector3Int_set_z_m5782180F67C4257C505F124971985D99C3422F74_AdjustorThunk },
	{ 0x06000449, Vector3Int__ctor_mE06A86999D16FA579A7F2142B872AB7E3695C9E0_AdjustorThunk },
	{ 0x0600044B, Vector3Int_Equals_m419967067E76BF0381E4CD8FE14DF5ED46ACFB02_AdjustorThunk },
	{ 0x0600044C, Vector3Int_Equals_mE4D179C5001B77DE05E3E4BC39DC9F6AE441EBD8_AdjustorThunk },
	{ 0x0600044D, Vector3Int_GetHashCode_mFAA200CFE26F006BEE6F9A65AFD0AC8C49D730EA_AdjustorThunk },
	{ 0x0600044E, Vector3Int_ToString_m49EB16DEA24181270D65A0F4ED39B3E8A46DB539_AdjustorThunk },
	{ 0x0600044F, Vector3Int_ToString_m74C285E175F089CED3A53A678216CD15A0AD0067_AdjustorThunk },
	{ 0x06000451, Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_AdjustorThunk },
	{ 0x06000452, Vector4_set_Item_mF24782F861A16BB0436C2262FA916B4EE69998A6_AdjustorThunk },
	{ 0x06000453, Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_AdjustorThunk },
	{ 0x06000454, Vector4_GetHashCode_m53F6FCA56A0051C9D1AB41AA3EAA2C86CDAA8B92_AdjustorThunk },
	{ 0x06000455, Vector4_Equals_mC2596CA0F441D25DE7A9419BE66A8FA2DA355CB9_AdjustorThunk },
	{ 0x06000456, Vector4_Equals_m73FAA65A1A565EE28D6C9385603829894B7D4392_AdjustorThunk },
	{ 0x06000458, Vector4_get_sqrMagnitude_m864A2908BCF9E060BA73DE3DD259EC06F47F913C_AdjustorThunk },
	{ 0x06000462, Vector4_ToString_mFA0DDF34C1E394F75EF65E06764A1BE750E7F388_AdjustorThunk },
	{ 0x06000463, Vector4_ToString_m2BE67BEBBD3059C9CEE29BF34AD30E1D7057E914_AdjustorThunk },
	{ 0x06000465, TypeDispatchData_Dispose_m5E70993B62AFC8BB19163A8D7711BD3B5CC9424C_AdjustorThunk },
	{ 0x06000466, TransformDispatchData_Dispose_m1C2E3B8C249D6E8785C19CC17C7ACBA4167A31BF_AdjustorThunk },
	{ 0x06000497, PropertyName__ctor_mFA341118B63F88B69464A6F1DF248B569686D778_AdjustorThunk },
	{ 0x06000498, PropertyName__ctor_m9F28B1DA9CEDDEAA6663C795456B9F5E95CAE92F_AdjustorThunk },
	{ 0x0600049B, PropertyName_GetHashCode_m7D4B878BA78625CDD78C9B045B5EADFDBF94C378_AdjustorThunk },
	{ 0x0600049C, PropertyName_Equals_mFD87005C352B7BEB4279560B1A489409B0692143_AdjustorThunk },
	{ 0x0600049D, PropertyName_Equals_m7D00F3B148E65210A82101C31F1F217C9527113E_AdjustorThunk },
	{ 0x0600049F, PropertyName_ToString_mDE271855F7B9A516185A66E12D90FE6B6C9DF6C0_AdjustorThunk },
	{ 0x060004CB, AssemblyVersion_ToString_mEB86A8E88D80F7C7459258EDF519B208400FA74B_AdjustorThunk },
	{ 0x060004CC, AssemblyVersion_Equals_m9FA3C12B7C0743C563E712CD08672EF9E99E1ADD_AdjustorThunk },
	{ 0x060004CD, AssemblyVersion_GetHashCode_mC191CC77C4664CD9A3F59E18BEE76E6BB5DA336F_AdjustorThunk },
	{ 0x060004CE, AssemblyFullName_Equals_mCCD71330EA2201DC5C3452AEEFF051AF203D0E9E_AdjustorThunk },
	{ 0x060004CF, AssemblyFullName_GetHashCode_m470DD9F4F78B21172D3A438C139ABF5587B0C4D0_AdjustorThunk },
	{ 0x060004D0, AssemblyFullName_ToString_m4D31288544EE855F460B3833AB0D51DF0AEADCEF_AdjustorThunk },
	{ 0x06000550, RangeInt_get_end_m5835FBEB410CB8AC0928AEFD95728A9AD57F6C63_AdjustorThunk },
	{ 0x06000551, RangeInt__ctor_m3CB91E79C7B5AED97E564581025B2F66778B7CBE_AdjustorThunk },
	{ 0x06000597, WorkRequest__ctor_m78DC33ED88BF9BA29E05D2180B2ADC439132F1F5_AdjustorThunk },
	{ 0x06000598, WorkRequest_Invoke_mBFEC6A3799BAFDE7ED840A0523D3D56160C03921_AdjustorThunk },
	{ 0x060005D8, DrivenRectTransformTracker_Add_mC0CE417831BF58E6DA81770CE5E2A99B142EEFEC_AdjustorThunk },
	{ 0x060005D9, DrivenRectTransformTracker_Clear_m9A7F5130E4007F70B14AB1FF13A2997C073A64EE_AdjustorThunk },
	{ 0x060006C0, MovedFromAttributeData_Set_m155005FB8BCE0569C40B02D75FFADB3FBDB7EEDD_AdjustorThunk },
	{ 0x060006C3, Scene_get_handle_mD508BE60333C6168610E12CECAB12E9B11C25E53_AdjustorThunk },
	{ 0x060006C4, Scene_GetHashCode_m74ACBFB8C656D5620A9A1E62D04E55ACBF63ADC9_AdjustorThunk },
	{ 0x060006C5, Scene_Equals_mB973481492F291BF8EAF1AD66B9F7FA3ACF3928D_AdjustorThunk },
	{ 0x060006D0, PlayerLoopSystem_ToString_m259B8533D2C64C15D381B16F32C710A0018684A0_AdjustorThunk },
	{ 0x06000717, VertexAttributeDescriptor_get_attribute_m2F7C7084857741A39E9FF0D4BABF98F9C7018AAA_AdjustorThunk },
	{ 0x06000718, VertexAttributeDescriptor_set_attribute_m526ED099EEA12DCB2FCB1BC88445FBF921E850A2_AdjustorThunk },
	{ 0x06000719, VertexAttributeDescriptor_get_format_mF8EF98F3E047BC26D177C3A008BCDAF979E52C79_AdjustorThunk },
	{ 0x0600071A, VertexAttributeDescriptor_set_format_mCA82263EBD7802621D9ECE42D0F44D4AAECD46C5_AdjustorThunk },
	{ 0x0600071B, VertexAttributeDescriptor_get_dimension_mAB440DDFA08BF61D717547EC8B9C43C3DFC56FB8_AdjustorThunk },
	{ 0x0600071C, VertexAttributeDescriptor_set_dimension_mEF4325AE27221C732B214354A2DEB044ACFC69EE_AdjustorThunk },
	{ 0x0600071D, VertexAttributeDescriptor_get_stream_mAA4D41E2B4B9024161B176700100ADB27855E4FF_AdjustorThunk },
	{ 0x0600071E, VertexAttributeDescriptor_set_stream_m9A3991AA0365A1DCEDF80A61990F7B48913A1C0A_AdjustorThunk },
	{ 0x0600071F, VertexAttributeDescriptor__ctor_m713B31395FB13FDEB2665F5C4C31572D5875A43A_AdjustorThunk },
	{ 0x06000720, VertexAttributeDescriptor_ToString_m45F7E7D6715E000C617173246F2C9BE23DF3FC8F_AdjustorThunk },
	{ 0x06000721, VertexAttributeDescriptor_GetHashCode_mCB8C5F6AD422FE6A410C5406843FCE4B0ED5F2F8_AdjustorThunk },
	{ 0x06000722, VertexAttributeDescriptor_Equals_m6FBFEE42E1BAAAC2D38434EDF0906C5B76D56BE5_AdjustorThunk },
	{ 0x06000723, VertexAttributeDescriptor_Equals_mF328DE139864C201987238048AC79F8925CA435D_AdjustorThunk },
	{ 0x06000728, BatchID_GetHashCode_m3F4DFD3D722865BBC47F74C5E452E985B6BBBA3A_AdjustorThunk },
	{ 0x06000729, BatchID_Equals_m953C8EE9ABF083CFAA3D7210F1085C9FD7EED71D_AdjustorThunk },
	{ 0x0600072A, BatchID_Equals_m4207BA1B44B5BF1DA2847571B6D9A981F5AE3CFF_AdjustorThunk },
	{ 0x0600072C, BatchMaterialID_GetHashCode_mE28980612614E66848ADABB7825CA73FFA39DD09_AdjustorThunk },
	{ 0x0600072D, BatchMaterialID_Equals_m776A152CA1957E92FCD24B4E2EDAF8A6D78E953E_AdjustorThunk },
	{ 0x0600072E, BatchMaterialID_Equals_m073A3DECD4CDE73F0CC314024B89399982E5BA83_AdjustorThunk },
	{ 0x06000730, BatchMeshID_GetHashCode_m62EDD33030F375FAAD44A23FAD7901A2BE1D08A9_AdjustorThunk },
	{ 0x06000731, BatchMeshID_Equals_mA501F8E992E7B32BD8985DD68D5D0B360A104A42_AdjustorThunk },
	{ 0x06000732, BatchMeshID_Equals_m985986D473BB12E9F43C59B41846660884B15C06_AdjustorThunk },
	{ 0x06000734, BatchPackedCullingViewID_GetHashCode_m588865495A1A1F2AC79A62D76B2DDC91D743F4A5_AdjustorThunk },
	{ 0x06000735, BatchPackedCullingViewID_Equals_mD7B8D7EFB1678D80FC11B9FA1171988669CD0C7E_AdjustorThunk },
	{ 0x06000736, BatchPackedCullingViewID_Equals_m2E5BC66A5B8D6737BABE5D6A0019F901192D2A9F_AdjustorThunk },
	{ 0x06000737, BatchCullingContext__ctor_mB26CB3EE7FD392F94B9816FCA04D76B8AAB6FDF3_AdjustorThunk },
	{ 0x0600073B, LODParameters_Equals_mAD45601A9E881620B23A7922D8AA5AFBC91E0B9F_AdjustorThunk },
	{ 0x0600073C, LODParameters_Equals_mF956A067D82497A00AC8234322CD848E6FB7BADE_AdjustorThunk },
	{ 0x0600073D, LODParameters_GetHashCode_mECFCEAAAC935759A99C30C69BBC99A1148F46F40_AdjustorThunk },
	{ 0x0600076C, ScriptableRenderContext_GetCameras_Internal_m852016B3544E3ED5FEFB9695EC175622A5B6A8C8_AdjustorThunk },
	{ 0x0600076D, ScriptableRenderContext__ctor_m10159F14BB69F555C375E13BB77A1898FDB42FA5_AdjustorThunk },
	{ 0x0600076E, ScriptableRenderContext_GetCameras_m9B2329F79132EE49B719560AD739FD3601C44189_AdjustorThunk },
	{ 0x0600076F, ScriptableRenderContext_Equals_mBFDA5815F2B6ABA9B16503DA906B8BA42078718D_AdjustorThunk },
	{ 0x06000770, ScriptableRenderContext_Equals_m99E5A233945DFC3B9A786F2413ECE68E5019AB88_AdjustorThunk },
	{ 0x06000771, ScriptableRenderContext_GetHashCode_mA1EE09239F1ACFC29A2ADB027D5E76E690510207_AdjustorThunk },
	{ 0x06000774, ShaderTagId__ctor_m4191968F1D2CE19F9092253EC10F83734A9CFF5B_AdjustorThunk },
	{ 0x06000775, ShaderTagId_Equals_m02826F7AFC63AA3AE5DB14F7A891F8F173FD9A33_AdjustorThunk },
	{ 0x06000776, ShaderTagId_Equals_m932EFCC38C276EEB2784BBC866330F4C595F52E0_AdjustorThunk },
	{ 0x06000777, ShaderTagId_GetHashCode_mF5E3A1F96CBDFDCEFABE1B56125EBBA6E3B9EFEF_AdjustorThunk },
	{ 0x06000778, StencilState_set_enabled_m6DC861C699D1044E896E833D2DAE69B82F796564_AdjustorThunk },
	{ 0x06000779, StencilState_set_readMask_m1BA8F9033413889D4E77DA343DC0029566A9BB9B_AdjustorThunk },
	{ 0x0600077A, StencilState_set_writeMask_m94471C671E03D42F36DA61436B1068B362375D65_AdjustorThunk },
	{ 0x0600077B, StencilState_set_compareFunctionFront_m1388C37901DAB6AF9D23C0F01946DCCE19BC9BFC_AdjustorThunk },
	{ 0x0600077C, StencilState_set_passOperationFront_m1F15CC29366DAEAA6CCE1DB0622C70D6ECC5A3EB_AdjustorThunk },
	{ 0x0600077D, StencilState_set_failOperationFront_mADCECAE5D2E75ABAE51650F1F314E661D09C2CD6_AdjustorThunk },
	{ 0x0600077E, StencilState_set_zFailOperationFront_mC7D8F0A08B9AEC4203BD6B352CB795E8011EFBB6_AdjustorThunk },
	{ 0x0600077F, StencilState_set_compareFunctionBack_m8AF73F4E8FC95A46D33E3192C50702D2AA15D61D_AdjustorThunk },
	{ 0x06000780, StencilState_set_passOperationBack_m4B1395FE21F5B5C809DC6F31D5824A90E05ED220_AdjustorThunk },
	{ 0x06000781, StencilState_set_failOperationBack_mD279271DD1F99EE5B8BC19F5AE60988E6C6F0E4A_AdjustorThunk },
	{ 0x06000782, StencilState_set_zFailOperationBack_mC092ABD8A5EA87245640A10E54C6A1990C4F6864_AdjustorThunk },
	{ 0x06000783, StencilState_Equals_m9FFB8A41D8838FD128875CB2D4DAA760C6DF1051_AdjustorThunk },
	{ 0x06000784, StencilState_Equals_mEA45A5D2BF2223B15EE0FB8BCEDBA9CB534ADF4B_AdjustorThunk },
	{ 0x06000785, StencilState_GetHashCode_mB4A02DEE780377C853D16FFF49CCB9D9F4F711A5_AdjustorThunk },
	{ 0x060007AF, Playable__ctor_mD2EB35E024816AEED68795D0124EAB30E05BF6C4_AdjustorThunk },
	{ 0x060007B0, Playable_GetHandle_m39356D23E849DC5428B262092657662C064E04F8_AdjustorThunk },
	{ 0x060007B1, Playable_Equals_mD72D3DB892B8867A0E7BAC032A16C08616EEFF86_AdjustorThunk },
	{ 0x060007C9, PlayableHandle_Equals_m60AD76B7D38CA989AE84501B2E9F9ED5CB5F9670_AdjustorThunk },
	{ 0x060007CA, PlayableHandle_Equals_m81BA0E127133DFF3E45DA61D185FDF48E16BCF45_AdjustorThunk },
	{ 0x060007CB, PlayableHandle_GetHashCode_m10FB32ECDC0B9D7BDAEA9E3B76BDDF4614F4EF4F_AdjustorThunk },
	{ 0x060007CD, PlayableHandle_IsValid_m07631D12846BAAF2CC302E69A28A44BFE9EB5098_AdjustorThunk },
	{ 0x060007CE, PlayableHandle_GetPlayableType_mD9750F1B85DF086F52641D6AB85789601486B686_AdjustorThunk },
	{ 0x060007D2, PlayableOutput__ctor_m55FBB20EC479F67641835EA48D84A1AB3DF39747_AdjustorThunk },
	{ 0x060007D3, PlayableOutput_GetHandle_m12FF2889D15884CBEB92A6CB376827EBA5A607BF_AdjustorThunk },
	{ 0x060007D4, PlayableOutput_Equals_m4CC730818751114DC5643600B5FE20243F4B7121_AdjustorThunk },
	{ 0x060007D7, PlayableOutputHandle_GetHashCode_mC2FAF756D71026E3AF4492157EDAE7186429B079_AdjustorThunk },
	{ 0x060007D9, PlayableOutputHandle_Equals_mB9106CB9333E0BF4C893E43AD7A23B64471CC21A_AdjustorThunk },
	{ 0x060007DA, PlayableOutputHandle_Equals_mF5C23882B2A007186F00EB3D23E9BD6664E8DAE6_AdjustorThunk },
	{ 0x060007DD, LinearColor_get_red_m376617B8E3156420835055189BB28D953FE46A2A_AdjustorThunk },
	{ 0x060007DE, LinearColor_set_red_m0ACFCEDDD205A6F235BE95936816E92898B01B52_AdjustorThunk },
	{ 0x060007DF, LinearColor_get_green_mCCE90A662234EE3605368F3AEC14E51572665AE5_AdjustorThunk },
	{ 0x060007E0, LinearColor_set_green_mBD9C7EA6415DC54B3F6B643C3CD02B71565F0694_AdjustorThunk },
	{ 0x060007E1, LinearColor_get_blue_mAFAEA5D5590DD14CFC48BC18DF4BFEBBDCB0A99A_AdjustorThunk },
	{ 0x060007E2, LinearColor_set_blue_m3FEEAF946772BB177733B67D9DA4B72D84874375_AdjustorThunk },
	{ 0x060007E5, LightDataGI_Init_m112DEBB76EC57AC52E6384C97A3E8B2EAA867207_AdjustorThunk },
	{ 0x060007E6, LightDataGI_Init_mACE06E00CC639CA89F3847E9DB55FD0F00812A7A_AdjustorThunk },
	{ 0x060007E7, LightDataGI_Init_m0A999D118CDCBDA99B9E24231ED057D943C9C67B_AdjustorThunk },
	{ 0x060007E8, LightDataGI_Init_mDC887CA8191C6CADE1DB585D7FEB46B080B25038_AdjustorThunk },
	{ 0x060007E9, LightDataGI_Init_mB2D1C73EDFEA6815E39A0FE3ED2F7BF9A7117632_AdjustorThunk },
	{ 0x060007EA, LightDataGI_InitNoBake_mBDF2EFB22D4BEE63B6F25F4EE9F1522D2866ED43_AdjustorThunk },
	{ 0x06000800, CameraPlayable_GetHandle_mA04469CA50B43AF6219F9967B8AEB310CB5455BD_AdjustorThunk },
	{ 0x06000801, CameraPlayable_Equals_mD0FA195F3EA6511043E8F0AA1680CEB7E0E2E2CF_AdjustorThunk },
	{ 0x06000802, MaterialEffectPlayable_GetHandle_m748319E116317E9ADD1EA36A4EDA488338471058_AdjustorThunk },
	{ 0x06000803, MaterialEffectPlayable_Equals_mC55640B5D29F90360F9743549FABD43C5AA320EC_AdjustorThunk },
	{ 0x06000804, TextureMixerPlayable_GetHandle_mB75CF651C6BDDF347ED6938D0F1DE4BED92BB7CD_AdjustorThunk },
	{ 0x06000805, TextureMixerPlayable_Equals_m6838329B39779020FC3309B7406B8A0418F44FE7_AdjustorThunk },
};
static const int32_t s_InvokerIndices[2109] = 
{
	4370,
	4370,
	6254,
	3533,
	3557,
	4272,
	4370,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4370,
	6254,
	6124,
	5953,
	5954,
	5533,
	2501,
	5236,
	3557,
	4744,
	5138,
	4676,
	4880,
	6254,
	4931,
	610,
	1914,
	4370,
	4370,
	4370,
	3632,
	4272,
	4272,
	6230,
	6075,
	3557,
	1936,
	4395,
	3535,
	4370,
	3370,
	3533,
	3533,
	3533,
	5215,
	6002,
	4881,
	4674,
	4556,
	6130,
	6130,
	4448,
	5546,
	5737,
	4272,
	3557,
	4272,
	4370,
	6224,
	5112,
	5113,
	5712,
	4503,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4370,
	4370,
	6254,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4732,
	0,
	5936,
	0,
	4370,
	4370,
	4370,
	4370,
	4370,
	4370,
	4370,
	0,
	0,
	0,
	0,
	0,
	4731,
	5685,
	5232,
	4548,
	5235,
	5686,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4370,
	5023,
	4370,
	4370,
	5918,
	6130,
	5949,
	2498,
	4370,
	4246,
	3557,
	4370,
	2521,
	2521,
	6204,
	6204,
	6204,
	6132,
	6217,
	6128,
	6204,
	4976,
	6204,
	6254,
	6254,
	6254,
	6254,
	6125,
	6132,
	6204,
	6254,
	1914,
	4370,
	1914,
	3471,
	1914,
	1016,
	3533,
	3533,
	5987,
	3535,
	4370,
	4310,
	4310,
	4310,
	4246,
	4246,
	4246,
	4290,
	4272,
	4246,
	1513,
	3260,
	3260,
	1485,
	1486,
	3149,
	6224,
	6224,
	6217,
	5922,
	6217,
	5922,
	6132,
	6132,
	6132,
	6132,
	3471,
	907,
	1530,
	907,
	1914,
	3557,
	5291,
	1914,
	3495,
	5716,
	6132,
	6254,
	4953,
	5721,
	636,
	319,
	1916,
	4370,
	6224,
	5097,
	6132,
	5721,
	4707,
	6132,
	5721,
	5721,
	5297,
	6132,
	5721,
	6132,
	5721,
	5721,
	5297,
	6125,
	5691,
	6132,
	5721,
	5402,
	6204,
	6254,
	0,
	0,
	4756,
	5988,
	5402,
	5561,
	5846,
	5846,
	5846,
	5846,
	5988,
	5988,
	5988,
	4774,
	0,
	6254,
	3557,
	0,
	2521,
	4246,
	4272,
	1970,
	4357,
	5651,
	6093,
	4370,
	629,
	6254,
	4370,
	2521,
	1977,
	4246,
	2521,
	2448,
	4361,
	3637,
	4361,
	3637,
	4361,
	3637,
	4361,
	4361,
	5370,
	5370,
	1977,
	3637,
	4272,
	1475,
	4362,
	3638,
	4362,
	3638,
	1978,
	4272,
	1475,
	2521,
	2449,
	4246,
	4361,
	1977,
	1185,
	4272,
	1475,
	1977,
	4361,
	4361,
	3259,
	4272,
	1475,
	712,
	1976,
	3573,
	6233,
	4916,
	4310,
	3588,
	4310,
	3588,
	4359,
	3635,
	4359,
	4359,
	3635,
	4359,
	3635,
	4310,
	3588,
	4310,
	3588,
	4359,
	3635,
	4310,
	3588,
	4310,
	3588,
	4310,
	3588,
	4310,
	3588,
	2609,
	2611,
	6009,
	2537,
	1187,
	5411,
	5411,
	4246,
	2521,
	2537,
	4272,
	1475,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	4246,
	4246,
	4246,
	4246,
	629,
	2538,
	4272,
	1475,
	2538,
	4370,
	1914,
	4370,
	4272,
	1475,
	4370,
	6219,
	6130,
	4246,
	4246,
	4246,
	4246,
	4246,
	4246,
	4370,
	6254,
	6254,
	6132,
	6132,
	4370,
	3535,
	4246,
	4246,
	4246,
	4246,
	6118,
	6224,
	6132,
	6254,
	5273,
	5273,
	4784,
	6254,
	1914,
	4370,
	4218,
	2539,
	2979,
	4272,
	6217,
	6217,
	6239,
	6204,
	6217,
	6254,
	4727,
	4727,
	4968,
	4968,
	6132,
	6254,
	4701,
	5306,
	5306,
	5734,
	4988,
	6126,
	6131,
	6131,
	6254,
	6254,
	6254,
	6131,
	4988,
	4988,
	6128,
	6254,
	4947,
	4947,
	6134,
	6124,
	6124,
	4946,
	6124,
	6254,
	6254,
	6254,
	4272,
	5697,
	6217,
	1778,
	969,
	6219,
	6130,
	3482,
	4370,
	969,
	4370,
	4370,
	4370,
	1778,
	1778,
	4272,
	4272,
	3557,
	4199,
	3482,
	3533,
	3482,
	4246,
	3533,
	4246,
	3533,
	4246,
	4246,
	4272,
	4272,
	3557,
	4370,
	5988,
	5922,
	5922,
	4370,
	5721,
	5721,
	6132,
	3557,
	3557,
	3557,
	4272,
	3557,
	4272,
	2945,
	2496,
	2521,
	3533,
	3557,
	3557,
	4246,
	839,
	1472,
	2496,
	3557,
	4272,
	3557,
	4272,
	3557,
	4246,
	1800,
	1725,
	1773,
	1778,
	3175,
	2675,
	3124,
	1920,
	1800,
	1725,
	1838,
	1773,
	1778,
	3176,
	3175,
	2675,
	3264,
	3126,
	3124,
	1716,
	1716,
	1716,
	4246,
	4310,
	4201,
	4310,
	4199,
	4310,
	4310,
	4310,
	4257,
	4246,
	4310,
	4272,
	3471,
	3471,
	4370,
	3557,
	4370,
	4370,
	6132,
	4370,
	1463,
	64,
	3533,
	2496,
	65,
	830,
	630,
	4199,
	4246,
	4246,
	4197,
	3480,
	3482,
	3533,
	4370,
	5918,
	5918,
	0,
	0,
	65,
	0,
	0,
	0,
	0,
	0,
	0,
	4272,
	3557,
	4272,
	3557,
	4272,
	3557,
	3557,
	3557,
	4272,
	3557,
	3557,
	998,
	676,
	3557,
	998,
	676,
	3557,
	998,
	676,
	3557,
	998,
	676,
	0,
	1778,
	635,
	321,
	0,
	1778,
	4370,
	1137,
	2496,
	2496,
	3557,
	3124,
	1463,
	962,
	66,
	1912,
	674,
	182,
	4370,
	4370,
	3533,
	4370,
	3471,
	3471,
	4370,
	4246,
	4246,
	4246,
	3533,
	4246,
	3533,
	4199,
	4246,
	4246,
	3533,
	4359,
	4246,
	4246,
	2910,
	2945,
	2496,
	1139,
	3126,
	4272,
	6254,
	3471,
	4246,
	6224,
	4451,
	4462,
	4199,
	1541,
	1139,
	315,
	384,
	367,
	3085,
	4358,
	1912,
	747,
	18,
	314,
	628,
	961,
	1281,
	0,
	1541,
	4370,
	1139,
	367,
	1139,
	367,
	1912,
	3557,
	313,
	320,
	4490,
	4505,
	4199,
	1139,
	1139,
	962,
	629,
	962,
	629,
	317,
	960,
	626,
	962,
	628,
	6128,
	4199,
	4450,
	4461,
	316,
	174,
	316,
	174,
	316,
	175,
	106,
	314,
	171,
	172,
	6128,
	4199,
	4467,
	4479,
	747,
	747,
	316,
	174,
	316,
	174,
	104,
	173,
	103,
	171,
	314,
	6128,
	4199,
	4489,
	4504,
	629,
	316,
	629,
	316,
	171,
	314,
	170,
	312,
	628,
	6128,
	4246,
	3533,
	4246,
	3533,
	3533,
	3533,
	3533,
	6224,
	6132,
	6224,
	6132,
	4293,
	4293,
	3533,
	4293,
	4293,
	3482,
	6132,
	3577,
	4295,
	5990,
	6132,
	4370,
	3577,
	3557,
	629,
	629,
	316,
	316,
	629,
	316,
	629,
	962,
	316,
	174,
	5504,
	5504,
	5504,
	5503,
	4295,
	6136,
	5918,
	5504,
	5504,
	5990,
	4471,
	4453,
	4472,
	4494,
	4525,
	4681,
	4893,
	3471,
	3471,
	3471,
	3471,
	5977,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	3533,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	3533,
	3533,
	3533,
	629,
	316,
	1542,
	3482,
	3482,
	1970,
	2941,
	4272,
	5898,
	5984,
	2521,
	2491,
	4246,
	2967,
	5385,
	5385,
	5385,
	5711,
	5977,
	5302,
	6217,
	5279,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3557,
	4272,
	3557,
	4199,
	3482,
	4246,
	3533,
	2496,
	5988,
	1778,
	970,
	1916,
	1916,
	970,
	636,
	712,
	1038,
	4272,
	1475,
	4246,
	2521,
	2455,
	5459,
	5460,
	5372,
	5372,
	5070,
	5070,
	2676,
	6206,
	6206,
	6206,
	6206,
	6206,
	6206,
	6206,
	4201,
	4310,
	6120,
	5860,
	612,
	5861,
	5857,
	4272,
	1475,
	6219,
	4370,
	2498,
	4370,
	4370,
	2521,
	2521,
	4246,
	4361,
	4361,
	5141,
	5398,
	5961,
	4267,
	722,
	4246,
	2521,
	2516,
	5540,
	3264,
	3264,
	3260,
	3260,
	5963,
	6222,
	4272,
	1475,
	6254,
	5683,
	4928,
	5353,
	5683,
	5226,
	5226,
	3175,
	1800,
	1038,
	1948,
	5676,
	4246,
	2521,
	2611,
	6118,
	4361,
	5628,
	6040,
	4310,
	5676,
	5676,
	6251,
	6251,
	6251,
	6251,
	6251,
	6251,
	6251,
	6251,
	5676,
	5676,
	6118,
	5675,
	5673,
	5675,
	5448,
	5448,
	4272,
	1475,
	6254,
	6004,
	6005,
	5604,
	712,
	6232,
	5672,
	5849,
	5409,
	5409,
	5623,
	5190,
	6005,
	4246,
	2521,
	2532,
	4272,
	1475,
	6254,
	5683,
	5683,
	5305,
	5843,
	5918,
	6035,
	5859,
	6035,
	6035,
	6035,
	6035,
	6035,
	5625,
	6035,
	6035,
	5918,
	5625,
	5504,
	5625,
	5504,
	5625,
	5625,
	6035,
	6035,
	6035,
	6035,
	5923,
	5923,
	5923,
	6035,
	5203,
	5105,
	6035,
	5203,
	5203,
	5414,
	4536,
	5625,
	5203,
	6028,
	5919,
	6087,
	5916,
	5479,
	5882,
	6254,
	5733,
	3175,
	1800,
	1948,
	5225,
	5225,
	5667,
	4272,
	1475,
	4246,
	2521,
	2609,
	5627,
	4310,
	4310,
	5667,
	5667,
	5667,
	5667,
	5667,
	5667,
	6111,
	5666,
	5662,
	5666,
	5446,
	5446,
	6113,
	6117,
	6249,
	6249,
	6249,
	6249,
	6249,
	6249,
	6249,
	6254,
	4246,
	3533,
	4246,
	3533,
	1756,
	6112,
	6115,
	5447,
	2521,
	2610,
	4246,
	4272,
	1475,
	6254,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	962,
	5449,
	2521,
	2612,
	4246,
	4272,
	1475,
	6254,
	3175,
	1800,
	712,
	4246,
	2521,
	2613,
	5629,
	4310,
	6253,
	5678,
	5678,
	5450,
	5450,
	6123,
	6119,
	6122,
	6114,
	4272,
	1475,
	6254,
	4370,
	4370,
	6254,
	6254,
	4370,
	187,
	69,
	5618,
	5626,
	5882,
	5081,
	5935,
	6105,
	5882,
	5131,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	924,
	739,
	4370,
	3528,
	3528,
	4370,
	4199,
	4370,
	6204,
	6254,
	6132,
	6132,
	5295,
	5056,
	6254,
	6254,
	4370,
	4370,
	3557,
	4370,
	3588,
	1948,
	3588,
	4370,
	1756,
	6003,
	5711,
	3557,
	3567,
	5847,
	5408,
	4246,
	2521,
	2531,
	6003,
	4272,
	5988,
	5569,
	6224,
	6224,
	4370,
	3126,
	1475,
	6254,
	0,
	5569,
	5569,
	0,
	5721,
	4370,
	5988,
	5988,
	5922,
	5922,
	5922,
	0,
	6254,
	4370,
	3557,
	3557,
	1912,
	4370,
	4370,
	4370,
	3557,
	3533,
	4246,
	4370,
	4199,
	3482,
	4199,
	4370,
	6254,
	6130,
	6254,
	6254,
	4370,
	1475,
	5365,
	4272,
	2521,
	4246,
	2521,
	4246,
	4272,
	4272,
	4272,
	3126,
	0,
	1169,
	1472,
	0,
	0,
	0,
	0,
	0,
	1472,
	0,
	0,
	0,
	0,
	1916,
	1916,
	0,
	0,
	4370,
	4370,
	4370,
	6130,
	5717,
	5174,
	0,
	4272,
	4199,
	4370,
	4370,
	5086,
	5711,
	5401,
	5988,
	5569,
	6254,
	5977,
	6254,
	4370,
	2967,
	3126,
	3126,
	2967,
	2967,
	4370,
	3126,
	4370,
	4370,
	0,
	3126,
	1472,
	0,
	0,
	1472,
	0,
	0,
	159,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	1169,
	3126,
	3126,
	3126,
	0,
	4272,
	4246,
	3533,
	3482,
	4199,
	4199,
	1016,
	3557,
	4370,
	1916,
	5721,
	4246,
	4246,
	5921,
	5955,
	6132,
	4714,
	4959,
	5717,
	4200,
	4370,
	4199,
	4370,
	1920,
	1026,
	3557,
	2521,
	3126,
	1475,
	3126,
	3126,
	3557,
	3557,
	3557,
	4370,
	4199,
	3482,
	6132,
	6132,
	5846,
	4981,
	5721,
	5402,
	5846,
	1475,
	3126,
	3557,
	3557,
	4272,
	4370,
	4370,
	0,
	0,
	5922,
	0,
	5716,
	5988,
	4246,
	1756,
	3533,
	3533,
	4370,
	5988,
	0,
	6132,
	5562,
	6224,
	6204,
	4370,
	6132,
	6224,
	5278,
	5988,
	6254,
	4370,
	3557,
	4370,
	3557,
	4272,
	5721,
	4272,
	4272,
	4370,
	1778,
	5988,
	6254,
	6254,
	6254,
	4370,
	1916,
	4246,
	4246,
	2521,
	5846,
	5402,
	5846,
	4248,
	4272,
	3557,
	0,
	5724,
	6132,
	5712,
	6132,
	4246,
	3533,
	5721,
	4272,
	5402,
	5402,
	6217,
	5988,
	5988,
	5988,
	5721,
	5985,
	4370,
	6254,
	3533,
	1912,
	1916,
	1916,
	4272,
	4370,
	4199,
	6254,
	6254,
	5844,
	1017,
	4370,
	4370,
	4310,
	3588,
	4199,
	3588,
	4370,
	4370,
	4370,
	0,
	0,
	2967,
	4370,
	4370,
	6217,
	5846,
	5843,
	6217,
	6217,
	6217,
	5843,
	6217,
	6217,
	5391,
	5504,
	5918,
	6239,
	6239,
	6239,
	6239,
	6130,
	4370,
	4370,
	71,
	5135,
	6204,
	6204,
	6204,
	6204,
	4473,
	4684,
	4272,
	3557,
	6125,
	4199,
	3482,
	4246,
	3533,
	4199,
	4199,
	4285,
	3569,
	5683,
	5697,
	5049,
	4760,
	4760,
	5049,
	4760,
	5049,
	4760,
	4760,
	4760,
	6254,
	1016,
	4370,
	6132,
	6132,
	4290,
	4359,
	3635,
	4359,
	3635,
	4359,
	3635,
	4359,
	3635,
	4359,
	3635,
	4359,
	3635,
	4359,
	3635,
	3557,
	3557,
	1800,
	6132,
	4359,
	4370,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	1914,
	3557,
	4370,
	4361,
	4361,
	3637,
	4361,
	4284,
	4284,
	3568,
	4361,
	3637,
	4272,
	3557,
	4272,
	3557,
	4272,
	3557,
	1906,
	4267,
	4267,
	3260,
	3260,
	4246,
	4370,
	4361,
	2521,
	3482,
	4272,
	3124,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	1530,
	1530,
	3471,
	3557,
	4272,
	4199,
	4370,
	4370,
	4370,
	4246,
	4246,
	4363,
	4363,
	4363,
	4197,
	4290,
	4363,
	4272,
	4310,
	4272,
	4359,
	4199,
	4246,
	4272,
	4272,
	4272,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	3471,
	4755,
	4755,
	6121,
	6121,
	6121,
	6110,
	5846,
	6132,
	6132,
	6132,
	6132,
	2521,
	5988,
	4272,
	4272,
	4246,
	4310,
	4272,
	4199,
	4370,
	4370,
	4370,
	4370,
	1916,
	0,
	0,
	5846,
	0,
	3557,
	3557,
	1916,
	3557,
	3557,
	4370,
	1174,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4272,
	4272,
	4272,
	4246,
	4272,
	4199,
	3126,
	5174,
	4370,
	4370,
	4370,
	4370,
	1916,
	3557,
	3557,
	1916,
	4370,
	4272,
	4370,
	4370,
	4370,
	4370,
	0,
	0,
	3126,
	585,
	4370,
	4370,
	3557,
	1916,
	4272,
	4272,
	5174,
	1914,
	4370,
	4370,
	3557,
	1475,
	1475,
	5988,
	4370,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3557,
	4370,
	619,
	619,
	3557,
	4246,
	4246,
	2521,
	6224,
	6224,
	4370,
	3120,
	6254,
	5978,
	5731,
	6137,
	5732,
	6254,
	4272,
	1914,
	4370,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6132,
	6254,
	4370,
	6224,
	4199,
	6224,
	4370,
	4272,
	1552,
	1552,
	3557,
	3557,
	3557,
	3557,
	1552,
	1131,
	1130,
	4370,
	4963,
	6128,
	6128,
	4370,
	4370,
	2521,
	4370,
	2521,
	4370,
	3557,
	924,
	3122,
	1552,
	4370,
	4370,
	4370,
	4240,
	3528,
	4370,
	4370,
	2521,
	4370,
	2521,
	4370,
	2521,
	3557,
	4272,
	2521,
	4246,
	4370,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	4246,
	3533,
	629,
	4272,
	4246,
	2521,
	2616,
	6204,
	6217,
	6124,
	6254,
	4246,
	2521,
	2444,
	6254,
	4246,
	2521,
	2445,
	6254,
	4246,
	2521,
	2446,
	6254,
	4246,
	2447,
	2521,
	3,
	4965,
	1914,
	572,
	2503,
	2521,
	4246,
	0,
	0,
	1945,
	1945,
	0,
	4199,
	3482,
	4370,
	3482,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	4272,
	0,
	4370,
	4370,
	4370,
	6224,
	6132,
	6254,
	5721,
	6132,
	6254,
	6224,
	5293,
	6132,
	6204,
	6254,
	1916,
	3535,
	3557,
	2549,
	2521,
	4246,
	6254,
	5260,
	3557,
	2521,
	2551,
	4246,
	3482,
	3482,
	3482,
	3533,
	3533,
	3533,
	3533,
	3533,
	3533,
	3533,
	3533,
	2554,
	2521,
	4246,
	6224,
	6132,
	4246,
	4246,
	4246,
	4246,
	4199,
	4199,
	4199,
	4199,
	4199,
	4199,
	6130,
	5843,
	5698,
	5843,
	5698,
	5698,
	5698,
	6130,
	6130,
	6130,
	6130,
	6130,
	4370,
	6254,
	6217,
	5985,
	4246,
	4246,
	4370,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6227,
	3564,
	4279,
	2525,
	6254,
	0,
	4218,
	4272,
	4983,
	5717,
	4370,
	4370,
	3562,
	3562,
	3562,
	3562,
	1934,
	1934,
	1934,
	1032,
	4272,
	6254,
	1914,
	1484,
	0,
	6228,
	5406,
	2521,
	2527,
	4246,
	5406,
	4199,
	4272,
	6254,
	5837,
	5977,
	3565,
	4280,
	2528,
	6254,
	6229,
	4246,
	5407,
	2521,
	2529,
	5407,
	6254,
	4310,
	3588,
	4310,
	3588,
	4310,
	3588,
	5537,
	6221,
	1530,
	1530,
	1530,
	1530,
	1530,
	3533,
	5843,
	5959,
	6033,
	5858,
	5692,
	5711,
	5711,
	5711,
	5711,
	5711,
	5711,
	6132,
	6224,
	6254,
	5291,
	6254,
	1914,
	1902,
	6254,
	4370,
	1902,
	4279,
	2451,
	4279,
	2513,
	4279,
	2590,
	4199,
	4370,
	3482,
	6204,
	6224,
	4370,
	0,
	6132,
	6224,
	6254,
	6254,
	4272,
	3557,
	3471,
	4370,
	5503,
	5503,
	5503,
	5503,
	5504,
	5918,
	5918,
	5918,
	5504,
	5843,
	5843,
	5843,
	5390,
	5843,
	5843,
	5843,
	5843,
	6254,
	5721,
	6125,
	5691,
	5691,
	0,
	0,
	0,
	5297,
	0,
	0,
	5721,
	0,
	0,
	5721,
	5697,
	6254,
	1916,
	4272,
	5988,
	5569,
	5172,
	5562,
	5978,
};
static const Il2CppTokenRangePair s_rgctxIndices[103] = 
{
	{ 0x0200000A, { 2, 10 } },
	{ 0x0200000E, { 19, 10 } },
	{ 0x02000037, { 29, 16 } },
	{ 0x02000038, { 45, 7 } },
	{ 0x0200003D, { 57, 21 } },
	{ 0x0200003E, { 78, 9 } },
	{ 0x02000041, { 87, 5 } },
	{ 0x02000044, { 95, 3 } },
	{ 0x02000146, { 235, 8 } },
	{ 0x02000147, { 243, 8 } },
	{ 0x02000148, { 251, 10 } },
	{ 0x02000149, { 261, 12 } },
	{ 0x0200014A, { 273, 5 } },
	{ 0x02000153, { 278, 10 } },
	{ 0x02000155, { 288, 5 } },
	{ 0x02000157, { 293, 10 } },
	{ 0x02000159, { 303, 7 } },
	{ 0x020001F0, { 310, 18 } },
	{ 0x020001F1, { 328, 7 } },
	{ 0x020001F4, { 335, 28 } },
	{ 0x020001F5, { 363, 4 } },
	{ 0x06000009, { 0, 2 } },
	{ 0x06000010, { 12, 3 } },
	{ 0x06000011, { 15, 4 } },
	{ 0x06000062, { 52, 3 } },
	{ 0x06000063, { 55, 2 } },
	{ 0x0600007C, { 92, 1 } },
	{ 0x0600007E, { 93, 2 } },
	{ 0x06000088, { 98, 1 } },
	{ 0x06000089, { 99, 1 } },
	{ 0x0600008A, { 100, 1 } },
	{ 0x0600008B, { 101, 1 } },
	{ 0x0600008C, { 102, 1 } },
	{ 0x06000093, { 103, 2 } },
	{ 0x06000094, { 105, 1 } },
	{ 0x06000095, { 106, 1 } },
	{ 0x06000096, { 107, 1 } },
	{ 0x06000097, { 108, 1 } },
	{ 0x06000098, { 109, 1 } },
	{ 0x06000099, { 110, 1 } },
	{ 0x0600009A, { 111, 2 } },
	{ 0x0600009B, { 113, 1 } },
	{ 0x0600009C, { 114, 3 } },
	{ 0x0600009D, { 117, 1 } },
	{ 0x0600009E, { 118, 1 } },
	{ 0x0600010A, { 119, 4 } },
	{ 0x0600010B, { 123, 13 } },
	{ 0x06000118, { 136, 3 } },
	{ 0x0600011B, { 139, 2 } },
	{ 0x0600024F, { 141, 2 } },
	{ 0x06000250, { 143, 2 } },
	{ 0x06000252, { 145, 1 } },
	{ 0x06000253, { 146, 1 } },
	{ 0x06000254, { 147, 2 } },
	{ 0x06000255, { 149, 2 } },
	{ 0x06000256, { 151, 2 } },
	{ 0x06000257, { 153, 3 } },
	{ 0x0600026E, { 156, 2 } },
	{ 0x06000272, { 158, 2 } },
	{ 0x060002AF, { 160, 3 } },
	{ 0x060004A8, { 163, 2 } },
	{ 0x060004AB, { 165, 2 } },
	{ 0x060004B3, { 167, 2 } },
	{ 0x060004D4, { 169, 2 } },
	{ 0x060004D7, { 171, 2 } },
	{ 0x060004D8, { 173, 2 } },
	{ 0x060004D9, { 175, 2 } },
	{ 0x060004DA, { 177, 2 } },
	{ 0x060004DB, { 179, 2 } },
	{ 0x060004DD, { 181, 2 } },
	{ 0x060004DE, { 183, 2 } },
	{ 0x060004DF, { 185, 2 } },
	{ 0x060004E0, { 187, 2 } },
	{ 0x060004E3, { 189, 2 } },
	{ 0x060004E4, { 191, 2 } },
	{ 0x06000502, { 193, 2 } },
	{ 0x06000505, { 195, 2 } },
	{ 0x06000506, { 197, 2 } },
	{ 0x06000508, { 199, 2 } },
	{ 0x06000509, { 201, 2 } },
	{ 0x0600050B, { 203, 2 } },
	{ 0x0600050C, { 205, 2 } },
	{ 0x0600050D, { 207, 2 } },
	{ 0x0600050E, { 209, 2 } },
	{ 0x0600050F, { 211, 2 } },
	{ 0x06000510, { 213, 2 } },
	{ 0x06000511, { 215, 3 } },
	{ 0x06000516, { 218, 2 } },
	{ 0x0600054A, { 220, 3 } },
	{ 0x0600054B, { 223, 5 } },
	{ 0x0600054D, { 228, 2 } },
	{ 0x06000556, { 230, 2 } },
	{ 0x0600057A, { 232, 1 } },
	{ 0x0600065E, { 233, 2 } },
	{ 0x06000742, { 367, 2 } },
	{ 0x060007C6, { 369, 1 } },
	{ 0x0600082B, { 370, 2 } },
	{ 0x0600082C, { 372, 6 } },
	{ 0x0600082D, { 378, 4 } },
	{ 0x0600082F, { 382, 2 } },
	{ 0x06000830, { 384, 2 } },
	{ 0x06000832, { 386, 2 } },
	{ 0x06000833, { 388, 2 } },
};
extern const uint32_t g_rgctx_JobStruct_1_Initialize_mD7A7D050A756996D6ABC4B3263CAB0DDA01E19B7;
extern const uint32_t g_rgctx_JobStruct_1_t500AD58020CA7EED545A3C9BDED26F9ADFD03CF3;
extern const uint32_t g_rgctx_JobStruct_1_t9B14DC02939321AF08B0D9861EC2DE39FB2699ED;
extern const uint32_t g_rgctx_JobStruct_1_t9B14DC02939321AF08B0D9861EC2DE39FB2699ED;
extern const uint32_t g_rgctx_T_tF4AF2A128E0752ED064F922D7181D6824613BB02;
extern const uint32_t g_rgctx_JobStruct_1_Execute_m45CF2DD91DFB334933AEB19608F139B97F00D9E6;
extern const uint32_t g_rgctx_ExecuteJobFunction_t4512BFE0B86D90D2328A29F4BCD80437EACDA954;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_mB59C9E8588B3AFEFC7979AF159ED46EAF96ED5E7;
extern const uint32_t g_rgctx_TU26_tE84B1A0A412D9CB22C4B7C045C3E0B39030378DF;
extern const uint32_t g_rgctx_T_tF4AF2A128E0752ED064F922D7181D6824613BB02;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tF4AF2A128E0752ED064F922D7181D6824613BB02_IJob_Execute_m6FB7F8EFAB486DA016EA24BB9BED238B4FC4DA27;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreate_TisJobStruct_1_t9B14DC02939321AF08B0D9861EC2DE39FB2699ED_m654AF35867A4B084668D2EC6E98335B0EC45D671;
extern const uint32_t g_rgctx_ParallelForJobStruct_1_Initialize_m0FCB39C6F34FE41917212319A2C84964CA7D6BD7;
extern const uint32_t g_rgctx_ParallelForJobStruct_1_tCEBFABBE9BEFEF19C14EA7871A076D630B0F0A74;
extern const uint32_t g_rgctx_ParallelForJobStruct_1_tCEBFABBE9BEFEF19C14EA7871A076D630B0F0A74;
extern const uint32_t g_rgctx_T_t6813F43536401836AB43EBD9D1A33BF765878EC3;
extern const uint32_t g_rgctx_UnsafeUtility_AddressOf_TisT_t6813F43536401836AB43EBD9D1A33BF765878EC3_m3F1DCD8B04BE305A251D5766807F32C2819145F2;
extern const uint32_t g_rgctx_TU26_tC51B37173FD5E08D4D8831ABF794D0055AD5CFA2;
extern const uint32_t g_rgctx_IJobParallelForExtensions_GetReflectionData_TisT_t6813F43536401836AB43EBD9D1A33BF765878EC3_m90BDC4DEF041FB0B7EF61B8EE2290A2680C62068;
extern const uint32_t g_rgctx_ParallelForJobStruct_1_tEF5B83079821A31EF124E43C06E887A9F3325A10;
extern const uint32_t g_rgctx_ParallelForJobStruct_1_tEF5B83079821A31EF124E43C06E887A9F3325A10;
extern const uint32_t g_rgctx_T_tFDC1689AE74A499B29AEBBF350042D7BEB5DF421;
extern const uint32_t g_rgctx_ParallelForJobStruct_1_Execute_mD697D00E6E20EA7358BF19B763AE8A8C806F0125;
extern const uint32_t g_rgctx_ExecuteJobFunction_tD105541081E8AB3594F9FCA208DFBC84B1426C32;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_m13591A35F12C3D8441B1DA0B8DF1983DF3835D8F;
extern const uint32_t g_rgctx_TU26_tD56ADC3413844E8641ED41194B152CD1674960E9;
extern const uint32_t g_rgctx_T_tFDC1689AE74A499B29AEBBF350042D7BEB5DF421;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tFDC1689AE74A499B29AEBBF350042D7BEB5DF421_IJobParallelFor_Execute_m424D27AF3C66D5013E807D6FFF6B8E5D75A3DE82;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreate_TisParallelForJobStruct_1_tEF5B83079821A31EF124E43C06E887A9F3325A10_mEEC197DEC91B74D426B4AF5F87D6BD7B279C1548;
extern const uint32_t g_rgctx_NativeArray_1_Allocate_mB5C60244102A554BEDF83E77F7D668AF650811CD;
extern const uint32_t g_rgctx_NativeArray_1_t9FED9BA6969776FB13D9111EB338E0273EE689F7;
extern const uint32_t g_rgctx_NativeArray_1U26_tAABA6D541CCA28E111FD53E86E1EB641D3AB1CCD;
extern const uint32_t g_rgctx_NativeArray_1_t9FED9BA6969776FB13D9111EB338E0273EE689F7;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m49CDDF2EC7C8480D9665F98A8D35F1810ACD3947;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_mB720AF5A26CED6F7D5F5AF9F1FF54C641A99D51F;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_m22C8155318CC4787B023DE2BED66C5B2518CA3D1;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_m83E61DA75E3A302EF6C903174839C6317AAC7FCC;
extern const uint32_t g_rgctx_T_t05490BB68E93F7FF07D46087FEC0242A3FB9D022;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_m1B320E8488A42E2C73BCBF01317A7F8C6CC67910;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m8F63985410A60584EDFCA5BA30D381AC413C0BF1;
extern const uint32_t g_rgctx_Enumerator_tE2EF59F915D6B7F2A37C947360340D614063ACE2;
extern const uint32_t g_rgctx_Enumerator__ctor_m0E321AD2EF64898A83F9AE834A7C248665FF10C7;
extern const uint32_t g_rgctx_IEnumerator_1_t94A04507167D65A615DB0CF0C3569EA01137BC6F;
extern const uint32_t g_rgctx_NativeArray_1_GetEnumerator_mA95B7D8B0D3419D5CBE4A7D8A2A2E2D57757AB0C;
extern const uint32_t g_rgctx_NativeArray_1_Equals_mA006DE1192EAB16EF588E028577FA5169BABD1C5;
extern const uint32_t g_rgctx_NativeArray_1U26_tD091D9C9F1D3DD99FB57510BE208E6C1D98C62CA;
extern const uint32_t g_rgctx_NativeArray_1_t99B5A119EEB30372BF581B474153FF6B44A9A1CB;
extern const uint32_t g_rgctx_Enumerator_tEAEB17A03872DDD2F7667E53C31FB03AAA037703;
extern const uint32_t g_rgctx_T_tCB2B4AD90EC47251952F0D7CF84CE2365DFB7790;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tCB2B4AD90EC47251952F0D7CF84CE2365DFB7790_m7E5E51AD821589237204CCC66826D358D7077F93;
extern const uint32_t g_rgctx_Enumerator_get_Current_m5D805336760473EF350CA58A4BB168EB1997843A;
extern const uint32_t g_rgctx_Enumerator_tEAEB17A03872DDD2F7667E53C31FB03AAA037703;
extern const uint32_t g_rgctx_NativeArray_1_t03DCB2073CE1DA7BC252E41A107FB90449A3DA58;
extern const uint32_t g_rgctx_NativeSlice_1_t5CC8186A30AF1437BD72DF09D324740D06B08141;
extern const uint32_t g_rgctx_NativeSlice_1__ctor_m2D1EE616B587F8DD7D0FD938E3851FA9D6801546;
extern const uint32_t g_rgctx_NativeSlice_1_t355D3C4F6F548D3CA491C8ADF86ABB455ADCB675;
extern const uint32_t g_rgctx_NativeSlice_1__ctor_mAB6470448A68D5DF06E02310782DA04F9306AC54;
extern const uint32_t g_rgctx_NativeSlice_1_tA97EC884413647283F01C67B3BC95ED41379C2F3;
extern const uint32_t g_rgctx_NativeArray_1_t050B3F6AF4DB5B2B490647C97C011F8C915BD960;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m33D38A2D95C8ABD59F4937B020B7E68CD6663B1A;
extern const uint32_t g_rgctx_NativeArray_1_t050B3F6AF4DB5B2B490647C97C011F8C915BD960;
extern const uint32_t g_rgctx_NativeSlice_1__ctor_mC7DE9713D4F8CB41CB3FD5BADF83BB8A16F7DDFF;
extern const uint32_t g_rgctx_NativeSlice_1_tA97EC884413647283F01C67B3BC95ED41379C2F3;
extern const uint32_t g_rgctx_NativeSlice_1__ctor_m0A772F5B6F5EA9FE33FC4CF88229B18484B5F5E5;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m033B67D65CABCADAB69FC0A790A42485E2000D03;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m3E9F8638EC263421E2FAF3BD5B7254E983A745F5;
extern const uint32_t g_rgctx_T_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElementWithStride_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m2335A90A9396C306C5F60C37F0C88EC5306DB4C5;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_GetUnsafePtr_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m5DFD0A3C0034A0D21F260D46DC8FBDBFB19EE765;
extern const uint32_t g_rgctx_NativeSlice_1_get_Stride_m79069541DC62D6377285836CD49CC0E3997649D0;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m02BA91BF441A8972D72D32DAE7A488DDDAD3D1E0;
extern const uint32_t g_rgctx_TU5BU5D_tA9AAA6ADBAC95AFA2FB6295553AA85C893A97227;
extern const uint32_t g_rgctx_Enumerator_t95EA745781BCCF798D9EFFE1A0E070FBFC471F8F;
extern const uint32_t g_rgctx_Enumerator__ctor_m280BEF2360693542715D2254731108D51F0D8830;
extern const uint32_t g_rgctx_NativeSlice_1U26_tA1812E47ABF0A731648EBE103EC2EBEB17B80476;
extern const uint32_t g_rgctx_IEnumerator_1_tBC3488787FAEB9403E91B74445415CB4313B5B03;
extern const uint32_t g_rgctx_NativeSlice_1_GetEnumerator_m2FF71F9A2E042FD3D64B1BE8635DCA733CF26EB1;
extern const uint32_t g_rgctx_NativeSlice_1_Equals_m76AE404ED4B8F993C43EF94E215462326B919F30;
extern const uint32_t g_rgctx_NativeSlice_1U26_tE971BE10EBCDE308A1541F8110FCBA414C67616A;
extern const uint32_t g_rgctx_NativeSlice_1_t162EA89993C636A6B88A168F1D37C4532D6C5E1B;
extern const uint32_t g_rgctx_Enumerator_t504C8759C6618524B38F557D5CE660D70B34A168;
extern const uint32_t g_rgctx_NativeSlice_1_get_Length_m727DD3DA3EA23B15E5F9D5B51471CAA2E43C24F8;
extern const uint32_t g_rgctx_NativeSlice_1_t162EA89993C636A6B88A168F1D37C4532D6C5E1B;
extern const uint32_t g_rgctx_NativeSlice_1_get_Item_m6AEBECC0468FBA63C6D83222E8D3AE42194AF9AE;
extern const uint32_t g_rgctx_T_t6DEC264D110932E9F21C7E232C370F10210B8165;
extern const uint32_t g_rgctx_Enumerator_get_Current_m8BCA74F6B5B28EB47124ECF863E4F98531DE6C70;
extern const uint32_t g_rgctx_Enumerator_t504C8759C6618524B38F557D5CE660D70B34A168;
extern const uint32_t g_rgctx_SharedStatic_1_tE092771727E6921B4A57CA11D8AB74496281E56B;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisT_tD893AF3D88C09CC1112FD976CA575A7A855066D0_m879E07C1306664721A79F1C1693A08F98B66830B;
extern const uint32_t g_rgctx_TU26_tBE06C6D65D857EB10D7131930E0CFA69230DD371;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tD893AF3D88C09CC1112FD976CA575A7A855066D0_mACEECD4695469A560DA771DBAB02DAAC8B923FC1;
extern const uint32_t g_rgctx_SharedStatic_1__ctor_m5E56BDEF7C5FD2D5D9999BBFE31B36BB5B515D50;
extern const uint32_t g_rgctx_BurstRuntime_GetHashCode64_TisTContext_t4F2733E8E90DBA0BC3E6A48E5BEA6FA03714E6AF_mA613B860B463511358A5E8E8BF23A3007FA4CD1E;
extern const uint32_t g_rgctx_HashCode64_1_tEC6075B45444522F8859FD1C0FBECB8B1CB1DCEF;
extern const uint32_t g_rgctx_HashCode64_1_tEC6075B45444522F8859FD1C0FBECB8B1CB1DCEF;
extern const uint32_t g_rgctx_T_t0950CBEE8A542FD63EDD2A4B01C4B61A4864E967;
extern const uint32_t g_rgctx_HashCode64_1_t03D8A642F13C2DD8E0DE0DF7F4892268238DB91E;
extern const uint32_t g_rgctx_HashCode64_1_t03D8A642F13C2DD8E0DE0DF7F4892268238DB91E;
extern const uint32_t g_rgctx_NativeArray_1_tE427A40BDB319E3A8E5955D8C9FD95222CF6D34C;
extern const uint32_t g_rgctx_NativeArray_1_t05AE66551542E7759233D80720E2E806CED187CE;
extern const uint32_t g_rgctx_NativeSlice_1_t7FECE477DA8BD50FAD2CE5D5E8AA24DEFFB818E3;
extern const uint32_t g_rgctx_NativeSlice_1_tDF54D5382140EF0801B11CF2C3BC567B883CB1E5;
extern const uint32_t g_rgctx_NativeSlice_1_t116CAE5850E6F759B55C8F8862EC6791B7620FBF;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisAlignOfHelper_1_t9287CDD928152D0ABCCD4196322B2F93EC1317AC_m83F5AC639BE2AB3E57D57A215986AD1978C10035;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tA45A0651658B25B78632C0849C8E9294D18C51F1_m7F1F532287215DD6B40CA1878D2F59C2B89F5737;
extern const uint32_t g_rgctx_T_t3818EBA71DF5EA591716A05E0BBD988D81931B5C;
extern const uint32_t g_rgctx_T_tF00BEB2D39C607DEF67E891605A383D95A9912A8;
extern const uint32_t g_rgctx_T_t9B5DAD81EFECDA494525FB73A37EE675A422B5E5;
extern const uint32_t g_rgctx_T_tF19C221134244841BC3C05863D0FAA96B103C520;
extern const uint32_t g_rgctx_TU26_tA784FACBA7D345646D749932AA7982E160E4747C;
extern const uint32_t g_rgctx_T_tE6588938356AD51CD7B5532933E8B84E7779C6E2;
extern const uint32_t g_rgctx_UU26_tFEA926B64EA66A5AD51F8347C1D2C810C4C7CD9D;
extern const uint32_t g_rgctx_TU26_t1F7975E6669C00533930CC8748553297B3868E78;
extern const uint32_t g_rgctx_TU26_t901727BC4FF52C726317D1F33EEDC7C5D77E3CCD;
extern const uint32_t g_rgctx_T_tA0A5977B9466EC4D9382AEF168A15381805BEFB6;
extern const uint32_t g_rgctx_UnsafeUtility_InternalEnumToInt_TisT_tA0A5977B9466EC4D9382AEF168A15381805BEFB6_mD822C2A355B19E619505BE8CCA47D070BC1E4ECE;
extern const uint32_t g_rgctx_TU26_t0DA4D95ECF055142996757209F03D8DAF0E84C74;
extern const uint32_t g_rgctx_TU26_tA0C763E8EC618118C41F1E06F6C1D785891FAA56;
extern const uint32_t g_rgctx_T_t59ABA7CBCA03FA1BDEA675A0F5246CCBCBFA2EF9;
extern const uint32_t g_rgctx_TU26_t37D65A6DBD4E0EA383692601DFEEA7F300E83BB8;
extern const uint32_t g_rgctx_T_t33D4A728696413871D1FA00D4BEB47300A9746A1;
extern const uint32_t g_rgctx_ExpressionEvaluator_TryParse_TisT_t33D4A728696413871D1FA00D4BEB47300A9746A1_m75D06EEACF11540CC4A2106D6E49C820BDF48201;
extern const uint32_t g_rgctx_ExpressionEvaluator_EvaluateTokens_TisT_t33D4A728696413871D1FA00D4BEB47300A9746A1_m9895D8F61BF325ED28DDFFF349DFF2D1564BCFCE;
extern const uint32_t g_rgctx_T_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C;
extern const uint32_t g_rgctx_TU26_t2EEABA1FE273327B39148A77C3883B65D73F0FC0;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m92AC0C51F31CFB2FB26865B7DAC0BE06FC7DDCC9;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m76B03EF2DD23DAE0CC6F3605F669E1BF04E47147;
extern const uint32_t g_rgctx_T_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m23CC3616A88827E62225C56C2B187A30941B0141;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m190DF8987237D94EA3D82B0D839F5E410C7D8700;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisInt64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_m039E57FEAAE208C6D622091E6991E821775DC7A9;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisInt64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m7CA5F999132EAF0EDC445440B427846247283D85;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B4B030E40E6E5B780F28ED8C1329DECF370EFC6;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m4DD797AF952D359DC3223EF11FD00B475B761C3A;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisDouble_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_m7E031A10605A70CF33921A4D215987BCE96B9CB1;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisDouble_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_mF07A23DA10FCF0FB0C29DFB6318905A30F314329;
extern const uint32_t g_rgctx_TU26_t8D3A62318ADFD5FC9624CA053233BBF1E291ED81;
extern const uint32_t g_rgctx_T_t5F074AF3F1B8B69780BD219266873DA3C5286EA1;
extern const uint32_t g_rgctx_T_t5F074AF3F1B8B69780BD219266873DA3C5286EA1;
extern const uint32_t g_rgctx_TU26_tBC8BEF96452C0A75BC81BE88774248F4227C7268;
extern const uint32_t g_rgctx_ExpressionEvaluator_EvaluateTokens_TisT_t84AB1FFC3DF9E2252A998E749A6C22B10FC8820C_m497074731D1F8CD7A72363D0D6F026DAB8DD845A;
extern const uint32_t g_rgctx_TU5BU5D_t7D16EB7F9A3D6DA46564839B3740450A2F527939;
extern const uint32_t g_rgctx_TU5BU5D_t7D16EB7F9A3D6DA46564839B3740450A2F527939;
extern const uint32_t g_rgctx_Mesh_GetAllocArrayFromChannel_TisT_t37CF6268F33923438D7CC8E15A2B9E3CA15DF7C1_mB02197DF665AA05616216157D8895B7818B81121;
extern const uint32_t g_rgctx_TU5BU5D_t6E23A39854EF48396CDA90CF4E5ACF693A0D1544;
extern const uint32_t g_rgctx_TU5BU5D_tF801442A8A2DCA98857A92D3A9110A9A541FB4EA;
extern const uint32_t g_rgctx_TU5BU5D_t86742559A92B971E7EEB86C630E5C0A820F22080;
extern const uint32_t g_rgctx_List_1_t76DFC70F5BECF87DC83ECF6B531F4E0975E5CA71;
extern const uint32_t g_rgctx_NoAllocHelpers_SafeLength_TisT_t0DCE4B4060595856884B9A3C03499720761720E7_m0C3AA170C86473E746CCE56272DFF6E11D18A1A5;
extern const uint32_t g_rgctx_List_1_t59759A9D3F9D21229DB5B34C8CF7DACD4EE1D566;
extern const uint32_t g_rgctx_NoAllocHelpers_SafeLength_TisT_t3B82752A9C5C210D3D8FC85C28D365C3241482D3_m61D4DE9BE9CF325AFC07CFEDBAE7402703A24839;
extern const uint32_t g_rgctx_List_1_t6DEE8586D1C4896B1D11095A8455CFF871345841;
extern const uint32_t g_rgctx_Mesh_GetListForChannel_TisT_tE4FAA5DDEC4D323662045B0868386FF42AF7CC9A_mF6E578309A2A7AB8B4F9261CA80E45C4BC3EBA57;
extern const uint32_t g_rgctx_List_1_tCA88055D3747C3BE6B5EECA9E34429F66AA43A6F;
extern const uint32_t g_rgctx_List_1_Clear_m09903AE25F21B4B38417BE0A711FFCC66E032E2C;
extern const uint32_t g_rgctx_NoAllocHelpers_EnsureListElemCount_TisT_t175569283E6C6A13DD1949EA7FD00D9222090F79_m4F99D3715A61EA612657653F3D45B822C297B0E9;
extern const uint32_t g_rgctx_List_1_t3C893F8198A4FDD8CF83FCC1E969395E9997CC6D;
extern const uint32_t g_rgctx_Mesh_SetListForChannel_TisT_t9A2EC75898A21D54F6DCC115311D500F06E96113_mEF92BF2023B83F28BB730B7C75EB7C16A718FB80;
extern const uint32_t g_rgctx_List_1_tD06BC3F654E9ACB3AD3B1BBF0965143A8433CBA0;
extern const uint32_t g_rgctx_Mesh_GetListForChannel_TisT_t043AAD067EDE16218EE3DC6816E26FD55FA17DA0_mEC4B1C6015865F58BF89DE007185566C2664CEA9;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t7DC7F18AC34B88CECD08C33869772F0CF80DAD83_m2B741DFCA650DA571C6AC154C32EDB18791DB823;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t7DC7F18AC34B88CECD08C33869772F0CF80DAD83_m45F9947B56C3D6F1FC1E57EDF5B88F430DA9DCE7;
extern const uint32_t g_rgctx_NativeArray_1_tA6187801071B8237944A029385A94DA00D525E0E;
extern const uint32_t g_rgctx_T_t7BA7113A2954530BD7E345E21D14635F49B5EDD6;
extern const uint32_t g_rgctx_T_t7BA7113A2954530BD7E345E21D14635F49B5EDD6;
extern const uint32_t g_rgctx_T_tEDCB060AB22B7099B7E88211AD5B8F4429918030;
extern const uint32_t g_rgctx_T_tEDCB060AB22B7099B7E88211AD5B8F4429918030;
extern const uint32_t g_rgctx_T_t910859EA270AFBED8361DB1BE0ADF1EB1DB21242;
extern const uint32_t g_rgctx_T_t910859EA270AFBED8361DB1BE0ADF1EB1DB21242;
extern const uint32_t g_rgctx_T_t1E55920ACC166F48B3917FD084904933E8AFE7F1;
extern const uint32_t g_rgctx_T_t1E55920ACC166F48B3917FD084904933E8AFE7F1;
extern const uint32_t g_rgctx_T_t15C649661EFEF4630AEAB738C610F68EA352E1B2;
extern const uint32_t g_rgctx_T_t15C649661EFEF4630AEAB738C610F68EA352E1B2;
extern const uint32_t g_rgctx_GameObject_GetComponentsInChildren_TisT_t9C4229D45A3436A8EDA0B78CCC78E44565A33075_m7213955683A0BAC293DA97EC19475773D46CDA2A;
extern const uint32_t g_rgctx_TU5BU5D_tF1E15A0644DC7345547500A8FC2DABAB2D7B46A1;
extern const uint32_t g_rgctx_List_1_t8550AD942695D83821F7A7EECC1DDDC34A774A63;
extern const uint32_t g_rgctx_GameObject_GetComponentsInChildren_TisT_tEE186EBF77E976372E60AED47266A1391821BBD4_m314D3FD4EFC1C7A9CFE0784793EA17B10261EDFF;
extern const uint32_t g_rgctx_Component_GetComponentsInChildren_TisT_tD5232F456914D3CCFC14A4ECEC7232A49FD3FC44_m16F98BE92AF7AD3D2C7B18FC4106DEBA58ED65A8;
extern const uint32_t g_rgctx_TU5BU5D_t442534C9CE96E56325CF2ADFA77278FC43331F9C;
extern const uint32_t g_rgctx_List_1_t0E5177FE8D122E6B3793FAD0E6A05F2DDEAB350F;
extern const uint32_t g_rgctx_Component_GetComponentsInChildren_TisT_t70879F5D1F734F88B350043E60CA45605BB03E57_m7797C0739D8065443FB967F47A8082235CC72AAF;
extern const uint32_t g_rgctx_T_t2A6C94BDAF110BD8403F6F2865F59F93528931C5;
extern const uint32_t g_rgctx_T_t2A6C94BDAF110BD8403F6F2865F59F93528931C5;
extern const uint32_t g_rgctx_GameObject_GetComponentsInParent_TisT_t13EC64AE8BCA0B052F8A55A4F7590FF973A3A9BF_m9FF1CFAB7F148ACAA269FF734BFB2DEFBDB11DC3;
extern const uint32_t g_rgctx_TU5BU5D_t6BC2EC2B4DD9FFD81998AD17A79640FB13C74E47;
extern const uint32_t g_rgctx_List_1_tEA49D6116B093FAABC14C4E80F5649F95B893C86;
extern const uint32_t g_rgctx_GameObject_GetComponentsInParent_TisT_t265302F3B091FB653AAB70FF196133D4DF0599D5_m5DD4866C643C34561D9C6E80E93AC4D2686147D4;
extern const uint32_t g_rgctx_Component_GetComponentsInParent_TisT_tE9CB3070184311E4DEB32802D1C766A19BBD057B_mE6EE65B4D8809935B84A4C756E27C532384C2B94;
extern const uint32_t g_rgctx_TU5BU5D_tE83959EC4C18B8AB85DED5DCFEEDDE6CE281DB71;
extern const uint32_t g_rgctx_T_t47581EF4E1C5D4D48A07717EFF6B45DD55F0BC77;
extern const uint32_t g_rgctx_List_1_t99F56A75BBECB7E17D0738D4C917BEB9C479E350;
extern const uint32_t g_rgctx_GameObject_GetComponents_TisT_tD486D49B2768692B2DFF7DB4A894D40A69E0021D_m89A6C2FC256BD38ABF9BA1FED0B5CC129FCC0063;
extern const uint32_t g_rgctx_TU5BU5D_tDE4A6DE4A9482E69B81DC5612D030B7C4F5BB29F;
extern const uint32_t g_rgctx_T_tB259E176CED00288F986516129580EFD8D4AC4E7;
extern const uint32_t g_rgctx_T_tB259E176CED00288F986516129580EFD8D4AC4E7;
extern const uint32_t g_rgctx_GameObject_GetComponentInChildren_TisT_tF7E65CEFF1A6E9ECF1C91FF28C04D64CBDC6C508_m7125E43B89963DC84A0F5E247343423D9A8E0A39;
extern const uint32_t g_rgctx_T_tF7E65CEFF1A6E9ECF1C91FF28C04D64CBDC6C508;
extern const uint32_t g_rgctx_T_t2BE0BE87029DEAA33F169A0B03C82C3C144D13EF;
extern const uint32_t g_rgctx_T_t2BE0BE87029DEAA33F169A0B03C82C3C144D13EF;
extern const uint32_t g_rgctx_GameObject_GetComponentInParent_TisT_tC8A36AFF723260461A0F52D73082E9632C021261_m6923AFC7739625B6E62C33009597CFC8B5DE11DD;
extern const uint32_t g_rgctx_T_tC8A36AFF723260461A0F52D73082E9632C021261;
extern const uint32_t g_rgctx_T_t03A438CAD8F43C95AEAEBCDB0C5E4B7E377C521A;
extern const uint32_t g_rgctx_T_t03A438CAD8F43C95AEAEBCDB0C5E4B7E377C521A;
extern const uint32_t g_rgctx_T_tE3707E950CC3E6F7B2FED3F3C488B2E13964BE1B;
extern const uint32_t g_rgctx_TU5BU5D_t4B58FED69931A702C4CDE236963F576AB35697E9;
extern const uint32_t g_rgctx_T_tAC38CEEB07F60B53F887995965E1C3FF46090B2C;
extern const uint32_t g_rgctx_List_1_t8C143E88C4EDF4DC41B2EEF4447974D310E11B3A;
extern const uint32_t g_rgctx_T_tDA9113C1B891D47285D948EFD16B87B90B3D1538;
extern const uint32_t g_rgctx_TU5BU5D_t69B4917BCF2456954F2E7F9DF106821544E242BD;
extern const uint32_t g_rgctx_T_tDCECE57B5AADF34806B5C7C69A129CD977BFD4E6;
extern const uint32_t g_rgctx_List_1_t91EF2118E614DDBB4EE072D431B38201A6A0F693;
extern const uint32_t g_rgctx_T_tADCF0C6EA7FA2FB920AB65E71CFF37A8EC63F977;
extern const uint32_t g_rgctx_List_1_t573A05AA365CAF0A415A4B498E3786F18F4D2308;
extern const uint32_t g_rgctx_T_tE0F624BB3415CD6A7022ABD2FEAF02175C649724;
extern const uint32_t g_rgctx_TU5BU5D_tD91BEA4D5423357326FA8F9E4045AB7147FADA56;
extern const uint32_t g_rgctx_T_tBEDE8BBE5DC8F74672BAD75661002C63A79E83E7;
extern const uint32_t g_rgctx_TU26_t5534C6AEE2CBFB048B7D57184C1B012C1FA39092;
extern const uint32_t g_rgctx_T_tBEDE8BBE5DC8F74672BAD75661002C63A79E83E7;
extern const uint32_t g_rgctx_T_t7221EA77A9457E95161C641D88DD8D4463B59C66;
extern const uint32_t g_rgctx_T_t7221EA77A9457E95161C641D88DD8D4463B59C66;
extern const uint32_t g_rgctx_List_1_t14E850A5CF7932B582C52CFAD23DAE3A8844CB6A;
extern const uint32_t g_rgctx_List_1_get_Capacity_m950272EBDA7BE5C545E5C6E98861D7BAA333CBA6;
extern const uint32_t g_rgctx_List_1_get_Count_mF63F236B7F03C2C446C693D4A2E351E57CE4AC9B;
extern const uint32_t g_rgctx_List_1_tF097BDA567D423BB3064364B29042A57AB60C0F3;
extern const uint32_t g_rgctx_List_1_Clear_m5CE88FD8A68574E3178E6B454102A376782840BC;
extern const uint32_t g_rgctx_List_1_get_Capacity_m060ECEDB3E3396BCA84A5B12C0055F1D548B6F84;
extern const uint32_t g_rgctx_List_1_set_Capacity_m7FC3CA9BDEB4FB5ED72C740ADFB069A2BFE52C0B;
extern const uint32_t g_rgctx_NoAllocHelpers_ResizeList_TisT_t271661F5330ED1BA0DD22767D014A7ECBD09132F_mECFBCA64EE547516DE90097096E018CB6194A8C3;
extern const uint32_t g_rgctx_List_1_t04A6DB85FED7D5DF99475BDA35CA46434506AAFA;
extern const uint32_t g_rgctx_List_1_get_Count_m20AAF3F2978329D5B80FE08325CD1642F0F91182;
extern const uint32_t g_rgctx_T_t6556024D2F85D8382E937209D35061559B88B1F0;
extern const uint32_t g_rgctx_T_t6556024D2F85D8382E937209D35061559B88B1F0;
extern const uint32_t g_rgctx_T_tA60FAD0EC183C8CBFEEB5B01A6EE24445587118B;
extern const uint32_t g_rgctx_T_tD768C2F5135E25A583230AA493A72CF06612DC1B;
extern const uint32_t g_rgctx_T_tD768C2F5135E25A583230AA493A72CF06612DC1B;
extern const uint32_t g_rgctx_InvokableCall_1_tBB2807680C901EEC0931EBB3CC369FD512104232;
extern const uint32_t g_rgctx_UnityAction_1_tCCA8583F35FD5AE88387AAD4E2261C24D9979824;
extern const uint32_t g_rgctx_UnityAction_1U26_t003CD2980BC88468DFB87F4F26D911D5F76C809B;
extern const uint32_t g_rgctx_UnityAction_1_tCCA8583F35FD5AE88387AAD4E2261C24D9979824;
extern const uint32_t g_rgctx_InvokableCall_1_add_Delegate_mECA9E6CE4BAD907A97BE5964BC2CF01178F9427A;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_t4A7981CB01490A2A7DA9FBF2B6226DF5A138F42E_m2CF182DF4823FA208F18560866D02ECCB42D3689;
extern const uint32_t g_rgctx_T1_t4A7981CB01490A2A7DA9FBF2B6226DF5A138F42E;
extern const uint32_t g_rgctx_UnityAction_1_Invoke_m6598320BD28F7690DC297C2D1F87436595246AD2;
extern const uint32_t g_rgctx_UnityAction_2_tA5FBFB62634255D0C1C76ED53ED268910F21D441;
extern const uint32_t g_rgctx_UnityAction_2_tA5FBFB62634255D0C1C76ED53ED268910F21D441;
extern const uint32_t g_rgctx_InvokableCall_2_t25703EC2711D3B861B27C1E73986FFE42462290A;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_t9D4999D88C7B830ABB2D05D533BCBEEDD1143578_mC812F6A0AB6543CC0934B8D01D42F540CC083632;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT2_tF7E890566125E6432EE9E728DF2678F1FBD7624F_m781399C01BF9B2CF93D6C6F399687C36AEBE7A0F;
extern const uint32_t g_rgctx_T1_t9D4999D88C7B830ABB2D05D533BCBEEDD1143578;
extern const uint32_t g_rgctx_T2_tF7E890566125E6432EE9E728DF2678F1FBD7624F;
extern const uint32_t g_rgctx_UnityAction_2_Invoke_m7B3D91E337EE5BAED8EA3215A76E80E739B4B426;
extern const uint32_t g_rgctx_UnityAction_3_tB802B24846E106C51390123789ECCE1427B772DB;
extern const uint32_t g_rgctx_UnityAction_3_tB802B24846E106C51390123789ECCE1427B772DB;
extern const uint32_t g_rgctx_InvokableCall_3_t9A9A08AA8C9E99E0E9AD8F5AD8F8BC42A8BC34C0;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_tDD0CBCE6751A7AA6283525B34926445E9E8AE9CD_mDBA9307DD964ED9B2103E16CE784F7502963843D;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT2_t96DEF47C17C0A95AF6486BBDB3BF783D23B0EEA4_m9CC62ECDC1273F1541771983F5777E47364C3B71;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT3_tF187F110CB8F78B82DCD38B6B40A536930C3073A_m37A026B8F8E2E32222257650E6B519548F214D3E;
extern const uint32_t g_rgctx_T1_tDD0CBCE6751A7AA6283525B34926445E9E8AE9CD;
extern const uint32_t g_rgctx_T2_t96DEF47C17C0A95AF6486BBDB3BF783D23B0EEA4;
extern const uint32_t g_rgctx_T3_tF187F110CB8F78B82DCD38B6B40A536930C3073A;
extern const uint32_t g_rgctx_UnityAction_3_Invoke_mA324678227155AC00FF70BE3C44323B457BFD798;
extern const uint32_t g_rgctx_UnityAction_4_tFE7F69F55A61B1C94EA4396BA51831F006EF45C1;
extern const uint32_t g_rgctx_UnityAction_4_tFE7F69F55A61B1C94EA4396BA51831F006EF45C1;
extern const uint32_t g_rgctx_InvokableCall_4_t9C475778DFDD6AD8185D3FF3E15C0BFC964A6E3A;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_tCE3D89B38CA521697C593DAE4D82CAFBB2C9ED94_m1087B95A9C01F80F1B5221E69BC6017B38AB68F0;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT2_t34D887E41D241A60221CA639979D3D4F38703E87_m2ABC7A918766103B83CC28C47F9FEAD112DE77B7;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT3_tFBD42D7B101B8A5CD76C71411573A51F3E2853CA_mFAAE7EAC34341C6C77B393C3D5538169AFF6108F;
extern const uint32_t g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT4_t0BBC6D9FC60BD795EF0615DE3AF72F4023D692E4_m43C0DBD42E0F60E0D67D077B41E35BB0373EC459;
extern const uint32_t g_rgctx_T1_tCE3D89B38CA521697C593DAE4D82CAFBB2C9ED94;
extern const uint32_t g_rgctx_T2_t34D887E41D241A60221CA639979D3D4F38703E87;
extern const uint32_t g_rgctx_T3_tFBD42D7B101B8A5CD76C71411573A51F3E2853CA;
extern const uint32_t g_rgctx_T4_t0BBC6D9FC60BD795EF0615DE3AF72F4023D692E4;
extern const uint32_t g_rgctx_UnityAction_4_Invoke_m8DF32CE2946727C5507D46D1FD8AD351D15816AB;
extern const uint32_t g_rgctx_InvokableCall_1__ctor_m1389E18EDB338B358E4F3F611C988D4F289C3610;
extern const uint32_t g_rgctx_InvokableCall_1_t9D6E3FE6D6A16671D4E69788C9ACC7F2B36C7991;
extern const uint32_t g_rgctx_T_tF41167E30BA1B4B336205DB03EA61D01E1183FE2;
extern const uint32_t g_rgctx_CachedInvokableCall_1_t80EFEB2F749FF3E9417150CF0DE6386A9807FC2E;
extern const uint32_t g_rgctx_InvokableCall_1_Invoke_mFF9FABA4D19E5E62BC94188FD9669DF0822877AF;
extern const uint32_t g_rgctx_UnityEvent_1_t89A7C04EA256B0BC85B7146022DCB5DEE2372F96;
extern const uint32_t g_rgctx_UnityAction_1_t404B757C0C30C249C0CAABEB1A3C91BA0C1C38FB;
extern const uint32_t g_rgctx_UnityEvent_1_GetDelegate_m4D19C6DDE943609A15B7489437A87E2234394A4B;
extern const uint32_t g_rgctx_UnityEvent_1_t89A7C04EA256B0BC85B7146022DCB5DEE2372F96;
extern const uint32_t g_rgctx_T0_t1DDE0E960341E6946BD0558FBC0AF39C25D1BADD;
extern const uint32_t g_rgctx_InvokableCall_1_tE6E100E6334FD6163AE44C87E199C349C093BD75;
extern const uint32_t g_rgctx_InvokableCall_1__ctor_m5A5E72EA57CAF0FB814DC260129AF6299C863168;
extern const uint32_t g_rgctx_InvokableCall_1__ctor_m059C54248830773976A8AA9E2376F2084554A793;
extern const uint32_t g_rgctx_T0_t1DDE0E960341E6946BD0558FBC0AF39C25D1BADD;
extern const uint32_t g_rgctx_InvokableCall_1_Invoke_m6FE04AA3ACA2CCDADFDBD7370510749312B0A9C1;
extern const uint32_t g_rgctx_UnityEvent_2_t77E864DD98236D3B31273B2344253FF4A721457D;
extern const uint32_t g_rgctx_T0_tAA07CC24358202AA2E3E7613A154A398E0B7B225;
extern const uint32_t g_rgctx_T1_t71A7ECBDA9A30E9DF39AB8875233AB21E2D64D81;
extern const uint32_t g_rgctx_InvokableCall_2_t4BFE337E7B7692E2C6577ACD66F585B79CE14A24;
extern const uint32_t g_rgctx_InvokableCall_2__ctor_m0919348880B03810235E94AB7B17DEEBD362EA14;
extern const uint32_t g_rgctx_UnityEvent_3_t312372CE38F9767D682B125288A6AB0C9B009769;
extern const uint32_t g_rgctx_T0_t2BF919984BA3AAA12AAAF3E9960454C42BB39A22;
extern const uint32_t g_rgctx_T1_t71CAC2632D33DBDDEE8DA6226138D3EDF698F5D9;
extern const uint32_t g_rgctx_T2_tCDED8A2B57D1F30D46697051883B8E26107AC0E1;
extern const uint32_t g_rgctx_InvokableCall_3_t010264F21755EF589AF36AB230DF4498652AB3BE;
extern const uint32_t g_rgctx_InvokableCall_3__ctor_mB9CDB20563729AB099EA7E3162512DAA4ED765FF;
extern const uint32_t g_rgctx_T0_t2BF919984BA3AAA12AAAF3E9960454C42BB39A22;
extern const uint32_t g_rgctx_T1_t71CAC2632D33DBDDEE8DA6226138D3EDF698F5D9;
extern const uint32_t g_rgctx_T2_tCDED8A2B57D1F30D46697051883B8E26107AC0E1;
extern const uint32_t g_rgctx_InvokableCall_3_Invoke_m2B54FFBD2AE2161557093314FE3001E70D7199A0;
extern const uint32_t g_rgctx_UnityEvent_4_t296FA0E84C9D3BAD9D66269A99EB742DED5D8068;
extern const uint32_t g_rgctx_T0_tD2970B459E331AB1F26BE87351F9ED7EA5BE4495;
extern const uint32_t g_rgctx_T1_t946247E05BF713B0B6D28C73BB95D5A26B0220B4;
extern const uint32_t g_rgctx_T2_tEAFC8D2E09B9D0FB1F1EBFF88BA6ACD3D4BDCD37;
extern const uint32_t g_rgctx_T3_t2C56F4E8001A03C9253A7F7E73722E18F6784426;
extern const uint32_t g_rgctx_InvokableCall_4_t2364CDDEAD7D89EC8A3DF50EDA200336E070465B;
extern const uint32_t g_rgctx_InvokableCall_4__ctor_m925C0D7AB0E0E91F76BEE4603727F999289C8274;
extern const uint32_t g_rgctx_CollectionPool_2_tBED14C57FCB68DB76BF3F96F4FB125E03B25076A;
extern const uint32_t g_rgctx_ObjectPool_1_t7C13985D550A748FA9A6B94F9C1623CE4679F470;
extern const uint32_t g_rgctx_CollectionPool_2_tBED14C57FCB68DB76BF3F96F4FB125E03B25076A;
extern const uint32_t g_rgctx_ObjectPool_1_Get_mA3C3EBD279EB78EE26579B86090F039A025B3AAD;
extern const uint32_t g_rgctx_TCollection_t5BFE2E618E961C6F5EEE5496DA074584F9BC3732;
extern const uint32_t g_rgctx_TCollectionU26_tD1D26C2C957721447DB9CBAFB196D4F8AECE0F1B;
extern const uint32_t g_rgctx_ObjectPool_1_Get_m87F64382B0759A4E02D36A79013A939993F447A9;
extern const uint32_t g_rgctx_PooledObject_1_t0B033DB05DDEB335F69421D1A15A587E59169136;
extern const uint32_t g_rgctx_ObjectPool_1_Release_m7E889E584638CF6B9C781FE859969C7915AD730A;
extern const uint32_t g_rgctx_U3CU3Ec_t63CAD67F42EE09460E8CE67D3D4AFC06C24EC824;
extern const uint32_t g_rgctx_U3CU3Ec_t63CAD67F42EE09460E8CE67D3D4AFC06C24EC824;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__5_0_m0B4164FD1C7C2E4CE0BE3446510F35EFCFFB1BD7;
extern const uint32_t g_rgctx_Func_1_t60D3E01E44AD68BD9FFC56E26FD378C90CEF589B;
extern const uint32_t g_rgctx_Func_1__ctor_m3AD0F60CC3D5CA04D03B558DDF5BCDBDEF489DA1;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__5_1_mB08119F806C7F8C2A74BEDF68322D727EAE53003;
extern const uint32_t g_rgctx_Action_1_t80D55DE3AD8221E0F566C71FCBD9992DAC8FECA3;
extern const uint32_t g_rgctx_Action_1__ctor_m5D4DE6984C42AAD6BA6FE2D8FBD1F839050CB78D;
extern const uint32_t g_rgctx_ObjectPool_1__ctor_m6B07874047F865BA4688F5D820D36045341DEA93;
extern const uint32_t g_rgctx_U3CU3Ec_t04D4865A912255024F906D17C8A195EE1593748A;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mB7955C548374943C37C5660785F721CA6B3BD6E4;
extern const uint32_t g_rgctx_U3CU3Ec_t04D4865A912255024F906D17C8A195EE1593748A;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTCollection_t5ED9CAA12108B62823E6AEA72BAD47F5185F55E1_mE61262CC51A95BE5DFFFFC2D6B0BE14131648938;
extern const uint32_t g_rgctx_TCollection_t5ED9CAA12108B62823E6AEA72BAD47F5185F55E1;
extern const uint32_t g_rgctx_ICollection_1_t2D304B3C7AEB2D43C3BBFFB1ADA33E7D20383732;
extern const uint32_t g_rgctx_ICollection_1_Clear_mEDD484F7F31DF15F6304ACD4B4DEB9503AC138C4;
extern const uint32_t g_rgctx_ObjectPool_1_tE63C8B63DBE8BFC17745E730041BB0BFAB511939;
extern const uint32_t g_rgctx_List_1_t48CE44222097DC044D0323B6923DF3571BB4E63F;
extern const uint32_t g_rgctx_List_1_get_Count_mE0490CB264655E58D8EB38C8CEA7E4D63D9BFC86;
extern const uint32_t g_rgctx_Func_1_tEF5BB74FDEF368155E200E306BA1CAD0CB436B65;
extern const uint32_t g_rgctx_List_1__ctor_m7BDCC43F9702F4DD69B48BB2AC48E4AB12FDDE36;
extern const uint32_t g_rgctx_Action_1_t4D3400CCC7A0899CF79A7812A39237BA49676E65;
extern const uint32_t g_rgctx_Func_1_Invoke_mD557332A8DC62B9B0F100EEB545AC928457EAD47;
extern const uint32_t g_rgctx_T_tCFAA8FE0D8B484C472D820C17B0793289E9E39C4;
extern const uint32_t g_rgctx_ObjectPool_1_get_CountAll_m4ECEC17D66AC8E10D4F781136198ED61E58DC591;
extern const uint32_t g_rgctx_ObjectPool_1_set_CountAll_m3965F34127517FF3838F3B1A3151B87BE2A8C1F4;
extern const uint32_t g_rgctx_List_1_get_Item_m1FC005C212E3BA6D94C438E07BC0D3611CFFFF12;
extern const uint32_t g_rgctx_List_1_RemoveAt_m2C020B040BB32EE06F6978BF6E9F3FAC12A4E263;
extern const uint32_t g_rgctx_Action_1_Invoke_mD207BC1B3DA843EF8932F60D9A5F70B46DCA862E;
extern const uint32_t g_rgctx_TU26_t5C4CF259A3EE4F51336F4ADD207B163A842FE9F7;
extern const uint32_t g_rgctx_ObjectPool_1_Get_m346965C47A8057A104FF86B589808164718AEFB8;
extern const uint32_t g_rgctx_PooledObject_1_tF70DD6778258EF86D38C51C05CB9495020DBA70A;
extern const uint32_t g_rgctx_PooledObject_1__ctor_m12A289C39275E42805DB3258E2357629761142D7;
extern const uint32_t g_rgctx_IObjectPool_1_tFB7317D2EDC6E942F862B0C735D615866B002539;
extern const uint32_t g_rgctx_ObjectPool_1_get_CountInactive_m7605BEF02A49685FD3519423597769D844625260;
extern const uint32_t g_rgctx_List_1_Add_m3AD4F7467CA41C15D1DA2441D1C79B4361CFC0D4;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m02AD606E75BA73445A250735C4384333D7631266;
extern const uint32_t g_rgctx_Enumerator_t4279EF3D6AE0215E6554BBB3E20DD93666166950;
extern const uint32_t g_rgctx_Enumerator_get_Current_m15A2D78D507C83E2D7EB4E3EC46A568F6CC0D2FE;
extern const uint32_t g_rgctx_Enumerator_t4279EF3D6AE0215E6554BBB3E20DD93666166950;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mDE516E1156D2CE1C26A7CF88D9FA578665FE54F6;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t4279EF3D6AE0215E6554BBB3E20DD93666166950_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_Clear_mB53AE625790E9FDEFCCAD32C7B1042076A4406BE;
extern const uint32_t g_rgctx_ObjectPool_1_Clear_mAD5C9798EC2C21AC68D8095711D14A3CEB4C0B76;
extern const uint32_t g_rgctx_T_tE8178DE971A98D370EDED444BCC42EF5AFD40430;
extern const uint32_t g_rgctx_PooledObject_1_t119028370366D5A1481B276909D61395ECB12D46;
extern const uint32_t g_rgctx_IObjectPool_1_tD8A69F69073B574D1E5D55EF91AD5406FA3F03D3;
extern const uint32_t g_rgctx_IObjectPool_1_Release_mB8CB48DDD89DEC27582F8424BF51A0B8EEC51393;
extern const uint32_t g_rgctx_RequestData_tFFF859BCA58727F08590F65886AC454D579DFF08;
extern const uint32_t g_rgctx_RenderPipeline_ProcessRenderRequests_TisRequestData_tFFF859BCA58727F08590F65886AC454D579DFF08_mAE1B63B260C9DF005A6CE8812E746C22533769A0;
extern const uint32_t g_rgctx_T_t8347C3FFBA421F6891E8C74737DD40D4CC6D19A6;
extern const uint32_t g_rgctx_T_t39F5D4667B732F487E6D1C04AC3AFC25E19D97FC;
extern const uint32_t g_rgctx_Assert_AreEqual_TisT_t39F5D4667B732F487E6D1C04AC3AFC25E19D97FC_m6EA71CD2154BA34CC1104179BBBDB440336BD7BD;
extern const uint32_t g_rgctx_T_tACCC857719CE88974D289FAF0EE9DDC58264563F;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m6F5A3F35F100A22E9E5144304BC7F0825DD6683E;
extern const uint32_t g_rgctx_EqualityComparer_1_tBBC02D7C0BB1C0839A7EB84A21A593A7E3FE32B6;
extern const uint32_t g_rgctx_EqualityComparer_1_tBBC02D7C0BB1C0839A7EB84A21A593A7E3FE32B6;
extern const uint32_t g_rgctx_Assert_AreEqual_TisT_tACCC857719CE88974D289FAF0EE9DDC58264563F_m91956B6C615894EA0D090D3D9EF9B139EDF01817;
extern const uint32_t g_rgctx_IEqualityComparer_1_t4C96CEB7A72FE5412A12D242B94C220518DBF17E;
extern const uint32_t g_rgctx_T_t94EE85DC24ABEB798E6AD2D103E6C932B138C076;
extern const uint32_t g_rgctx_T_t94EE85DC24ABEB798E6AD2D103E6C932B138C076;
extern const uint32_t g_rgctx_IEqualityComparer_1_t4D6A1FC62EFD34627033CB2C8B6F8C2865EB6DAE;
extern const uint32_t g_rgctx_IEqualityComparer_1_Equals_mF81350D824FA7125D8A99F8E425C31227C4611EC;
extern const uint32_t g_rgctx_T_tA266C7DE21788BC60E4F7D570B9B4F38E63F5322;
extern const uint32_t g_rgctx_Assert_IsNull_TisT_tA266C7DE21788BC60E4F7D570B9B4F38E63F5322_m5A57E9D3E17DAA935DA2D6789F335E354D2333C9;
extern const uint32_t g_rgctx_T_tF6394524B1DBF230DA5FCC6861A3D9BCF16CBB5B;
extern const uint32_t g_rgctx_T_tF6394524B1DBF230DA5FCC6861A3D9BCF16CBB5B;
extern const uint32_t g_rgctx_T_t75F7EBED19756DE55C2A0B03311DFBF12B8DA686;
extern const uint32_t g_rgctx_Assert_IsNotNull_TisT_t75F7EBED19756DE55C2A0B03311DFBF12B8DA686_m629E06008376CC9724470D63DC95DE2C4B454114;
extern const uint32_t g_rgctx_T_tFC2248A896F8D93A742E85F18F92E4647BA3AC28;
extern const uint32_t g_rgctx_T_tFC2248A896F8D93A742E85F18F92E4647BA3AC28;
static const Il2CppRGCTXDefinition s_rgctxValues[390] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobStruct_1_Initialize_mD7A7D050A756996D6ABC4B3263CAB0DDA01E19B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobStruct_1_t500AD58020CA7EED545A3C9BDED26F9ADFD03CF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobStruct_1_t9B14DC02939321AF08B0D9861EC2DE39FB2699ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobStruct_1_t9B14DC02939321AF08B0D9861EC2DE39FB2699ED },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tF4AF2A128E0752ED064F922D7181D6824613BB02 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobStruct_1_Execute_m45CF2DD91DFB334933AEB19608F139B97F00D9E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_t4512BFE0B86D90D2328A29F4BCD80437EACDA954 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_mB59C9E8588B3AFEFC7979AF159ED46EAF96ED5E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tE84B1A0A412D9CB22C4B7C045C3E0B39030378DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF4AF2A128E0752ED064F922D7181D6824613BB02 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tF4AF2A128E0752ED064F922D7181D6824613BB02_IJob_Execute_m6FB7F8EFAB486DA016EA24BB9BED238B4FC4DA27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreate_TisJobStruct_1_t9B14DC02939321AF08B0D9861EC2DE39FB2699ED_m654AF35867A4B084668D2EC6E98335B0EC45D671 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParallelForJobStruct_1_Initialize_m0FCB39C6F34FE41917212319A2C84964CA7D6BD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelForJobStruct_1_tCEBFABBE9BEFEF19C14EA7871A076D630B0F0A74 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelForJobStruct_1_tCEBFABBE9BEFEF19C14EA7871A076D630B0F0A74 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6813F43536401836AB43EBD9D1A33BF765878EC3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AddressOf_TisT_t6813F43536401836AB43EBD9D1A33BF765878EC3_m3F1DCD8B04BE305A251D5766807F32C2819145F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tC51B37173FD5E08D4D8831ABF794D0055AD5CFA2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IJobParallelForExtensions_GetReflectionData_TisT_t6813F43536401836AB43EBD9D1A33BF765878EC3_m90BDC4DEF041FB0B7EF61B8EE2290A2680C62068 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelForJobStruct_1_tEF5B83079821A31EF124E43C06E887A9F3325A10 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelForJobStruct_1_tEF5B83079821A31EF124E43C06E887A9F3325A10 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tFDC1689AE74A499B29AEBBF350042D7BEB5DF421 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParallelForJobStruct_1_Execute_mD697D00E6E20EA7358BF19B763AE8A8C806F0125 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_tD105541081E8AB3594F9FCA208DFBC84B1426C32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_m13591A35F12C3D8441B1DA0B8DF1983DF3835D8F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tD56ADC3413844E8641ED41194B152CD1674960E9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFDC1689AE74A499B29AEBBF350042D7BEB5DF421 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tFDC1689AE74A499B29AEBBF350042D7BEB5DF421_IJobParallelFor_Execute_m424D27AF3C66D5013E807D6FFF6B8E5D75A3DE82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreate_TisParallelForJobStruct_1_tEF5B83079821A31EF124E43C06E887A9F3325A10_mEEC197DEC91B74D426B4AF5F87D6BD7B279C1548 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Allocate_mB5C60244102A554BEDF83E77F7D668AF650811CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t9FED9BA6969776FB13D9111EB338E0273EE689F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tAABA6D541CCA28E111FD53E86E1EB641D3AB1CCD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t9FED9BA6969776FB13D9111EB338E0273EE689F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m49CDDF2EC7C8480D9665F98A8D35F1810ACD3947 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_mB720AF5A26CED6F7D5F5AF9F1FF54C641A99D51F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_m22C8155318CC4787B023DE2BED66C5B2518CA3D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_m83E61DA75E3A302EF6C903174839C6317AAC7FCC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t05490BB68E93F7FF07D46087FEC0242A3FB9D022 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t05490BB68E93F7FF07D46087FEC0242A3FB9D022_m1B320E8488A42E2C73BCBF01317A7F8C6CC67910 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m8F63985410A60584EDFCA5BA30D381AC413C0BF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tE2EF59F915D6B7F2A37C947360340D614063ACE2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator__ctor_m0E321AD2EF64898A83F9AE834A7C248665FF10C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t94A04507167D65A615DB0CF0C3569EA01137BC6F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_GetEnumerator_mA95B7D8B0D3419D5CBE4A7D8A2A2E2D57757AB0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Equals_mA006DE1192EAB16EF588E028577FA5169BABD1C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tD091D9C9F1D3DD99FB57510BE208E6C1D98C62CA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t99B5A119EEB30372BF581B474153FF6B44A9A1CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tEAEB17A03872DDD2F7667E53C31FB03AAA037703 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCB2B4AD90EC47251952F0D7CF84CE2365DFB7790 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tCB2B4AD90EC47251952F0D7CF84CE2365DFB7790_m7E5E51AD821589237204CCC66826D358D7077F93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m5D805336760473EF350CA58A4BB168EB1997843A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tEAEB17A03872DDD2F7667E53C31FB03AAA037703 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t03DCB2073CE1DA7BC252E41A107FB90449A3DA58 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t5CC8186A30AF1437BD72DF09D324740D06B08141 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1__ctor_m2D1EE616B587F8DD7D0FD938E3851FA9D6801546 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t355D3C4F6F548D3CA491C8ADF86ABB455ADCB675 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1__ctor_mAB6470448A68D5DF06E02310782DA04F9306AC54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tA97EC884413647283F01C67B3BC95ED41379C2F3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t050B3F6AF4DB5B2B490647C97C011F8C915BD960 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m33D38A2D95C8ABD59F4937B020B7E68CD6663B1A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t050B3F6AF4DB5B2B490647C97C011F8C915BD960 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1__ctor_mC7DE9713D4F8CB41CB3FD5BADF83BB8A16F7DDFF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tA97EC884413647283F01C67B3BC95ED41379C2F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1__ctor_m0A772F5B6F5EA9FE33FC4CF88229B18484B5F5E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m033B67D65CABCADAB69FC0A790A42485E2000D03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElementWithStride_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m3E9F8638EC263421E2FAF3BD5B7254E983A745F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElementWithStride_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m2335A90A9396C306C5F60C37F0C88EC5306DB4C5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_GetUnsafePtr_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m5DFD0A3C0034A0D21F260D46DC8FBDBFB19EE765 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Stride_m79069541DC62D6377285836CD49CC0E3997649D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t50AD86EE4B2269E506CE7FEE3ED6AAD25BCDF5A9_m02BA91BF441A8972D72D32DAE7A488DDDAD3D1E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tA9AAA6ADBAC95AFA2FB6295553AA85C893A97227 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t95EA745781BCCF798D9EFFE1A0E070FBFC471F8F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator__ctor_m280BEF2360693542715D2254731108D51F0D8830 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1U26_tA1812E47ABF0A731648EBE103EC2EBEB17B80476 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tBC3488787FAEB9403E91B74445415CB4313B5B03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_GetEnumerator_m2FF71F9A2E042FD3D64B1BE8635DCA733CF26EB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_Equals_m76AE404ED4B8F993C43EF94E215462326B919F30 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1U26_tE971BE10EBCDE308A1541F8110FCBA414C67616A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t162EA89993C636A6B88A168F1D37C4532D6C5E1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t504C8759C6618524B38F557D5CE660D70B34A168 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Length_m727DD3DA3EA23B15E5F9D5B51471CAA2E43C24F8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t162EA89993C636A6B88A168F1D37C4532D6C5E1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Item_m6AEBECC0468FBA63C6D83222E8D3AE42194AF9AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6DEC264D110932E9F21C7E232C370F10210B8165 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m8BCA74F6B5B28EB47124ECF863E4F98531DE6C70 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t504C8759C6618524B38F557D5CE660D70B34A168 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SharedStatic_1_tE092771727E6921B4A57CA11D8AB74496281E56B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisT_tD893AF3D88C09CC1112FD976CA575A7A855066D0_m879E07C1306664721A79F1C1693A08F98B66830B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tBE06C6D65D857EB10D7131930E0CFA69230DD371 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tD893AF3D88C09CC1112FD976CA575A7A855066D0_mACEECD4695469A560DA771DBAB02DAAC8B923FC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1__ctor_m5E56BDEF7C5FD2D5D9999BBFE31B36BB5B515D50 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BurstRuntime_GetHashCode64_TisTContext_t4F2733E8E90DBA0BC3E6A48E5BEA6FA03714E6AF_mA613B860B463511358A5E8E8BF23A3007FA4CD1E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_tEC6075B45444522F8859FD1C0FBECB8B1CB1DCEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_tEC6075B45444522F8859FD1C0FBECB8B1CB1DCEF },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t0950CBEE8A542FD63EDD2A4B01C4B61A4864E967 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_t03D8A642F13C2DD8E0DE0DF7F4892268238DB91E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_t03D8A642F13C2DD8E0DE0DF7F4892268238DB91E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tE427A40BDB319E3A8E5955D8C9FD95222CF6D34C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t05AE66551542E7759233D80720E2E806CED187CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t7FECE477DA8BD50FAD2CE5D5E8AA24DEFFB818E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tDF54D5382140EF0801B11CF2C3BC567B883CB1E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t116CAE5850E6F759B55C8F8862EC6791B7620FBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisAlignOfHelper_1_t9287CDD928152D0ABCCD4196322B2F93EC1317AC_m83F5AC639BE2AB3E57D57A215986AD1978C10035 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tA45A0651658B25B78632C0849C8E9294D18C51F1_m7F1F532287215DD6B40CA1878D2F59C2B89F5737 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3818EBA71DF5EA591716A05E0BBD988D81931B5C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF00BEB2D39C607DEF67E891605A383D95A9912A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9B5DAD81EFECDA494525FB73A37EE675A422B5E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF19C221134244841BC3C05863D0FAA96B103C520 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tA784FACBA7D345646D749932AA7982E160E4747C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE6588938356AD51CD7B5532933E8B84E7779C6E2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_tFEA926B64EA66A5AD51F8347C1D2C810C4C7CD9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t1F7975E6669C00533930CC8748553297B3868E78 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t901727BC4FF52C726317D1F33EEDC7C5D77E3CCD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA0A5977B9466EC4D9382AEF168A15381805BEFB6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_InternalEnumToInt_TisT_tA0A5977B9466EC4D9382AEF168A15381805BEFB6_mD822C2A355B19E619505BE8CCA47D070BC1E4ECE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t0DA4D95ECF055142996757209F03D8DAF0E84C74 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tA0C763E8EC618118C41F1E06F6C1D785891FAA56 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t59ABA7CBCA03FA1BDEA675A0F5246CCBCBFA2EF9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t37D65A6DBD4E0EA383692601DFEEA7F300E83BB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t33D4A728696413871D1FA00D4BEB47300A9746A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExpressionEvaluator_TryParse_TisT_t33D4A728696413871D1FA00D4BEB47300A9746A1_m75D06EEACF11540CC4A2106D6E49C820BDF48201 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExpressionEvaluator_EvaluateTokens_TisT_t33D4A728696413871D1FA00D4BEB47300A9746A1_m9895D8F61BF325ED28DDFFF349DFF2D1564BCFCE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t2EEABA1FE273327B39148A77C3883B65D73F0FC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m92AC0C51F31CFB2FB26865B7DAC0BE06FC7DDCC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m76B03EF2DD23DAE0CC6F3605F669E1BF04E47147 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m23CC3616A88827E62225C56C2B187A30941B0141 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m190DF8987237D94EA3D82B0D839F5E410C7D8700 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisInt64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_m039E57FEAAE208C6D622091E6991E821775DC7A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisInt64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m7CA5F999132EAF0EDC445440B427846247283D85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B4B030E40E6E5B780F28ED8C1329DECF370EFC6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_m4DD797AF952D359DC3223EF11FD00B475B761C3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_TisDouble_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_m7E031A10605A70CF33921A4D215987BCE96B9CB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisDouble_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_TisT_tED1A83FD589CF4C18C4DEB9FB6D8B1984D69805C_mF07A23DA10FCF0FB0C29DFB6318905A30F314329 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t8D3A62318ADFD5FC9624CA053233BBF1E291ED81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5F074AF3F1B8B69780BD219266873DA3C5286EA1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t5F074AF3F1B8B69780BD219266873DA3C5286EA1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tBC8BEF96452C0A75BC81BE88774248F4227C7268 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExpressionEvaluator_EvaluateTokens_TisT_t84AB1FFC3DF9E2252A998E749A6C22B10FC8820C_m497074731D1F8CD7A72363D0D6F026DAB8DD845A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7D16EB7F9A3D6DA46564839B3740450A2F527939 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7D16EB7F9A3D6DA46564839B3740450A2F527939 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Mesh_GetAllocArrayFromChannel_TisT_t37CF6268F33923438D7CC8E15A2B9E3CA15DF7C1_mB02197DF665AA05616216157D8895B7818B81121 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6E23A39854EF48396CDA90CF4E5ACF693A0D1544 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF801442A8A2DCA98857A92D3A9110A9A541FB4EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t86742559A92B971E7EEB86C630E5C0A820F22080 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t76DFC70F5BECF87DC83ECF6B531F4E0975E5CA71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NoAllocHelpers_SafeLength_TisT_t0DCE4B4060595856884B9A3C03499720761720E7_m0C3AA170C86473E746CCE56272DFF6E11D18A1A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t59759A9D3F9D21229DB5B34C8CF7DACD4EE1D566 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NoAllocHelpers_SafeLength_TisT_t3B82752A9C5C210D3D8FC85C28D365C3241482D3_m61D4DE9BE9CF325AFC07CFEDBAE7402703A24839 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t6DEE8586D1C4896B1D11095A8455CFF871345841 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Mesh_GetListForChannel_TisT_tE4FAA5DDEC4D323662045B0868386FF42AF7CC9A_mF6E578309A2A7AB8B4F9261CA80E45C4BC3EBA57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tCA88055D3747C3BE6B5EECA9E34429F66AA43A6F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m09903AE25F21B4B38417BE0A711FFCC66E032E2C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NoAllocHelpers_EnsureListElemCount_TisT_t175569283E6C6A13DD1949EA7FD00D9222090F79_m4F99D3715A61EA612657653F3D45B822C297B0E9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t3C893F8198A4FDD8CF83FCC1E969395E9997CC6D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Mesh_SetListForChannel_TisT_t9A2EC75898A21D54F6DCC115311D500F06E96113_mEF92BF2023B83F28BB730B7C75EB7C16A718FB80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tD06BC3F654E9ACB3AD3B1BBF0965143A8433CBA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Mesh_GetListForChannel_TisT_t043AAD067EDE16218EE3DC6816E26FD55FA17DA0_mEC4B1C6015865F58BF89DE007185566C2664CEA9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t7DC7F18AC34B88CECD08C33869772F0CF80DAD83_m2B741DFCA650DA571C6AC154C32EDB18791DB823 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t7DC7F18AC34B88CECD08C33869772F0CF80DAD83_m45F9947B56C3D6F1FC1E57EDF5B88F430DA9DCE7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tA6187801071B8237944A029385A94DA00D525E0E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t7BA7113A2954530BD7E345E21D14635F49B5EDD6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7BA7113A2954530BD7E345E21D14635F49B5EDD6 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tEDCB060AB22B7099B7E88211AD5B8F4429918030 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEDCB060AB22B7099B7E88211AD5B8F4429918030 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t910859EA270AFBED8361DB1BE0ADF1EB1DB21242 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t910859EA270AFBED8361DB1BE0ADF1EB1DB21242 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t1E55920ACC166F48B3917FD084904933E8AFE7F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1E55920ACC166F48B3917FD084904933E8AFE7F1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t15C649661EFEF4630AEAB738C610F68EA352E1B2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t15C649661EFEF4630AEAB738C610F68EA352E1B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInChildren_TisT_t9C4229D45A3436A8EDA0B78CCC78E44565A33075_m7213955683A0BAC293DA97EC19475773D46CDA2A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF1E15A0644DC7345547500A8FC2DABAB2D7B46A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t8550AD942695D83821F7A7EECC1DDDC34A774A63 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInChildren_TisT_tEE186EBF77E976372E60AED47266A1391821BBD4_m314D3FD4EFC1C7A9CFE0784793EA17B10261EDFF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponentsInChildren_TisT_tD5232F456914D3CCFC14A4ECEC7232A49FD3FC44_m16F98BE92AF7AD3D2C7B18FC4106DEBA58ED65A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t442534C9CE96E56325CF2ADFA77278FC43331F9C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t0E5177FE8D122E6B3793FAD0E6A05F2DDEAB350F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponentsInChildren_TisT_t70879F5D1F734F88B350043E60CA45605BB03E57_m7797C0739D8065443FB967F47A8082235CC72AAF },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t2A6C94BDAF110BD8403F6F2865F59F93528931C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2A6C94BDAF110BD8403F6F2865F59F93528931C5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInParent_TisT_t13EC64AE8BCA0B052F8A55A4F7590FF973A3A9BF_m9FF1CFAB7F148ACAA269FF734BFB2DEFBDB11DC3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6BC2EC2B4DD9FFD81998AD17A79640FB13C74E47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tEA49D6116B093FAABC14C4E80F5649F95B893C86 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInParent_TisT_t265302F3B091FB653AAB70FF196133D4DF0599D5_m5DD4866C643C34561D9C6E80E93AC4D2686147D4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponentsInParent_TisT_tE9CB3070184311E4DEB32802D1C766A19BBD057B_mE6EE65B4D8809935B84A4C756E27C532384C2B94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tE83959EC4C18B8AB85DED5DCFEEDDE6CE281DB71 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t47581EF4E1C5D4D48A07717EFF6B45DD55F0BC77 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t99F56A75BBECB7E17D0738D4C917BEB9C479E350 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponents_TisT_tD486D49B2768692B2DFF7DB4A894D40A69E0021D_m89A6C2FC256BD38ABF9BA1FED0B5CC129FCC0063 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tDE4A6DE4A9482E69B81DC5612D030B7C4F5BB29F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tB259E176CED00288F986516129580EFD8D4AC4E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB259E176CED00288F986516129580EFD8D4AC4E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentInChildren_TisT_tF7E65CEFF1A6E9ECF1C91FF28C04D64CBDC6C508_m7125E43B89963DC84A0F5E247343423D9A8E0A39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF7E65CEFF1A6E9ECF1C91FF28C04D64CBDC6C508 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t2BE0BE87029DEAA33F169A0B03C82C3C144D13EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2BE0BE87029DEAA33F169A0B03C82C3C144D13EF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentInParent_TisT_tC8A36AFF723260461A0F52D73082E9632C021261_m6923AFC7739625B6E62C33009597CFC8B5DE11DD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC8A36AFF723260461A0F52D73082E9632C021261 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t03A438CAD8F43C95AEAEBCDB0C5E4B7E377C521A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t03A438CAD8F43C95AEAEBCDB0C5E4B7E377C521A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tE3707E950CC3E6F7B2FED3F3C488B2E13964BE1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t4B58FED69931A702C4CDE236963F576AB35697E9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tAC38CEEB07F60B53F887995965E1C3FF46090B2C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t8C143E88C4EDF4DC41B2EEF4447974D310E11B3A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tDA9113C1B891D47285D948EFD16B87B90B3D1538 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t69B4917BCF2456954F2E7F9DF106821544E242BD },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tDCECE57B5AADF34806B5C7C69A129CD977BFD4E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t91EF2118E614DDBB4EE072D431B38201A6A0F693 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tADCF0C6EA7FA2FB920AB65E71CFF37A8EC63F977 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t573A05AA365CAF0A415A4B498E3786F18F4D2308 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tE0F624BB3415CD6A7022ABD2FEAF02175C649724 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD91BEA4D5423357326FA8F9E4045AB7147FADA56 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tBEDE8BBE5DC8F74672BAD75661002C63A79E83E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5534C6AEE2CBFB048B7D57184C1B012C1FA39092 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBEDE8BBE5DC8F74672BAD75661002C63A79E83E7 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t7221EA77A9457E95161C641D88DD8D4463B59C66 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7221EA77A9457E95161C641D88DD8D4463B59C66 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t14E850A5CF7932B582C52CFAD23DAE3A8844CB6A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_m950272EBDA7BE5C545E5C6E98861D7BAA333CBA6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mF63F236B7F03C2C446C693D4A2E351E57CE4AC9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tF097BDA567D423BB3064364B29042A57AB60C0F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m5CE88FD8A68574E3178E6B454102A376782840BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_m060ECEDB3E3396BCA84A5B12C0055F1D548B6F84 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Capacity_m7FC3CA9BDEB4FB5ED72C740ADFB069A2BFE52C0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NoAllocHelpers_ResizeList_TisT_t271661F5330ED1BA0DD22767D014A7ECBD09132F_mECFBCA64EE547516DE90097096E018CB6194A8C3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t04A6DB85FED7D5DF99475BDA35CA46434506AAFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m20AAF3F2978329D5B80FE08325CD1642F0F91182 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t6556024D2F85D8382E937209D35061559B88B1F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6556024D2F85D8382E937209D35061559B88B1F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA60FAD0EC183C8CBFEEB5B01A6EE24445587118B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD768C2F5135E25A583230AA493A72CF06612DC1B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tD768C2F5135E25A583230AA493A72CF06612DC1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_1_tBB2807680C901EEC0931EBB3CC369FD512104232 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_tCCA8583F35FD5AE88387AAD4E2261C24D9979824 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1U26_t003CD2980BC88468DFB87F4F26D911D5F76C809B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_UnityAction_1_tCCA8583F35FD5AE88387AAD4E2261C24D9979824 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_1_add_Delegate_mECA9E6CE4BAD907A97BE5964BC2CF01178F9427A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_t4A7981CB01490A2A7DA9FBF2B6226DF5A138F42E_m2CF182DF4823FA208F18560866D02ECCB42D3689 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t4A7981CB01490A2A7DA9FBF2B6226DF5A138F42E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_1_Invoke_m6598320BD28F7690DC297C2D1F87436595246AD2 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_UnityAction_2_tA5FBFB62634255D0C1C76ED53ED268910F21D441 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_2_tA5FBFB62634255D0C1C76ED53ED268910F21D441 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_2_t25703EC2711D3B861B27C1E73986FFE42462290A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_t9D4999D88C7B830ABB2D05D533BCBEEDD1143578_mC812F6A0AB6543CC0934B8D01D42F540CC083632 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT2_tF7E890566125E6432EE9E728DF2678F1FBD7624F_m781399C01BF9B2CF93D6C6F399687C36AEBE7A0F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t9D4999D88C7B830ABB2D05D533BCBEEDD1143578 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tF7E890566125E6432EE9E728DF2678F1FBD7624F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_2_Invoke_m7B3D91E337EE5BAED8EA3215A76E80E739B4B426 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_UnityAction_3_tB802B24846E106C51390123789ECCE1427B772DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_3_tB802B24846E106C51390123789ECCE1427B772DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_3_t9A9A08AA8C9E99E0E9AD8F5AD8F8BC42A8BC34C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_tDD0CBCE6751A7AA6283525B34926445E9E8AE9CD_mDBA9307DD964ED9B2103E16CE784F7502963843D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT2_t96DEF47C17C0A95AF6486BBDB3BF783D23B0EEA4_m9CC62ECDC1273F1541771983F5777E47364C3B71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT3_tF187F110CB8F78B82DCD38B6B40A536930C3073A_m37A026B8F8E2E32222257650E6B519548F214D3E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_tDD0CBCE6751A7AA6283525B34926445E9E8AE9CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t96DEF47C17C0A95AF6486BBDB3BF783D23B0EEA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T3_tF187F110CB8F78B82DCD38B6B40A536930C3073A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_3_Invoke_mA324678227155AC00FF70BE3C44323B457BFD798 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_UnityAction_4_tFE7F69F55A61B1C94EA4396BA51831F006EF45C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_4_tFE7F69F55A61B1C94EA4396BA51831F006EF45C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_4_t9C475778DFDD6AD8185D3FF3E15C0BFC964A6E3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT1_tCE3D89B38CA521697C593DAE4D82CAFBB2C9ED94_m1087B95A9C01F80F1B5221E69BC6017B38AB68F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT2_t34D887E41D241A60221CA639979D3D4F38703E87_m2ABC7A918766103B83CC28C47F9FEAD112DE77B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT3_tFBD42D7B101B8A5CD76C71411573A51F3E2853CA_mFAAE7EAC34341C6C77B393C3D5538169AFF6108F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BaseInvokableCall_ThrowOnInvalidArg_TisT4_t0BBC6D9FC60BD795EF0615DE3AF72F4023D692E4_m43C0DBD42E0F60E0D67D077B41E35BB0373EC459 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_tCE3D89B38CA521697C593DAE4D82CAFBB2C9ED94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t34D887E41D241A60221CA639979D3D4F38703E87 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T3_tFBD42D7B101B8A5CD76C71411573A51F3E2853CA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T4_t0BBC6D9FC60BD795EF0615DE3AF72F4023D692E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_4_Invoke_m8DF32CE2946727C5507D46D1FD8AD351D15816AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_1__ctor_m1389E18EDB338B358E4F3F611C988D4F289C3610 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_1_t9D6E3FE6D6A16671D4E69788C9ACC7F2B36C7991 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF41167E30BA1B4B336205DB03EA61D01E1183FE2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CachedInvokableCall_1_t80EFEB2F749FF3E9417150CF0DE6386A9807FC2E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_1_Invoke_mFF9FABA4D19E5E62BC94188FD9669DF0822877AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_1_t89A7C04EA256B0BC85B7146022DCB5DEE2372F96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_t404B757C0C30C249C0CAABEB1A3C91BA0C1C38FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_GetDelegate_m4D19C6DDE943609A15B7489437A87E2234394A4B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_1_t89A7C04EA256B0BC85B7146022DCB5DEE2372F96 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T0_t1DDE0E960341E6946BD0558FBC0AF39C25D1BADD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_1_tE6E100E6334FD6163AE44C87E199C349C093BD75 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_1__ctor_m5A5E72EA57CAF0FB814DC260129AF6299C863168 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_1__ctor_m059C54248830773976A8AA9E2376F2084554A793 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T0_t1DDE0E960341E6946BD0558FBC0AF39C25D1BADD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_1_Invoke_m6FE04AA3ACA2CCDADFDBD7370510749312B0A9C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_2_t77E864DD98236D3B31273B2344253FF4A721457D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T0_tAA07CC24358202AA2E3E7613A154A398E0B7B225 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_t71A7ECBDA9A30E9DF39AB8875233AB21E2D64D81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_2_t4BFE337E7B7692E2C6577ACD66F585B79CE14A24 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_2__ctor_m0919348880B03810235E94AB7B17DEEBD362EA14 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_3_t312372CE38F9767D682B125288A6AB0C9B009769 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T0_t2BF919984BA3AAA12AAAF3E9960454C42BB39A22 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_t71CAC2632D33DBDDEE8DA6226138D3EDF698F5D9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_tCDED8A2B57D1F30D46697051883B8E26107AC0E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_3_t010264F21755EF589AF36AB230DF4498652AB3BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_3__ctor_mB9CDB20563729AB099EA7E3162512DAA4ED765FF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T0_t2BF919984BA3AAA12AAAF3E9960454C42BB39A22 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t71CAC2632D33DBDDEE8DA6226138D3EDF698F5D9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tCDED8A2B57D1F30D46697051883B8E26107AC0E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_3_Invoke_m2B54FFBD2AE2161557093314FE3001E70D7199A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_4_t296FA0E84C9D3BAD9D66269A99EB742DED5D8068 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T0_tD2970B459E331AB1F26BE87351F9ED7EA5BE4495 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_t946247E05BF713B0B6D28C73BB95D5A26B0220B4 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_tEAFC8D2E09B9D0FB1F1EBFF88BA6ACD3D4BDCD37 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T3_t2C56F4E8001A03C9253A7F7E73722E18F6784426 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InvokableCall_4_t2364CDDEAD7D89EC8A3DF50EDA200336E070465B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokableCall_4__ctor_m925C0D7AB0E0E91F76BEE4603727F999289C8274 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionPool_2_tBED14C57FCB68DB76BF3F96F4FB125E03B25076A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObjectPool_1_t7C13985D550A748FA9A6B94F9C1623CE4679F470 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionPool_2_tBED14C57FCB68DB76BF3F96F4FB125E03B25076A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_Get_mA3C3EBD279EB78EE26579B86090F039A025B3AAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TCollection_t5BFE2E618E961C6F5EEE5496DA074584F9BC3732 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TCollectionU26_tD1D26C2C957721447DB9CBAFB196D4F8AECE0F1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_Get_m87F64382B0759A4E02D36A79013A939993F447A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledObject_1_t0B033DB05DDEB335F69421D1A15A587E59169136 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_Release_m7E889E584638CF6B9C781FE859969C7915AD730A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t63CAD67F42EE09460E8CE67D3D4AFC06C24EC824 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t63CAD67F42EE09460E8CE67D3D4AFC06C24EC824 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__5_0_m0B4164FD1C7C2E4CE0BE3446510F35EFCFFB1BD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t60D3E01E44AD68BD9FFC56E26FD378C90CEF589B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1__ctor_m3AD0F60CC3D5CA04D03B558DDF5BCDBDEF489DA1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__5_1_mB08119F806C7F8C2A74BEDF68322D727EAE53003 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t80D55DE3AD8221E0F566C71FCBD9992DAC8FECA3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_m5D4DE6984C42AAD6BA6FE2D8FBD1F839050CB78D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1__ctor_m6B07874047F865BA4688F5D820D36045341DEA93 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t04D4865A912255024F906D17C8A195EE1593748A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mB7955C548374943C37C5660785F721CA6B3BD6E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t04D4865A912255024F906D17C8A195EE1593748A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTCollection_t5ED9CAA12108B62823E6AEA72BAD47F5185F55E1_mE61262CC51A95BE5DFFFFC2D6B0BE14131648938 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TCollection_t5ED9CAA12108B62823E6AEA72BAD47F5185F55E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t2D304B3C7AEB2D43C3BBFFB1ADA33E7D20383732 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Clear_mEDD484F7F31DF15F6304ACD4B4DEB9503AC138C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObjectPool_1_tE63C8B63DBE8BFC17745E730041BB0BFAB511939 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t48CE44222097DC044D0323B6923DF3571BB4E63F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mE0490CB264655E58D8EB38C8CEA7E4D63D9BFC86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_tEF5BB74FDEF368155E200E306BA1CAD0CB436B65 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m7BDCC43F9702F4DD69B48BB2AC48E4AB12FDDE36 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t4D3400CCC7A0899CF79A7812A39237BA49676E65 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_mD557332A8DC62B9B0F100EEB545AC928457EAD47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCFAA8FE0D8B484C472D820C17B0793289E9E39C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_get_CountAll_m4ECEC17D66AC8E10D4F781136198ED61E58DC591 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_set_CountAll_m3965F34127517FF3838F3B1A3151B87BE2A8C1F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m1FC005C212E3BA6D94C438E07BC0D3611CFFFF12 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_RemoveAt_m2C020B040BB32EE06F6978BF6E9F3FAC12A4E263 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mD207BC1B3DA843EF8932F60D9A5F70B46DCA862E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5C4CF259A3EE4F51336F4ADD207B163A842FE9F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_Get_m346965C47A8057A104FF86B589808164718AEFB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledObject_1_tF70DD6778258EF86D38C51C05CB9495020DBA70A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PooledObject_1__ctor_m12A289C39275E42805DB3258E2357629761142D7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IObjectPool_1_tFB7317D2EDC6E942F862B0C735D615866B002539 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_get_CountInactive_m7605BEF02A49685FD3519423597769D844625260 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m3AD4F7467CA41C15D1DA2441D1C79B4361CFC0D4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m02AD606E75BA73445A250735C4384333D7631266 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t4279EF3D6AE0215E6554BBB3E20DD93666166950 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m15A2D78D507C83E2D7EB4E3EC46A568F6CC0D2FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t4279EF3D6AE0215E6554BBB3E20DD93666166950 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mDE516E1156D2CE1C26A7CF88D9FA578665FE54F6 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t4279EF3D6AE0215E6554BBB3E20DD93666166950_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_mB53AE625790E9FDEFCCAD32C7B1042076A4406BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_Clear_mAD5C9798EC2C21AC68D8095711D14A3CEB4C0B76 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE8178DE971A98D370EDED444BCC42EF5AFD40430 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledObject_1_t119028370366D5A1481B276909D61395ECB12D46 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IObjectPool_1_tD8A69F69073B574D1E5D55EF91AD5406FA3F03D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IObjectPool_1_Release_mB8CB48DDD89DEC27582F8424BF51A0B8EEC51393 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RequestData_tFFF859BCA58727F08590F65886AC454D579DFF08 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_RenderPipeline_ProcessRenderRequests_TisRequestData_tFFF859BCA58727F08590F65886AC454D579DFF08_mAE1B63B260C9DF005A6CE8812E746C22533769A0 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t8347C3FFBA421F6891E8C74737DD40D4CC6D19A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t39F5D4667B732F487E6D1C04AC3AFC25E19D97FC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Assert_AreEqual_TisT_t39F5D4667B732F487E6D1C04AC3AFC25E19D97FC_m6EA71CD2154BA34CC1104179BBBDB440336BD7BD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tACCC857719CE88974D289FAF0EE9DDC58264563F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m6F5A3F35F100A22E9E5144304BC7F0825DD6683E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tBBC02D7C0BB1C0839A7EB84A21A593A7E3FE32B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tBBC02D7C0BB1C0839A7EB84A21A593A7E3FE32B6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Assert_AreEqual_TisT_tACCC857719CE88974D289FAF0EE9DDC58264563F_m91956B6C615894EA0D090D3D9EF9B139EDF01817 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_t4C96CEB7A72FE5412A12D242B94C220518DBF17E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t94EE85DC24ABEB798E6AD2D103E6C932B138C076 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t94EE85DC24ABEB798E6AD2D103E6C932B138C076 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_t4D6A1FC62EFD34627033CB2C8B6F8C2865EB6DAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEqualityComparer_1_Equals_mF81350D824FA7125D8A99F8E425C31227C4611EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA266C7DE21788BC60E4F7D570B9B4F38E63F5322 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Assert_IsNull_TisT_tA266C7DE21788BC60E4F7D570B9B4F38E63F5322_m5A57E9D3E17DAA935DA2D6789F335E354D2333C9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tF6394524B1DBF230DA5FCC6861A3D9BCF16CBB5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF6394524B1DBF230DA5FCC6861A3D9BCF16CBB5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t75F7EBED19756DE55C2A0B03311DFBF12B8DA686 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Assert_IsNotNull_TisT_t75F7EBED19756DE55C2A0B03311DFBF12B8DA686_m629E06008376CC9724470D63DC95DE2C4B454114 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tFC2248A896F8D93A742E85F18F92E4647BA3AC28 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFC2248A896F8D93A742E85F18F92E4647BA3AC28 },
};
static TypeDefinitionIndex s_staticConstructorsToRunAtStartup[10] = 
{
	2614,
	2804,
	2805,
	2806,
	2807,
	2808,
	2809,
	2810,
	2811,
	0,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_CoreModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_CoreModule_CodeGenModule = 
{
	"UnityEngine.CoreModule.dll",
	2109,
	s_methodPointers,
	344,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	103,
	s_rgctxIndices,
	390,
	s_rgctxValues,
	NULL,
	NULL,
	s_staticConstructorsToRunAtStartup,
	NULL,
	NULL,
};
