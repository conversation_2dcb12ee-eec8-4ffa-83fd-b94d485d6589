extern "C" void RegisterStaticallyLinkedModulesGranular()
{
	void RegisterModule_SharedInternals();
	RegisterModule_SharedInternals();

	void RegisterModule_Core();
	RegisterModule_Core();

	void RegisterModule_Animation();
	RegisterModule_Animation();

	void RegisterModule_Audio();
	RegisterModule_Audio();

	void RegisterModule_InputLegacy();
	RegisterModule_InputLegacy();

	void RegisterModule_IMGUI();
	RegisterModule_IMGUI();

	void RegisterModule_JSONSerialize();
	RegisterModule_JSONSerialize();

	void RegisterModule_RuntimeInitializeOnLoadManagerInitializer();
	RegisterModule_RuntimeInitializeOnLoadManagerInitializer();

	void RegisterModule_TextRendering();
	RegisterModule_TextRendering();

	void RegisterModule_TextCoreFontEngine();
	RegisterModule_TextCoreFontEngine();

	void RegisterModule_TextCoreTextEngine();
	RegisterModule_TextCoreTextEngine();

	void RegisterModule_UI();
	RegisterModule_UI();

	void RegisterModule_UIElements();
	RegisterModule_UIElements();

	void RegisterModule_WebGL();
	RegisterModule_WebGL();

}

template <typename T> void RegisterUnityClass(const char*);
template <typename T> void RegisterStrippedType(int, const char*, const char*);

void InvokeRegisterStaticallyLinkedModuleClasses()
{
	// Do nothing (we're in stripping mode)
}

class Animator; template <> void RegisterUnityClass<Animator>(const char*);
class AnimatorController; template <> void RegisterUnityClass<AnimatorController>(const char*);
class AnimatorOverrideController; template <> void RegisterUnityClass<AnimatorOverrideController>(const char*);
class RuntimeAnimatorController; template <> void RegisterUnityClass<RuntimeAnimatorController>(const char*);
class AudioBehaviour; template <> void RegisterUnityClass<AudioBehaviour>(const char*);
class AudioClip; template <> void RegisterUnityClass<AudioClip>(const char*);
class AudioListener; template <> void RegisterUnityClass<AudioListener>(const char*);
class AudioManager; template <> void RegisterUnityClass<AudioManager>(const char*);
class SampleClip; template <> void RegisterUnityClass<SampleClip>(const char*);
class Behaviour; template <> void RegisterUnityClass<Behaviour>(const char*);
class BuildSettings; template <> void RegisterUnityClass<BuildSettings>(const char*);
class Camera; template <> void RegisterUnityClass<Camera>(const char*);
namespace Unity { class Component; } template <> void RegisterUnityClass<Unity::Component>(const char*);
class ComputeShader; template <> void RegisterUnityClass<ComputeShader>(const char*);
class Cubemap; template <> void RegisterUnityClass<Cubemap>(const char*);
class CubemapArray; template <> void RegisterUnityClass<CubemapArray>(const char*);
class DelayedCallManager; template <> void RegisterUnityClass<DelayedCallManager>(const char*);
class EditorExtension; template <> void RegisterUnityClass<EditorExtension>(const char*);
class GameManager; template <> void RegisterUnityClass<GameManager>(const char*);
class GameObject; template <> void RegisterUnityClass<GameObject>(const char*);
class GlobalGameManager; template <> void RegisterUnityClass<GlobalGameManager>(const char*);
class GraphicsSettings; template <> void RegisterUnityClass<GraphicsSettings>(const char*);
class InputManager; template <> void RegisterUnityClass<InputManager>(const char*);
class LevelGameManager; template <> void RegisterUnityClass<LevelGameManager>(const char*);
class Light; template <> void RegisterUnityClass<Light>(const char*);
class LightProbes; template <> void RegisterUnityClass<LightProbes>(const char*);
class LightingSettings; template <> void RegisterUnityClass<LightingSettings>(const char*);
class LightmapSettings; template <> void RegisterUnityClass<LightmapSettings>(const char*);
class LowerResBlitTexture; template <> void RegisterUnityClass<LowerResBlitTexture>(const char*);
class Material; template <> void RegisterUnityClass<Material>(const char*);
class Mesh; template <> void RegisterUnityClass<Mesh>(const char*);
class MeshFilter; template <> void RegisterUnityClass<MeshFilter>(const char*);
class MeshRenderer; template <> void RegisterUnityClass<MeshRenderer>(const char*);
class MonoBehaviour; template <> void RegisterUnityClass<MonoBehaviour>(const char*);
class MonoManager; template <> void RegisterUnityClass<MonoManager>(const char*);
class MonoScript; template <> void RegisterUnityClass<MonoScript>(const char*);
class NamedObject; template <> void RegisterUnityClass<NamedObject>(const char*);
class Object; template <> void RegisterUnityClass<Object>(const char*);
class PlayerSettings; template <> void RegisterUnityClass<PlayerSettings>(const char*);
class PreloadData; template <> void RegisterUnityClass<PreloadData>(const char*);
class QualitySettings; template <> void RegisterUnityClass<QualitySettings>(const char*);
namespace UI { class RectTransform; } template <> void RegisterUnityClass<UI::RectTransform>(const char*);
class ReflectionProbe; template <> void RegisterUnityClass<ReflectionProbe>(const char*);
class RenderSettings; template <> void RegisterUnityClass<RenderSettings>(const char*);
class RenderTexture; template <> void RegisterUnityClass<RenderTexture>(const char*);
class Renderer; template <> void RegisterUnityClass<Renderer>(const char*);
class ResourceManager; template <> void RegisterUnityClass<ResourceManager>(const char*);
class RuntimeInitializeOnLoadManager; template <> void RegisterUnityClass<RuntimeInitializeOnLoadManager>(const char*);
class Shader; template <> void RegisterUnityClass<Shader>(const char*);
class ShaderNameRegistry; template <> void RegisterUnityClass<ShaderNameRegistry>(const char*);
class SortingGroup; template <> void RegisterUnityClass<SortingGroup>(const char*);
class Sprite; template <> void RegisterUnityClass<Sprite>(const char*);
class SpriteAtlas; template <> void RegisterUnityClass<SpriteAtlas>(const char*);
class SpriteRenderer; template <> void RegisterUnityClass<SpriteRenderer>(const char*);
class TagManager; template <> void RegisterUnityClass<TagManager>(const char*);
class TextAsset; template <> void RegisterUnityClass<TextAsset>(const char*);
class Texture; template <> void RegisterUnityClass<Texture>(const char*);
class Texture2D; template <> void RegisterUnityClass<Texture2D>(const char*);
class Texture2DArray; template <> void RegisterUnityClass<Texture2DArray>(const char*);
class Texture3D; template <> void RegisterUnityClass<Texture3D>(const char*);
class TimeManager; template <> void RegisterUnityClass<TimeManager>(const char*);
class Transform; template <> void RegisterUnityClass<Transform>(const char*);
namespace TextRendering { class Font; } template <> void RegisterUnityClass<TextRendering::Font>(const char*);
namespace UI { class Canvas; } template <> void RegisterUnityClass<UI::Canvas>(const char*);
namespace UI { class CanvasGroup; } template <> void RegisterUnityClass<UI::CanvasGroup>(const char*);
namespace UI { class CanvasRenderer; } template <> void RegisterUnityClass<UI::CanvasRenderer>(const char*);

void RegisterAllClasses()
{
void RegisterBuiltinTypes();
RegisterBuiltinTypes();
	//Total: 66 non stripped classes
	//0. Animator
	RegisterUnityClass<Animator>("Animation");
	//1. AnimatorController
	RegisterUnityClass<AnimatorController>("Animation");
	//2. AnimatorOverrideController
	RegisterUnityClass<AnimatorOverrideController>("Animation");
	//3. RuntimeAnimatorController
	RegisterUnityClass<RuntimeAnimatorController>("Animation");
	//4. AudioBehaviour
	RegisterUnityClass<AudioBehaviour>("Audio");
	//5. AudioClip
	RegisterUnityClass<AudioClip>("Audio");
	//6. AudioListener
	RegisterUnityClass<AudioListener>("Audio");
	//7. AudioManager
	RegisterUnityClass<AudioManager>("Audio");
	//8. SampleClip
	RegisterUnityClass<SampleClip>("Audio");
	//9. Behaviour
	RegisterUnityClass<Behaviour>("Core");
	//10. BuildSettings
	RegisterUnityClass<BuildSettings>("Core");
	//11. Camera
	RegisterUnityClass<Camera>("Core");
	//12. Component
	RegisterUnityClass<Unity::Component>("Core");
	//13. ComputeShader
	RegisterUnityClass<ComputeShader>("Core");
	//14. Cubemap
	RegisterUnityClass<Cubemap>("Core");
	//15. CubemapArray
	RegisterUnityClass<CubemapArray>("Core");
	//16. DelayedCallManager
	RegisterUnityClass<DelayedCallManager>("Core");
	//17. EditorExtension
	RegisterUnityClass<EditorExtension>("Core");
	//18. GameManager
	RegisterUnityClass<GameManager>("Core");
	//19. GameObject
	RegisterUnityClass<GameObject>("Core");
	//20. GlobalGameManager
	RegisterUnityClass<GlobalGameManager>("Core");
	//21. GraphicsSettings
	RegisterUnityClass<GraphicsSettings>("Core");
	//22. InputManager
	RegisterUnityClass<InputManager>("Core");
	//23. LevelGameManager
	RegisterUnityClass<LevelGameManager>("Core");
	//24. Light
	RegisterUnityClass<Light>("Core");
	//25. LightProbes
	RegisterUnityClass<LightProbes>("Core");
	//26. LightingSettings
	RegisterUnityClass<LightingSettings>("Core");
	//27. LightmapSettings
	RegisterUnityClass<LightmapSettings>("Core");
	//28. LowerResBlitTexture
	RegisterUnityClass<LowerResBlitTexture>("Core");
	//29. Material
	RegisterUnityClass<Material>("Core");
	//30. Mesh
	RegisterUnityClass<Mesh>("Core");
	//31. MeshFilter
	RegisterUnityClass<MeshFilter>("Core");
	//32. MeshRenderer
	RegisterUnityClass<MeshRenderer>("Core");
	//33. MonoBehaviour
	RegisterUnityClass<MonoBehaviour>("Core");
	//34. MonoManager
	RegisterUnityClass<MonoManager>("Core");
	//35. MonoScript
	RegisterUnityClass<MonoScript>("Core");
	//36. NamedObject
	RegisterUnityClass<NamedObject>("Core");
	//37. Object
	//Skipping Object
	//38. PlayerSettings
	RegisterUnityClass<PlayerSettings>("Core");
	//39. PreloadData
	RegisterUnityClass<PreloadData>("Core");
	//40. QualitySettings
	RegisterUnityClass<QualitySettings>("Core");
	//41. RectTransform
	RegisterUnityClass<UI::RectTransform>("Core");
	//42. ReflectionProbe
	RegisterUnityClass<ReflectionProbe>("Core");
	//43. RenderSettings
	RegisterUnityClass<RenderSettings>("Core");
	//44. RenderTexture
	RegisterUnityClass<RenderTexture>("Core");
	//45. Renderer
	RegisterUnityClass<Renderer>("Core");
	//46. ResourceManager
	RegisterUnityClass<ResourceManager>("Core");
	//47. RuntimeInitializeOnLoadManager
	RegisterUnityClass<RuntimeInitializeOnLoadManager>("Core");
	//48. Shader
	RegisterUnityClass<Shader>("Core");
	//49. ShaderNameRegistry
	RegisterUnityClass<ShaderNameRegistry>("Core");
	//50. SortingGroup
	RegisterUnityClass<SortingGroup>("Core");
	//51. Sprite
	RegisterUnityClass<Sprite>("Core");
	//52. SpriteAtlas
	RegisterUnityClass<SpriteAtlas>("Core");
	//53. SpriteRenderer
	RegisterUnityClass<SpriteRenderer>("Core");
	//54. TagManager
	RegisterUnityClass<TagManager>("Core");
	//55. TextAsset
	RegisterUnityClass<TextAsset>("Core");
	//56. Texture
	RegisterUnityClass<Texture>("Core");
	//57. Texture2D
	RegisterUnityClass<Texture2D>("Core");
	//58. Texture2DArray
	RegisterUnityClass<Texture2DArray>("Core");
	//59. Texture3D
	RegisterUnityClass<Texture3D>("Core");
	//60. TimeManager
	RegisterUnityClass<TimeManager>("Core");
	//61. Transform
	RegisterUnityClass<Transform>("Core");
	//62. Font
	RegisterUnityClass<TextRendering::Font>("TextRendering");
	//63. Canvas
	RegisterUnityClass<UI::Canvas>("UI");
	//64. CanvasGroup
	RegisterUnityClass<UI::CanvasGroup>("UI");
	//65. CanvasRenderer
	RegisterUnityClass<UI::CanvasRenderer>("UI");

}
