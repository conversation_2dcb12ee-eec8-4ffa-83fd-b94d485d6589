{"System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"Library/PlayerDataCache/WebGL/Data/data.unity3d": "data.unity3d", "Library/PlayerDataCache/WebGL/Data/RuntimeInitializeOnLoads.json": "RuntimeInitializeOnLoads.json", "Library/PlayerDataCache/WebGL/Data/ScriptingAssemblies.json": "ScriptingAssemblies.json", "Library/Bee/artifacts/WebGL/boot.config": "boot.config", "D:/Game Development/Interns/Basic Connect Wallet/Library/Bee/artifacts/WebGL/il2cppOutput/data/Metadata/global-metadata.dat": "Il2CppData/Metadata/global-metadata.dat", "C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WebGLSupport/BuildTools/data/unity_default_resources": "Resources/unity_default_resources", "Temp/StagingArea/Data/Resources/unity_builtin_extra": "Data/Resources/unity_builtin_extra"}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "WebGLPlayerBuildProgram.DataAssembler", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\WebGLPlayerBuildProgram.exe", "targets": ["Library/Bee/artifacts/WebGL/webgl.data"], "inputs": ["Library/PlayerDataCache/WebGL/Data/data.unity3d", "Library/PlayerDataCache/WebGL/Data/RuntimeInitializeOnLoads.json", "Library/PlayerDataCache/WebGL/Data/ScriptingAssemblies.json", "Library/Bee/artifacts/WebGL/boot.config", "D:/Game Development/Interns/Basic Connect Wallet/Library/Bee/artifacts/WebGL/il2cppOutput/data/Metadata/global-metadata.dat", "C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WebGLSupport/BuildTools/data/unity_default_resources", "Temp/StagingArea/Data/Resources/unity_builtin_extra"], "targetDirectories": []}}