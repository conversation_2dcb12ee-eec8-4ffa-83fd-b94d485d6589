{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 11208, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 11208, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 11208, "tid": 1807, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 11208, "tid": 1807, "ts": 1754309325437089, "dur": 14, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 11208, "tid": 1807, "ts": 1754309325437119, "dur": 6, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 11208, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 11208, "tid": 1, "ts": 1754309325167045, "dur": 1817, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 11208, "tid": 1, "ts": 1754309325168865, "dur": 33851, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 11208, "tid": 1, "ts": 1754309325202718, "dur": 32514, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 11208, "tid": 1807, "ts": 1754309325437128, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 11208, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325166787, "dur": 20828, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325187616, "dur": 248934, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325187628, "dur": 33, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325187663, "dur": 10, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325187676, "dur": 2018, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325189749, "dur": 52, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325189872, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325189951, "dur": 31, "ph": "X", "name": "ProcessMessages 5611", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325190012, "dur": 167, "ph": "X", "name": "ReadAsync 5611", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325190242, "dur": 38, "ph": "X", "name": "ProcessMessages 3069", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325190388, "dur": 181, "ph": "X", "name": "ReadAsync 3069", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325190597, "dur": 5, "ph": "X", "name": "ProcessMessages 9248", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325190624, "dur": 152, "ph": "X", "name": "ReadAsync 9248", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325190927, "dur": 31, "ph": "X", "name": "ProcessMessages 2809", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325190987, "dur": 139, "ph": "X", "name": "ReadAsync 2809", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325191164, "dur": 6, "ph": "X", "name": "ProcessMessages 10012", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325194979, "dur": 386, "ph": "X", "name": "ReadAsync 10012", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195368, "dur": 23, "ph": "X", "name": "ProcessMessages 20492", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195395, "dur": 32, "ph": "X", "name": "ReadAsync 20492", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195430, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195433, "dur": 24, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195461, "dur": 24, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195487, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195489, "dur": 18, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195509, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195511, "dur": 19, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195535, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195575, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195577, "dur": 22, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195601, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195603, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195628, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195630, "dur": 27, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195660, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195663, "dur": 33, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195700, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195703, "dur": 24, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195730, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195732, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195762, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195764, "dur": 24, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195790, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195792, "dur": 26, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195821, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195823, "dur": 21, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195847, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195849, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195872, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195874, "dur": 25, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195902, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195904, "dur": 24, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195931, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195933, "dur": 25, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195960, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195963, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195985, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325195988, "dur": 23, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196014, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196016, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196046, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196049, "dur": 22, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196073, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196075, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196099, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196101, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196122, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196124, "dur": 30, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196158, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196181, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196182, "dur": 24, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196210, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196212, "dur": 23, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196237, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196239, "dur": 34, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196275, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196277, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196299, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196321, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196323, "dur": 25, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196350, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196353, "dur": 21, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196378, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196404, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196406, "dur": 19, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196427, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196429, "dur": 196, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196630, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196661, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196663, "dur": 26, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196692, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196695, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196723, "dur": 3, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196727, "dur": 25, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196754, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196756, "dur": 21, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196779, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196781, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196811, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196813, "dur": 20, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196835, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196837, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196864, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196866, "dur": 25, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196895, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196897, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196919, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196922, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196949, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196951, "dur": 23, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196976, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325196979, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197007, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197009, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197031, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197033, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197056, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197058, "dur": 24, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197084, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197086, "dur": 23, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197111, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197113, "dur": 27, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197142, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197144, "dur": 20, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197166, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197168, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197193, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197196, "dur": 32, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197230, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197233, "dur": 24, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197260, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197262, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197289, "dur": 17, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197308, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197310, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197338, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197357, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197359, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197381, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197383, "dur": 66, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197452, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197475, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197478, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197501, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197502, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197526, "dur": 28, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197556, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197558, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197580, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197582, "dur": 23, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197608, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197633, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197635, "dur": 20, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197657, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197659, "dur": 19, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197680, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197682, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197703, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197706, "dur": 20, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197729, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197731, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197754, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197756, "dur": 29, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197789, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197818, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197821, "dur": 21, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197843, "dur": 1, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197846, "dur": 24, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197872, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197874, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197896, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197898, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197921, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197923, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197945, "dur": 45, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325197998, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198028, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198031, "dur": 27, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198061, "dur": 3, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198065, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198096, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198098, "dur": 27, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198128, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198135, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198158, "dur": 23, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198184, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198207, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198209, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198236, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198239, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198264, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198266, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198287, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198289, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198312, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198314, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198336, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198339, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198357, "dur": 15, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198375, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198397, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198399, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198426, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198428, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198451, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198453, "dur": 21, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198476, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198478, "dur": 18, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198499, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198501, "dur": 20, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198523, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198525, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198546, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198548, "dur": 20, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198570, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198572, "dur": 19, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198594, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198597, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198620, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198622, "dur": 19, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198644, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198646, "dur": 19, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198667, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198668, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198694, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198696, "dur": 25, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198724, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198727, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198759, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198761, "dur": 26, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198790, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198792, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198819, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198821, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198848, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198851, "dur": 24, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198877, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198879, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198901, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198903, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198927, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198930, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198954, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198957, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198980, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325198983, "dur": 26, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199012, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199014, "dur": 27, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199043, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199045, "dur": 22, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199069, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199071, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199100, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199103, "dur": 22, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199128, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199130, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199152, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199154, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199177, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199179, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199204, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199230, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199232, "dur": 23, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199257, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199259, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199283, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199286, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199314, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199317, "dur": 27, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199346, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199349, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199374, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199377, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199394, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199395, "dur": 19, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199416, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199418, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199437, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199439, "dur": 16, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199456, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199458, "dur": 17, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199477, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199479, "dur": 28, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199510, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199512, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199532, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199534, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199554, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199556, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199578, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199580, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199600, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199601, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199622, "dur": 23, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199647, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199649, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199671, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199673, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199698, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199699, "dur": 20, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199721, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199724, "dur": 18, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199744, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199747, "dur": 21, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199770, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199772, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199791, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199824, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199826, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199848, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199849, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199871, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199892, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199894, "dur": 19, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199915, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199917, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199939, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199942, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199962, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199964, "dur": 19, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325199986, "dur": 18, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200007, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200009, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200031, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200033, "dur": 19, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200054, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200056, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200078, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200080, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200106, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200108, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200132, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200134, "dur": 20, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200157, "dur": 19, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200179, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200199, "dur": 18, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200220, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200243, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200247, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200270, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200273, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200294, "dur": 23, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200320, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200344, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200346, "dur": 17, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200366, "dur": 17, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200386, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200406, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200425, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200441, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200443, "dur": 19, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200465, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200482, "dur": 1, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200484, "dur": 16, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200503, "dur": 381, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200889, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200911, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200912, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200931, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200966, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200987, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325200989, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201027, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201047, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201049, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201081, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201083, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201121, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201123, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201147, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201149, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201170, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201173, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201195, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201197, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201220, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201222, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201246, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201265, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201284, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201286, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201307, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201309, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201329, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201351, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201371, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201374, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201396, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201416, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201437, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201458, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201461, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201484, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201486, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201505, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201524, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201543, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201545, "dur": 16, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201566, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201586, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201604, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201606, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201627, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201630, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201677, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201698, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201700, "dur": 67, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201772, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201796, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201798, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201820, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201822, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201840, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201859, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201879, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201882, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201899, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201902, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201922, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201940, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201942, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201961, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201986, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325201988, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202011, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202013, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202032, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202035, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202057, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202061, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202084, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202088, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202114, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202116, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202146, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202167, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202170, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202192, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202195, "dur": 20, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202219, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202221, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202245, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202248, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202273, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202275, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202300, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202302, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202326, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202329, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202352, "dur": 3, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202356, "dur": 16, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202375, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202379, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202402, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202404, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202424, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202426, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202450, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202467, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202489, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202510, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202512, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202534, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202536, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202560, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202563, "dur": 16, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202581, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202583, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202606, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202608, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202684, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202703, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202705, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202752, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325202774, "dur": 409, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325203187, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325203210, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325203395, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325203418, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325203420, "dur": 6206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209631, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209634, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209673, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209675, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209740, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209742, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209774, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209777, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209800, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209801, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209879, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209900, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325209904, "dur": 751, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325210660, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325210686, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325210689, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325210745, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325210770, "dur": 438, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211212, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211241, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211261, "dur": 119, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211384, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211407, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211409, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211430, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211494, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211510, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211675, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211695, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211827, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211853, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211855, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211930, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211945, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325211982, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212005, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212007, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212098, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212113, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212135, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212154, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212323, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212338, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212457, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212479, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212481, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212497, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212538, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212553, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212569, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212586, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212602, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212617, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212632, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212652, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212671, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212686, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212701, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212720, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212738, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212754, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212776, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212797, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212799, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212818, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212834, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212865, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212890, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212892, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212916, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212965, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325212982, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213001, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213020, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213036, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213066, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213087, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213089, "dur": 83, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213176, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213197, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213220, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213222, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213246, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213248, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213268, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213270, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213286, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213308, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213397, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213421, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213437, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213483, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213503, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213505, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213564, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213582, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213601, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213619, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213621, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213639, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213738, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213756, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213784, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213807, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325213993, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214008, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214205, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214208, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214231, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214336, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214358, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214360, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214384, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214386, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214407, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214409, "dur": 439, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214853, "dur": 126, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214981, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325214983, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215009, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215011, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215052, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215054, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215122, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215148, "dur": 265, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215419, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215445, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215447, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215470, "dur": 246, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215721, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215746, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215764, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215766, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215848, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215869, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215898, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325215916, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325216132, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325216158, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325216181, "dur": 3320, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325219506, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325219509, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325219540, "dur": 8, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325219549, "dur": 189733, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325409296, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325409302, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325409346, "dur": 367, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325409715, "dur": 4864, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325414590, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325414593, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325414621, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325414625, "dur": 3237, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325417868, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325417871, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325417900, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325417923, "dur": 6422, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325424352, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325424355, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325424374, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325424377, "dur": 900, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325425283, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325425310, "dur": 23, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325425334, "dur": 355, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325425693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325425694, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325425722, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 11208, "tid": 25769803776, "ts": 1754309325425724, "dur": 10815, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 11208, "tid": 1807, "ts": 1754309325437142, "dur": 1249, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 11208, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 11208, "tid": 21474836480, "ts": 1754309325166747, "dur": 68509, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 11208, "tid": 21474836480, "ts": 1754309325235257, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 11208, "tid": 21474836480, "ts": 1754309325235260, "dur": 71, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 11208, "tid": 1807, "ts": 1754309325438394, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 11208, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 11208, "tid": 17179869184, "ts": 1754309325164469, "dur": 272124, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 11208, "tid": 17179869184, "ts": 1754309325164607, "dur": 2106, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 11208, "tid": 17179869184, "ts": 1754309325436596, "dur": 72, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 11208, "tid": 17179869184, "ts": 1754309325436612, "dur": 18, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 11208, "tid": 17179869184, "ts": 1754309325436669, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 11208, "tid": 1807, "ts": 1754309325438404, "dur": 18, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754309325187105, "dur": 2040, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325189159, "dur": 586, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325189836, "dur": 392, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325190645, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_2A1DCE08184F097D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754309325191239, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_845FBD1B5AF442B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754309325191774, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_C6B909213A0BCD59.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754309325193419, "dur": 2686, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754309325190255, "dur": 10994, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325201255, "dur": 224719, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325425975, "dur": 232, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325426348, "dur": 74, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325426439, "dur": 6024, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754309325190383, "dur": 10878, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325201297, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325201484, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1754309325201272, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_55CE00BEA3C83003.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754309325201714, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325201843, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_6DBABB005B232272.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754309325202069, "dur": 632, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325202068, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_6AAF5D748AA28C64.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754309325203031, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754309325203122, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754309325203868, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325204117, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325204736, "dur": 647, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325205448, "dur": 626, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325206076, "dur": 785, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325206905, "dur": 806, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325207993, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325208199, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325208256, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325209100, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325210038, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325210093, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325203308, "dur": 7018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754309325210399, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325210505, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325211348, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325211974, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325212878, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754309325212610, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754309325213218, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325213495, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325213619, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325213813, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325214060, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325214150, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325214485, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325214800, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325215124, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325215633, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325215713, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325216172, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325216478, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325216894, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325217213, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754309325217615, "dur": 208377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325190423, "dur": 10854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325201301, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754309325201490, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1754309325201280, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_573913C1FF268A2A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754309325201776, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325201884, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754309325201882, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8182EAE46932F6EA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754309325201956, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754309325201954, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_22170A5568CA4919.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754309325202011, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325202342, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_22170A5568CA4919.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754309325202442, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754309325202440, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_5768D94A2A501EAC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754309325202672, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_5A4DAB7FB6024184.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754309325202799, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754309325203304, "dur": 622, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754309325203927, "dur": 966, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_EditorPanel.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754309325203927, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325205099, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325205347, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325205579, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325205804, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325206031, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325206275, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325206506, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325206720, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325207032, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325207254, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325207893, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325208119, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325208616, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325209209, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325209856, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325210380, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325210510, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325211350, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325211973, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325212645, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754309325213166, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325213243, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/SkyMavis.Waypoint.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754309325213363, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325213526, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325213613, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325213817, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325213931, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325214004, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325214179, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325214377, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325214805, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325215142, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325215729, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325216173, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325216498, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325216917, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325217210, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325217590, "dur": 20665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754309325238256, "dur": 187723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325190415, "dur": 10856, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325201288, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325201476, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1754309325201274, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4A99E7ADB31DB747.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325201711, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325201777, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325201775, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E74EDDF9CB117B30.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325201857, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E74EDDF9CB117B30.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202043, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202041, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4CE912B968344AF2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202130, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202128, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_B1F49F509BF34BB0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202226, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202224, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_EEC6715D6C078ED4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202347, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202431, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202345, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_C49F1B1E1B36C13A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202557, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202555, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_C6B909213A0BCD59.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325202798, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754309325203109, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754309325203305, "dur": 634, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754309325203941, "dur": 1545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\DropdownOptionListDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754309325203941, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325205692, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325205919, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325206186, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325206402, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325206829, "dur": 1439, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerStay2DMListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754309325206622, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325208269, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325208521, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325209317, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754309325208745, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325210167, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325210383, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325210523, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325211350, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325212004, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325212138, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754309325212825, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325212885, "dur": 494, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754309325213411, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754309325213652, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754309325214620, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325214806, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325215137, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325215733, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325216181, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325216482, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325216897, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325217223, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754309325217594, "dur": 208377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325190454, "dur": 10829, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325201309, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201447, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201286, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7F3FD0BA415121F8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201636, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7C14B74751CEB14A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201708, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201707, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5735A0C9A2F205F9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201803, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201802, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_31A9EFA363AB3C83.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201910, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754309325201908, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C8D7D3E3F1064FA2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202035, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325202210, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9C4E84A9B52BE867.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202351, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202349, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_BFCB0FCACAF77119.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202412, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325202516, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202514, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C7B7FEB7CB3B6160.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202645, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202643, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325202897, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754309325203097, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754309325203309, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325203537, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325203765, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325204240, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325204484, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325204719, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325205092, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325205350, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325205621, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325205839, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325206080, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325206298, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325206537, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325206758, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325207026, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325207242, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325207868, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325208123, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325208367, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325208544, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325208767, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325209262, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325209396, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325210180, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325210376, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325210500, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325211368, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325212002, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754309325212150, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754309325212733, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754309325213439, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325213573, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325213655, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325213807, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325213992, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325214165, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325214372, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325214810, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325215109, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325215611, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325215745, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325216197, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325216478, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325216902, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325217211, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754309325217598, "dur": 208404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325190474, "dur": 10816, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325201324, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201448, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201294, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FF05A8CB0537AF9E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201646, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_D8ECB7F659D29723.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201716, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201714, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_CFCACFCB0B6C8B75.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201794, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325201852, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_CFCACFCB0B6C8B75.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201922, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325201920, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_DF7D9FD794808934.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202006, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202005, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_76FFDB8825EAACD8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202101, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202099, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_845FBD1B5AF442B3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202188, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202187, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_D99B253E5C16B06E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202266, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202265, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_167389B99B5142D0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202348, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202347, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_AC815CCA2EC74CAA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202426, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202425, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_C0537938892DEAF6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325202830, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754309325203334, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325203552, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325203765, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325204232, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325204448, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325204686, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325205052, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325205285, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325205549, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325205786, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325206083, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325206310, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325206531, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325206748, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325206991, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325207215, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325207854, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325208078, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325208326, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325208535, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325208769, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325208989, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325209212, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325209700, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325210401, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325210502, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325211367, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325212005, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325212600, "dur": 468, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageFormats\\OpenCover\\Model\\File.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754309325212146, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754309325213128, "dur": 789, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325213966, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754309325214373, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754309325214237, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754309325215139, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325215606, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325215738, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325216184, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325216496, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325216908, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325217216, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754309325217607, "dur": 208391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325190494, "dur": 10803, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325201312, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754309325201443, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1754309325201301, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6BBAA4C46A2C16BA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325201605, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325201779, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WebGLModule.dll_2B23D4946A599CB6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325201852, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WebGLModule.dll_2B23D4946A599CB6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325201980, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754309325201978, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_1C70BFBBAC69830A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202105, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202104, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D55CC3D658EA7477.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202243, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_02E770FC7B807D62.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202359, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202428, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202357, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E4099628EC59A0DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202542, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202540, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_8415DF9E9D2C944F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202625, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325202687, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202685, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5CE5D6336EE197AE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325202825, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754309325203163, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325203497, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325203731, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325204223, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325204497, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325205128, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325205356, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325205606, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325205831, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325206084, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325206320, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325206532, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325206751, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325206983, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325207288, "dur": 990, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\ITextPreProcessor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754309325207205, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325208463, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\TestState.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754309325208407, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325209240, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325209729, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325210375, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325210499, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325211351, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325212008, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325212605, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754309325213453, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\QueryVisualElementsExtensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754309325212172, "dur": 1908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754309325214080, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325214175, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754309325214312, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754309325214815, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325215110, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325215632, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325215714, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325216172, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325216483, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325216896, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325217204, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754309325217592, "dur": 208400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325190521, "dur": 10783, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325201325, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754309325201440, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1754309325201307, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_014553A2310FB6CF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325201634, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754309325201632, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_7B7D5A6F18794603.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325201720, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754309325201718, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1D18347A9CFA8727.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202015, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_67049C814DF7041C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202110, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202108, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_CFA1A966D9233765.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202280, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_70474FFD2EE073C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202353, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4221B8E55F8CA2CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202424, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202423, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BF7B0BE0213AA4AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325202898, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BF7B0BE0213AA4AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325203314, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325203601, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325204324, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325204992, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325205226, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325205575, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325205810, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325206131, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325206355, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325206574, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325206797, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325207042, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325207265, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325207895, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325208231, "dur": 1503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\Processor\\AssetModificationProcessor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754309325208128, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325209854, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325210374, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325210501, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325211347, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325211978, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325212607, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/SkyMavis.Waypoint.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754309325213057, "dur": 444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754309325212736, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/SkyMavis.Waypoint.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754309325213718, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/SkyMavis.Waypoint.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754309325214227, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325214382, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325214785, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325215101, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325215638, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325215709, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325216170, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325216480, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325216892, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325217206, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754309325217592, "dur": 208383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325190546, "dur": 10765, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325201331, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754309325201459, "dur": 403, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1754309325201315, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_BCAB8C8433680ED4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325201886, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754309325201885, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_39172F89F493CAD5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325201963, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F84AFC1EA875A931.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325202035, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754309325202033, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3F588C0A7C6C07D3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325202362, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754309325202360, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_21DF6E33E020BB3D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325202423, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325202866, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754309325203099, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754309325203156, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325203316, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325203552, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325203875, "dur": 841, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Animation\\ClipCurveEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754309325204750, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Animation\\AnimationTrackActions.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754309325203786, "dur": 1718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325205504, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325205735, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325206035, "dur": 1193, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_4.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754309325207294, "dur": 966, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_0.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754309325205951, "dur": 2363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325208314, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325208522, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325208738, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325209474, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325209931, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325210378, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325210510, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325211353, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325212003, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325212140, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754309325212742, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325212896, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325213081, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754309325213823, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325213999, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325214170, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325214378, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325214804, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325215146, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325215719, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325216178, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325216512, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754309325216595, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754309325216909, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325217212, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325217590, "dur": 18485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325236077, "dur": 2175, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754309325238252, "dur": 187760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754309325190563, "dur": 10800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754309325201381, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201444, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201367, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0A4F51C79A281E51.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201652, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201726, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201650, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_5AB0D3C74155ED7A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201877, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_5AB0D3C74155ED7A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201957, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325201955, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_E8070D706152CF3C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325202108, "dur": 767, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325202107, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_914656F6FBA8F5C6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325202888, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_914656F6FBA8F5C6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325203120, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325203681, "dur": 1338, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325205056, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325205141, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325205389, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325205441, "dur": 623, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325206066, "dur": 789, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325206906, "dur": 2956, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754309325203437, "dur": 7002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754309325210517, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325210623, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754309325211363, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325211479, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754309325211999, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325212603, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\NoAllocEnumerator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754309325212128, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754309325213906, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754309325214021, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325214149, "dur": 1394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754309325215624, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754309325215747, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754309325216186, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754309325216484, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754309325216893, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754309325217230, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754309325217602, "dur": 208370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325190596, "dur": 10775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325201391, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325201465, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1754309325201375, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F5A15826918C0F2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325201634, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325201725, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325201717, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4E3884B8FC8ED343.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325201812, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4E3884B8FC8ED343.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325201918, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325201917, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5577D3E1632C1B08.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325202135, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325202229, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325202227, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_177D521712D70A28.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325202436, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325202435, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_78F35D34EC6436FD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325202697, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325202695, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_AE499E41BD2D7290.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325202780, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325202861, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754309325203313, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325203620, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325204098, "dur": 788, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_SpriteAssetMenu.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754309325203866, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325205107, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325205328, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325205581, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325205803, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325206022, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325206259, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325206483, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325206702, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325206959, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325207189, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325207813, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325208054, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325208297, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325208517, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325208737, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325209476, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325209630, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325210373, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325210520, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325211348, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325211972, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325212605, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325213453, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325213207, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754309325213860, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754309325214058, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325214241, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754309325215723, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325215849, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754309325216510, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325216646, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754309325217219, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325217305, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754309325217606, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754309325217692, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754309325218381, "dur": 191633, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754309325415122, "dur": 9931, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325415120, "dur": 9935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754309325425074, "dur": 872, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754309325190627, "dur": 10750, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325201395, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754309325201458, "dur": 388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1754309325201380, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_3FF536AA263283DD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754309325201906, "dur": 624, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754309325201905, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_51138DD0E69FA6F7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754309325202863, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754309325203180, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754309325203943, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325204135, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325204379, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325204620, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325204993, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325205258, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325205524, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325205762, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325205991, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325206259, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325206487, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325206717, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325206969, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325207191, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325207857, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325208099, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325208329, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325208547, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325208760, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325209775, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325210400, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325210498, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325211346, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325212029, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754309325212242, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754309325212422, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754309325212573, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754309325213642, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754309325214295, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325214357, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325214531, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325214811, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325215138, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325215627, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325215756, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325216175, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325216477, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325216904, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325217207, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325217591, "dur": 197533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754309325415127, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754309325415126, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754309325415305, "dur": 3307, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754309325418616, "dur": 7361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325190648, "dur": 10736, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325201398, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754309325201451, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1754309325201385, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_339523FD88F58CE5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754309325201724, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754309325201723, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_840091757717660B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754309325202025, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754309325202024, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_AEBE2A22DC1E5B26.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754309325202208, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325202357, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754309325202356, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EF20AB2F56E66DDF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754309325202411, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325202528, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FB383BF5D7743A79.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754309325202693, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754309325202692, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_A28F69477662F91E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754309325203096, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754309325203336, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325203582, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325203807, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325204720, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325205487, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325205722, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325206041, "dur": 903, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_3_0.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754309325205938, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325207055, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325207271, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325207906, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325208171, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325208421, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325208644, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325209558, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325210379, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325210506, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325211345, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325212001, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/2000b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754309325212144, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754309325212560, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325212883, "dur": 579, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754309325213468, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325213532, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325213592, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325213751, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325213806, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325214003, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325214168, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325214387, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325214781, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325215105, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325215637, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325215710, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325216171, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325216479, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325216893, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325217205, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754309325217593, "dur": 208385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754309325435354, "dur": 1535, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 11208, "tid": 1807, "ts": 1754309325438466, "dur": 438, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 11208, "tid": 1807, "ts": 1754309325438939, "dur": 8368, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 11208, "tid": 1807, "ts": 1754309325437105, "dur": 10240, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}