{"WebGLPlayerBuildProgram.FrameworkAssembler+Data": {"threadsSupport": false, "source": "Library/Bee/artifacts/WebGL/build/debug_WebGL_wasm/build.js", "jsprePlugins": []}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "WebGLPlayerBuildProgram.FrameworkAssembler", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\WebGLPlayerBuildProgram.exe", "targets": ["Library/Bee/artifacts/WebGL/build/debug_WebGL_wasm/build.framework.js"], "inputs": ["Library/Bee/artifacts/WebGL/build/debug_WebGL_wasm/build.js"], "targetDirectories": []}}