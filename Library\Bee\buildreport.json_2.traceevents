{ "pid": 11208, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913130648, "dur": 14110, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913144759, "dur": 50882604, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913144770, "dur": 24, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913144797, "dur": 40749, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913185556, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913185560, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913185604, "dur": 12, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913185617, "dur": 2238, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913187860, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913187893, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913187895, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913187917, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913187938, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913187959, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913187984, "dur": 17, "ph": "X", "name": "ReadAsync 500", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188004, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188030, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188032, "dur": 32, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188066, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188068, "dur": 22, "ph": "X", "name": "ReadAsync 542", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188093, "dur": 19, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188114, "dur": 60, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188179, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188210, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188211, "dur": 22, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188237, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188259, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188261, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188288, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188290, "dur": 32, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188324, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188326, "dur": 16, "ph": "X", "name": "ReadAsync 532", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188346, "dur": 18, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188367, "dur": 10, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188380, "dur": 15, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188398, "dur": 18, "ph": "X", "name": "ReadAsync 106", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188418, "dur": 31, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188452, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188473, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188493, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188519, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188538, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188558, "dur": 19, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188579, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188581, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188601, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188621, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188638, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188657, "dur": 16, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188675, "dur": 15, "ph": "X", "name": "ReadAsync 61", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188693, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188711, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188730, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188754, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188773, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188790, "dur": 16, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188810, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188830, "dur": 114, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188947, "dur": 29, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188978, "dur": 1, "ph": "X", "name": "ProcessMessages 1624", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188980, "dur": 15, "ph": "X", "name": "ReadAsync 1624", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913188998, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189016, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189038, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189055, "dur": 15, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189072, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189093, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189110, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189133, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189152, "dur": 15, "ph": "X", "name": "ReadAsync 142", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189171, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189189, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189208, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189224, "dur": 15, "ph": "X", "name": "ReadAsync 273", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189242, "dur": 14, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189258, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189260, "dur": 26, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189288, "dur": 17, "ph": "X", "name": "ReadAsync 653", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189308, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189325, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189342, "dur": 16, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189360, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189380, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189398, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189416, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189436, "dur": 14, "ph": "X", "name": "ReadAsync 491", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189452, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189470, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189488, "dur": 14, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189505, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189522, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189540, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189557, "dur": 17, "ph": "X", "name": "ReadAsync 445", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189576, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189596, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189613, "dur": 14, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189630, "dur": 15, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189647, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189666, "dur": 14, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189683, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189700, "dur": 13, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189715, "dur": 16, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189733, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189789, "dur": 28, "ph": "X", "name": "ReadAsync 474", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189820, "dur": 2, "ph": "X", "name": "ProcessMessages 926", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189823, "dur": 26, "ph": "X", "name": "ReadAsync 926", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189850, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189852, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189874, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189893, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189894, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189917, "dur": 15, "ph": "X", "name": "ReadAsync 500", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189935, "dur": 14, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189952, "dur": 20, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189973, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189975, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913189993, "dur": 16, "ph": "X", "name": "ReadAsync 474", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190011, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190028, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190048, "dur": 17, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190067, "dur": 14, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190084, "dur": 17, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190104, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190123, "dur": 14, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190140, "dur": 16, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190158, "dur": 13, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190174, "dur": 18, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190194, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190212, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190232, "dur": 14, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190249, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190266, "dur": 14, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190284, "dur": 10, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190295, "dur": 12, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190310, "dur": 15, "ph": "X", "name": "ReadAsync 153", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190328, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190345, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190362, "dur": 12, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190377, "dur": 17, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190398, "dur": 15, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190415, "dur": 156, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190573, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190575, "dur": 53, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190630, "dur": 2, "ph": "X", "name": "ProcessMessages 3569", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190633, "dur": 20, "ph": "X", "name": "ReadAsync 3569", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190655, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190657, "dur": 16, "ph": "X", "name": "ReadAsync 445", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190675, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190694, "dur": 15, "ph": "X", "name": "ReadAsync 177", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190712, "dur": 22, "ph": "X", "name": "ReadAsync 190", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190737, "dur": 14, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190754, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190778, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190799, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190801, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190825, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190827, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190848, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190850, "dur": 19, "ph": "X", "name": "ReadAsync 536", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190872, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190892, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190910, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190912, "dur": 19, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190932, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190934, "dur": 16, "ph": "X", "name": "ReadAsync 536", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190953, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190974, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913190996, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191014, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191016, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191034, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191050, "dur": 15, "ph": "X", "name": "ReadAsync 146", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191067, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191094, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191095, "dur": 19, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191116, "dur": 14, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191132, "dur": 16, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191151, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191170, "dur": 25, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191198, "dur": 52, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191254, "dur": 21, "ph": "X", "name": "ReadAsync 535", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191277, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191278, "dur": 17, "ph": "X", "name": "ReadAsync 938", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191298, "dur": 19, "ph": "X", "name": "ReadAsync 491", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191318, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191320, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191339, "dur": 15, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191357, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191376, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191395, "dur": 17, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191415, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191434, "dur": 12, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191449, "dur": 15, "ph": "X", "name": "ReadAsync 72", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191467, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191484, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191503, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191519, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191538, "dur": 14, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191555, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191572, "dur": 14, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191589, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191609, "dur": 15, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191627, "dur": 14, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191643, "dur": 14, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191659, "dur": 14, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191676, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191696, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191715, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191737, "dur": 16, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191757, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191778, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191779, "dur": 20, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191802, "dur": 15, "ph": "X", "name": "ReadAsync 514", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191819, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191837, "dur": 55, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191894, "dur": 1, "ph": "X", "name": "ProcessMessages 1337", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191896, "dur": 16, "ph": "X", "name": "ReadAsync 1337", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191914, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191915, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191937, "dur": 15, "ph": "X", "name": "ReadAsync 514", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191955, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191975, "dur": 15, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913191992, "dur": 14, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192009, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192027, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192044, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192062, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192079, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192100, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192116, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192137, "dur": 15, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192154, "dur": 15, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192172, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192189, "dur": 13, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192204, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192206, "dur": 15, "ph": "X", "name": "ReadAsync 165", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192223, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192242, "dur": 14, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192259, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192276, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192295, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192313, "dur": 13, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192329, "dur": 17, "ph": "X", "name": "ReadAsync 169", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192348, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192350, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192369, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192370, "dur": 9, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192381, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192399, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192416, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192436, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192455, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192473, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192492, "dur": 14, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192509, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192527, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192544, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192561, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192579, "dur": 12, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192594, "dur": 15, "ph": "X", "name": "ReadAsync 73", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192613, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192632, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192650, "dur": 15, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192668, "dur": 14, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192684, "dur": 16, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192703, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192721, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192742, "dur": 30, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192775, "dur": 15, "ph": "X", "name": "ReadAsync 576", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192793, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192814, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192833, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192850, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192870, "dur": 14, "ph": "X", "name": "ReadAsync 497", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192887, "dur": 14, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192904, "dur": 18, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192925, "dur": 15, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192943, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192960, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913192982, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193000, "dur": 15, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193017, "dur": 15, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193034, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193052, "dur": 14, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193069, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193086, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193103, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193123, "dur": 16, "ph": "X", "name": "ReadAsync 509", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193142, "dur": 12, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193157, "dur": 15, "ph": "X", "name": "ReadAsync 127", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193174, "dur": 15, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193191, "dur": 15, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193209, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193227, "dur": 15, "ph": "X", "name": "ReadAsync 522", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193244, "dur": 16, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193263, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193279, "dur": 17, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193300, "dur": 16, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193317, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193318, "dur": 13, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193334, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193352, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193370, "dur": 15, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193388, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193407, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193423, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193441, "dur": 14, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193458, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193478, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193494, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193497, "dur": 13, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193513, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193530, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193547, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193564, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193581, "dur": 14, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193597, "dur": 15, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193614, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193631, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193649, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193666, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193685, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193702, "dur": 14, "ph": "X", "name": "ReadAsync 104", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193718, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193737, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193756, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193775, "dur": 17, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193794, "dur": 15, "ph": "X", "name": "ReadAsync 555", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193811, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193812, "dur": 16, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193831, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193849, "dur": 12, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193863, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193897, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193914, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193931, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193949, "dur": 13, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193963, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193965, "dur": 14, "ph": "X", "name": "ReadAsync 113", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913193982, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194000, "dur": 12, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194014, "dur": 14, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194031, "dur": 18, "ph": "X", "name": "ReadAsync 252", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194052, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194068, "dur": 15, "ph": "X", "name": "ReadAsync 206", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194086, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194107, "dur": 16, "ph": "X", "name": "ReadAsync 263", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194125, "dur": 14, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194142, "dur": 14, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194159, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194178, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194196, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194214, "dur": 18, "ph": "X", "name": "ReadAsync 230", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194234, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913194236, "dur": 15638, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209879, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209881, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209918, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209922, "dur": 31, "ph": "X", "name": "ReadAsync 228", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209956, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209960, "dur": 27, "ph": "X", "name": "ReadAsync 172", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209990, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913209993, "dur": 30, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913210026, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913210029, "dur": 24, "ph": "X", "name": "ReadAsync 204", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913210055, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913210057, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913210081, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913210083, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913210109, "dur": 2592, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913212705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913212707, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913212729, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913212731, "dur": 1458, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214193, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214215, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214256, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214271, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214286, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214306, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214325, "dur": 574, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214904, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913214931, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913215001, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913215020, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913215197, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913215221, "dur": 1157, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913216382, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913216402, "dur": 1983, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913218389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913218391, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913218420, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913218422, "dur": 1256, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913219683, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913219708, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913219710, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913219893, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913219922, "dur": 307, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220234, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220259, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220261, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220425, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220440, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220616, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220642, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220644, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220713, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220740, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913220741, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221090, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221116, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221139, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221141, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221166, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221236, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221260, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221263, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221314, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221348, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221351, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221409, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221431, "dur": 390, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221827, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913221853, "dur": 795, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222653, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222674, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222692, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222728, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222748, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222750, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222768, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222801, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222825, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222924, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913222940, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223095, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223097, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223112, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223140, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223158, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223194, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223214, "dur": 188, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223405, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223426, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223469, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223488, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223543, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223562, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223616, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223638, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223660, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223662, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223774, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223796, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223943, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223945, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223969, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913223990, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224063, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224084, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224086, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224106, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224125, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224157, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224179, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224312, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224335, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224356, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224358, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224402, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224424, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224442, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224478, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224504, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224519, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224567, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224597, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224685, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224703, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224706, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224736, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224759, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224760, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224888, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224906, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224908, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224925, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913224947, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225001, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225015, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225029, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225044, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225060, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225271, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225292, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225343, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225363, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225443, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225464, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225482, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225500, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225526, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225543, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225545, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225564, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225565, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225634, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225651, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225750, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913225770, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226023, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226039, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226061, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226080, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226099, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226119, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226140, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226157, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226186, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226205, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226272, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226292, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226294, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226338, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226354, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226378, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226393, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226463, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226480, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226538, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226556, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226573, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226592, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226737, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226756, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226793, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226814, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226816, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226897, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226915, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226917, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226953, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913226974, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227033, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227051, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227054, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227096, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227113, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227129, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227264, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227281, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227314, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227333, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227406, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227422, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227475, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227493, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227536, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227558, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227560, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227580, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227638, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227658, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227660, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227708, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227725, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227750, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227768, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227770, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227851, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227868, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227907, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227924, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227976, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913227993, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228043, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228060, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228179, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228196, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228212, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228261, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228277, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228340, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228358, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228378, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228397, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228472, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228487, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228506, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228508, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228546, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228561, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228584, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228599, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228632, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228648, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228753, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228769, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228789, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228887, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228906, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913228989, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229005, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229056, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229074, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229091, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229115, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229132, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229185, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229201, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229215, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229218, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229241, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229505, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229528, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229530, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229610, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229637, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229727, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229750, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229775, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229798, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229800, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229905, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229927, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229929, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229950, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913229952, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230014, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230033, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230098, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230121, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230198, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230211, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230241, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230256, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230294, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230310, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230311, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230409, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230431, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230456, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230476, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230504, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230523, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230525, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230549, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230550, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230587, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230607, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230624, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230626, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230723, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230747, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230750, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230773, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230775, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230883, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230911, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230913, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230966, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913230988, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231070, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231105, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231251, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231276, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231522, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231524, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231550, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231571, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231573, "dur": 276, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231854, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231873, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231935, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231958, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913231960, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232006, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232030, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232104, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232127, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232266, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232292, "dur": 347, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232644, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232666, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232697, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232721, "dur": 10, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232733, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232762, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232788, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232790, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232809, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232828, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913232829, "dur": 261, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233095, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233118, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233120, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233148, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233151, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233277, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233304, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233305, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233461, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233483, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233485, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233507, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233526, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233528, "dur": 247, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233780, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233810, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913233813, "dur": 243, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234061, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234082, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234084, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234151, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234176, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234178, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234366, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234390, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234393, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234417, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234530, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234552, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234581, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234605, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234607, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234648, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234671, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234759, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234778, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234780, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234841, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913234867, "dur": 185, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235058, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235081, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235159, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235179, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235246, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235271, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235611, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235635, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235637, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235666, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235668, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235895, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235917, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235919, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235946, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235970, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913235972, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236003, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236037, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236064, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236107, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236132, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236157, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236159, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236184, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236210, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236212, "dur": 328, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236544, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236572, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236574, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236598, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236600, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236624, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236626, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236641, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236678, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236699, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236700, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236725, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236747, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236749, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236778, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236799, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236844, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236871, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236874, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236897, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236898, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236926, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913236928, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237049, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237074, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237076, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237102, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237193, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237223, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237225, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237252, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237254, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237276, "dur": 187, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237469, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237496, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237498, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237536, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237557, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237559, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237585, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237587, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237613, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237695, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237722, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237724, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237749, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237751, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237778, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237941, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237966, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913237968, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238014, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238035, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238067, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238087, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238089, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238111, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238138, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238160, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238190, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238212, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238214, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238239, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238261, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238299, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238317, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238338, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238356, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238375, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238398, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238400, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238421, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238423, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238442, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238530, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238568, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238571, "dur": 413, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913238989, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239012, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239015, "dur": 223, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239243, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239264, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239266, "dur": 168, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239439, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239464, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239466, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239519, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239542, "dur": 391, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239936, "dur": 25, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239964, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913239967, "dur": 46, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913240017, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913240038, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913240041, "dur": 1698, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241746, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241773, "dur": 7, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241782, "dur": 63, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241849, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241876, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241881, "dur": 38, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241924, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241948, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241951, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913241997, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242019, "dur": 7, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242028, "dur": 253, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242285, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242312, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242314, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242369, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242392, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913242395, "dur": 769, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243167, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243169, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243196, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243201, "dur": 159, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243365, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243385, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243387, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243454, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243475, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913243480, "dur": 755, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913244241, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913244263, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913244268, "dur": 3373, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913247649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913247652, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913247683, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913247686, "dur": 333, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248025, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248057, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248060, "dur": 237, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248303, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248330, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248333, "dur": 344, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248682, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248708, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913248721, "dur": 299, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249023, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249025, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249055, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249063, "dur": 306, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249374, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249403, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913249408, "dur": 1079, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913250492, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913250494, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913250519, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913250522, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913250841, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913250865, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913250867, "dur": 1127, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913251999, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913252026, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913252029, "dur": 2446, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254482, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254486, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254514, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254518, "dur": 245, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254766, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254768, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254796, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913254799, "dur": 453, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913255255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913255257, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913255284, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913255295, "dur": 262, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913255563, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913255590, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913255598, "dur": 5615, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913261221, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913261226, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913261264, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913261269, "dur": 1100, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913262372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913262374, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913262406, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913262419, "dur": 86702, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913349135, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913349141, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913349174, "dur": 13, "ph": "X", "name": "ProcessMessages 1618", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913349188, "dur": 10306, "ph": "X", "name": "ReadAsync 1618", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913359504, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913359507, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913359530, "dur": 19, "ph": "X", "name": "ProcessMessages 494", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913359551, "dur": 6737, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913366293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913366296, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913366327, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913366331, "dur": 42671, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913409014, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913409019, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913409045, "dur": 14, "ph": "X", "name": "ProcessMessages 1594", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913409112, "dur": 91480, "ph": "X", "name": "ReadAsync 1594", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913500602, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913500607, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913500627, "dur": 15, "ph": "X", "name": "ProcessMessages 493", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913500643, "dur": 199, "ph": "X", "name": "ReadAsync 493", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913500845, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913500870, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913500873, "dur": 2070, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913502947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913502949, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913503102, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309913503117, "dur": 50228598, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963731724, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963731730, "dur": 156, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963731888, "dur": 12, "ph": "X", "name": "ProcessMessages 217", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963731901, "dur": 952, "ph": "X", "name": "ReadAsync 217", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963732859, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963732884, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963732887, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963732905, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963732906, "dur": 6468, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963739386, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963739389, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963739412, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309963739426, "dur": 272627, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964012065, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964012069, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964012098, "dur": 24, "ph": "X", "name": "ProcessMessages 501", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964012125, "dur": 154, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964012285, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964012311, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964012314, "dur": 839, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964013159, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964013205, "dur": 6, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964013213, "dur": 456, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964013673, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964013692, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11208, "tid": 38654705664, "ts": 1754309964013694, "dur": 13656, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964027774, "dur": 1990, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911068253, "dur": 15789, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911084044, "dur": 42825, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911084054, "dur": 23, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911084080, "dur": 13959, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911098050, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911098054, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911098077, "dur": 8, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911098086, "dur": 8385, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106481, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106528, "dur": 2, "ph": "X", "name": "ProcessMessages 823", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106532, "dur": 17, "ph": "X", "name": "ReadAsync 823", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106553, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106580, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106582, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106613, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106615, "dur": 23, "ph": "X", "name": "ReadAsync 588", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106641, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106643, "dur": 25, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106672, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106695, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106697, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106721, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106723, "dur": 26, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106752, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106755, "dur": 31, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106789, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106791, "dur": 55, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106850, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106879, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106881, "dur": 30, "ph": "X", "name": "ReadAsync 450", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106913, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106915, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106940, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106943, "dur": 23, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106968, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106972, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106996, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911106997, "dur": 18, "ph": "X", "name": "ReadAsync 532", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107018, "dur": 16, "ph": "X", "name": "ReadAsync 475", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107036, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107038, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107061, "dur": 17, "ph": "X", "name": "ReadAsync 515", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107081, "dur": 482, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107566, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107592, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107593, "dur": 17, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107613, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107633, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107635, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107656, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107678, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107696, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107715, "dur": 20, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107737, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107738, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107755, "dur": 15, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107771, "dur": 15, "ph": "X", "name": "ReadAsync 250", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107789, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107807, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107829, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107848, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107866, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107884, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107906, "dur": 17, "ph": "X", "name": "ReadAsync 478", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107925, "dur": 15, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107943, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107961, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911107978, "dur": 36, "ph": "X", "name": "ReadAsync 157", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108017, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108040, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108042, "dur": 23, "ph": "X", "name": "ReadAsync 539", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108067, "dur": 15, "ph": "X", "name": "ReadAsync 532", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108084, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108086, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108108, "dur": 17, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108128, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108145, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108162, "dur": 16, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108182, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108201, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108220, "dur": 16, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108239, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108258, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108276, "dur": 15, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108293, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108311, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108330, "dur": 16, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108348, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108368, "dur": 14, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108385, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108403, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108419, "dur": 17, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108438, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108457, "dur": 15, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108475, "dur": 16, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108493, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108509, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108531, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108551, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108553, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108573, "dur": 15, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108591, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108609, "dur": 15, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108627, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108653, "dur": 14, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108670, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108688, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108707, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108725, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108741, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108761, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108782, "dur": 14, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108799, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108816, "dur": 21, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108840, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108866, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108867, "dur": 18, "ph": "X", "name": "ReadAsync 539", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108924, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108926, "dur": 36, "ph": "X", "name": "ReadAsync 531", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108964, "dur": 3, "ph": "X", "name": "ProcessMessages 1061", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108969, "dur": 20, "ph": "X", "name": "ReadAsync 1061", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108991, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911108992, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109012, "dur": 18, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109033, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109050, "dur": 17, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109069, "dur": 15, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109088, "dur": 29, "ph": "X", "name": "ReadAsync 151", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109119, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109145, "dur": 19, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109167, "dur": 15, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109185, "dur": 17, "ph": "X", "name": "ReadAsync 230", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109204, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109224, "dur": 14, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109241, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109260, "dur": 16, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109278, "dur": 13, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109294, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109311, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109328, "dur": 35, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109366, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109393, "dur": 17, "ph": "X", "name": "ReadAsync 519", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109412, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109432, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109434, "dur": 19, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109454, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109456, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109478, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109496, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109518, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109538, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109556, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109573, "dur": 16, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109591, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109611, "dur": 14, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109627, "dur": 3, "ph": "X", "name": "ProcessMessages 270", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109630, "dur": 15, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109648, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109673, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109692, "dur": 14, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109709, "dur": 15, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109727, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109745, "dur": 13, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109760, "dur": 15, "ph": "X", "name": "ReadAsync 155", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109777, "dur": 16, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109795, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109812, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109813, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109832, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109849, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109868, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109869, "dur": 17, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109889, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109907, "dur": 14, "ph": "X", "name": "ReadAsync 273", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109925, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109945, "dur": 16, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109964, "dur": 15, "ph": "X", "name": "ReadAsync 177", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109981, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911109998, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110018, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110037, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110057, "dur": 91, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110151, "dur": 1, "ph": "X", "name": "ProcessMessages 1700", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110153, "dur": 13, "ph": "X", "name": "ReadAsync 1700", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110168, "dur": 17, "ph": "X", "name": "ReadAsync 167", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110188, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110207, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110223, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110241, "dur": 29, "ph": "X", "name": "ReadAsync 526", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110272, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110290, "dur": 15, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110308, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110325, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110345, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110347, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110365, "dur": 15, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110382, "dur": 13, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110398, "dur": 17, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110418, "dur": 15, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110436, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110452, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110455, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110473, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110491, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110509, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110526, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110549, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110569, "dur": 15, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110586, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110603, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110621, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110639, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110657, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110676, "dur": 15, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110693, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110716, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110736, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110756, "dur": 16, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110775, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110793, "dur": 15, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110811, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110828, "dur": 14, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110845, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110865, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110883, "dur": 17, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110902, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110904, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110926, "dur": 19, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110947, "dur": 11, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110960, "dur": 13, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110975, "dur": 16, "ph": "X", "name": "ReadAsync 267", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911110995, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111011, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111029, "dur": 18, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111049, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111068, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111085, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111103, "dur": 15, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111121, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111143, "dur": 15, "ph": "X", "name": "ReadAsync 509", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111161, "dur": 14, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111178, "dur": 14, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111195, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111213, "dur": 15, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111230, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111248, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111265, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111282, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111300, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111317, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111340, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111360, "dur": 14, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111378, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111395, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111423, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111425, "dur": 16, "ph": "X", "name": "ReadAsync 536", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111444, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111465, "dur": 14, "ph": "X", "name": "ReadAsync 445", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111480, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111483, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111502, "dur": 15, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111520, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111537, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111555, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111571, "dur": 14, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111588, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111609, "dur": 14, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111626, "dur": 17, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111646, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111667, "dur": 15, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111685, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111705, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111725, "dur": 14, "ph": "X", "name": "ReadAsync 514", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111742, "dur": 15, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111759, "dur": 15, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111776, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111794, "dur": 15, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111811, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111831, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111849, "dur": 17, "ph": "X", "name": "ReadAsync 247", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111868, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111889, "dur": 21, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111912, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111914, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111940, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111943, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111969, "dur": 17, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911111989, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112009, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112030, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112050, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112051, "dur": 18, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112071, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112095, "dur": 17, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112114, "dur": 15, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112132, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112150, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112169, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112186, "dur": 18, "ph": "X", "name": "ReadAsync 204", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112207, "dur": 16, "ph": "X", "name": "ReadAsync 469", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112226, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112243, "dur": 16, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112262, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112279, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112296, "dur": 14, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112313, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112332, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112350, "dur": 15, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112368, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112385, "dur": 14, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112400, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112418, "dur": 15, "ph": "X", "name": "ReadAsync 419", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112436, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112453, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112470, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112489, "dur": 14, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112506, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112525, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112543, "dur": 16, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112562, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112579, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112599, "dur": 16, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112617, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112635, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112658, "dur": 15, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112675, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112693, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112713, "dur": 16, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112731, "dur": 15, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112749, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112767, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112785, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112807, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112832, "dur": 23, "ph": "X", "name": "ReadAsync 189", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112859, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112878, "dur": 18, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112899, "dur": 13, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112915, "dur": 14, "ph": "X", "name": "ReadAsync 188", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112931, "dur": 14, "ph": "X", "name": "ReadAsync 168", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112947, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112968, "dur": 16, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911112987, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113005, "dur": 19, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113029, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113055, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113057, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113086, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113087, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113107, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113130, "dur": 18, "ph": "X", "name": "ReadAsync 723", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113151, "dur": 15, "ph": "X", "name": "ReadAsync 251", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113169, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113187, "dur": 14, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113203, "dur": 41, "ph": "X", "name": "ReadAsync 138", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113248, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113274, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113275, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113299, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113316, "dur": 15, "ph": "X", "name": "ReadAsync 157", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113334, "dur": 15, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113351, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113368, "dur": 295, "ph": "X", "name": "ReadAsync 136", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113667, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113692, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113693, "dur": 23, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113719, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113740, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113742, "dur": 23, "ph": "X", "name": "ReadAsync 198", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113769, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113792, "dur": 15, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113810, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113829, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113849, "dur": 14, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113866, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113884, "dur": 19, "ph": "X", "name": "ReadAsync 285", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113905, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911113907, "dur": 4139, "ph": "X", "name": "ReadAsync 113", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911118051, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911118074, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11208, "tid": 34359738368, "ts": 1754309911118076, "dur": 8785, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964029767, "dur": 609, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11208, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 11208, "tid": 30064771072, "ts": 1754309911064401, "dur": 62502, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11208, "tid": 30064771072, "ts": 1754309911064532, "dur": 2801, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11208, "tid": 30064771072, "ts": 1754309911126945, "dur": 2000372, "ph": "X", "name": "await ExecuteBuildProgram", "args": {} },
{ "pid": 11208, "tid": 30064771072, "ts": 1754309913127436, "dur": 50899974, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11208, "tid": 30064771072, "ts": 1754309913127536, "dur": 2401, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11208, "tid": 30064771072, "ts": 1754309964027414, "dur": 17, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 11208, "tid": 30064771072, "ts": 1754309964027433, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964030378, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 11208, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 11208, "tid": 1, "ts": 1754309911002030, "dur": 1135, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11208, "tid": 1, "ts": 1754309911003169, "dur": 49823, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 11208, "tid": 1, "ts": 1754309911052995, "dur": 11378, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964030394, "dur": 6, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1754309912512460, "dur": 598430, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912513309, "dur": 47742, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912571694, "dur": 515025, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912571924, "dur": 489460, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912572490, "dur": 19282, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912592119, "dur": 412, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912617719, "dur": 116830, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912756382, "dur": 304211, "ph": "X", "name": "SetupIl2CppBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912757222, "dur": 206273, "ph": "X", "name": "SetupIl2Cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309912971524, "dur": 83160, "ph": "X", "name": "SetupSpecificConfigImpl GameAssembly", "args": {"info": "release_WebGL_wasm"} },
{ "pid": 35942, "tid": 1, "ts": 1754309912977886, "dur": 60327, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "release_WebGL_wasm"} },
{ "pid": 35942, "tid": 1, "ts": 1754309912978595, "dur": 37116, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "release_WebGL_wasm"} },
{ "pid": 35942, "tid": 1, "ts": 1754309913015741, "dur": 2595, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "release_WebGL_wasm"} },
{ "pid": 35942, "tid": 1, "ts": 1754309913061050, "dur": 333, "ph": "X", "name": "SetupCopyDataIl2cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309913066261, "dur": 13049, "ph": "X", "name": "SetupSpecificConfigImpl build", "args": {"info": "debug_WebGL_wasm"} },
{ "pid": 35942, "tid": 1, "ts": 1754309913092365, "dur": 305, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309913092671, "dur": 18217, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309913093821, "dur": 11323, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309913116293, "dur": 1745, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754309913115969, "dur": 2244, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754309911083807, "dur":15272, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911099089, "dur":6717, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911105899, "dur":280, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911106198, "dur":7449, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911113652, "dur":1209, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911114904, "dur":2538, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911117505, "dur":241, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911117802, "dur":5228, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754309911106473, "dur":7183, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309911113658, "dur":1199, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309911106503, "dur":7162, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309911113667, "dur":1738, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\BuildTools\\lib\\modules\\WebGLSupport_VehiclesModule_Dynamic.a" }}
,{ "pid":12345, "tid":2, "ts":1754309911113667, "dur":3750, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309911106534, "dur":7142, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309911113678, "dur":1741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\BuildTools\\lib\\modules\\WebGLSupport_ImageConversionModule_Dynamic.a" }}
,{ "pid":12345, "tid":3, "ts":1754309911113678, "dur":3748, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309911106495, "dur":7166, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309911114219, "dur":1159, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\BuildTools\\lib\\modules\\WebGLSupport_XRModule_Dynamic.a" }}
,{ "pid":12345, "tid":4, "ts":1754309911113664, "dur":2026, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309911106526, "dur":7145, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309911113673, "dur":1709, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\BuildTools\\lib\\modules\\WebGLSupport_TerrainModule_Dynamic.a" }}
,{ "pid":12345, "tid":5, "ts":1754309911113673, "dur":3668, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309911106554, "dur":7126, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309911113683, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\BuildTools\\prejs\\FullScreen.js" }}
,{ "pid":12345, "tid":6, "ts":1754309911113683, "dur":2454, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309911106580, "dur":7106, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309911113689, "dur":715, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\BuildTools\\lib\\Eval.js" }}
,{ "pid":12345, "tid":7, "ts":1754309911115270, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\InternalCallRegistrationWriter\\InternalCallRegistrationWriter.exe" }}
,{ "pid":12345, "tid":7, "ts":1754309911113689, "dur":2411, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309911106605, "dur":7091, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309911113699, "dur":640, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309911114339, "dur":989, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":8, "ts":1754309911114339, "dur":3010, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309911106629, "dur":7071, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309911113845, "dur":1313, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\adler32.c" }}
,{ "pid":12345, "tid":9, "ts":1754309911115159, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\BuildTools\\Emscripten\\emscripten\\emar.bat" }}
,{ "pid":12345, "tid":9, "ts":1754309911115894, "dur":730, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\extra\\gc.c" }}
,{ "pid":12345, "tid":9, "ts":1754309911113703, "dur":4017, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309911106653, "dur":7051, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309911113708, "dur":1617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.dll" }}
,{ "pid":12345, "tid":10, "ts":1754309911113707, "dur":3852, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309911106677, "dur":7043, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309911113722, "dur":1885, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":11, "ts":1754309911113722, "dur":3540, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309911106694, "dur":7030, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309911113724, "dur":1979, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754309911113724, "dur":3924, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309911125833, "dur":426, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754309913144688, "dur":42150, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309913186844, "dur":384, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309913187304, "dur":153, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754309913187457, "dur":279, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309913187757, "dur":6377, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309913194140, "dur":50818993, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309964013134, "dur":255, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309964013389, "dur":70, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309964013584, "dur":7293, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754309913187963, "dur":6180, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913194145, "dur":15589, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913210013, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913210078, "dur":118, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913210197, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\SkyMavis.Waypoint.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913210269, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913210428, "dur":210, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913210639, "dur":343, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913210983, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913211043, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1754309913211683, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Temp\\StagingArea\\Data\\Managed\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754309913211734, "dur":107, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Temp\\StagingArea\\Data\\Managed\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1754309913209809, "dur":2460, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker D:/Game Development/Interns/Basic Connect Wallet/Library/Bee/artifacts/unitylinker_ber2.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754309913212270, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913213331, "dur":550, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"IL2CPP_CodeGen D:/Game Development/Interns/Basic Connect Wallet/Library/Bee/artifacts/il2cpp_conv_3o6c.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754309913213882, "dur":1223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913216303, "dur":6029, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913222338, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913216295, "dur":6284, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/99690jwfo38u.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913222619, "dur":180, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Mono.Security.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913222807, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913222604, "dur":453, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/k1kz69c6nctv.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913223111, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Assembly-CSharp.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913223420, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\Internal\\Compiler\\CompilerEnvironmentClang.h" }}
,{ "pid":12345, "tid":1, "ts":1754309913223266, "dur":430, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913223075, "dur":621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vbkaep7nuvpm.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913223763, "dur":182, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__27.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913223953, "dur":420, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913223748, "dur":626, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/23umv7kdz9ny.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913224410, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913224562, "dur":367, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913224396, "dur":533, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/y2e19f19i6th.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913224958, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913225094, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913224944, "dur":351, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/9b4sy69ob340.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913225296, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913225361, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/aa0o42pnu07y.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913225457, "dur":1420, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__17.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913226895, "dur":323, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913225445, "dur":1773, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/w3jjw1mz6lrf.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913227238, "dur":108, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__13.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913227346, "dur":129, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__13.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913227512, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913227235, "dur":514, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/nqjum654io35.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913227771, "dur":704, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913228476, "dur":224, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913228710, "dur":257, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913227769, "dur":1198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/03tz56x5m7aj.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913228986, "dur":145, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913229132, "dur":306, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913229522, "dur":99, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm\\Runtime.h" }}
,{ "pid":12345, "tid":1, "ts":1754309913229447, "dur":467, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913228984, "dur":931, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/upaygtadrrke.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913229931, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/7bho0y288d7n.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913230019, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913230076, "dur":102, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913230186, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913230017, "dur":419, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vajj3isej5b3.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913230459, "dur":128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\__Generated.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913230587, "dur":138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\__Generated.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913230733, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913230457, "dur":499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/cg9kfmsryhje.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913230979, "dur":1414, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\it4wngfjo6pk1.lump.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913232419, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913230977, "dur":1716, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/kw8axufmnkh0.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913232729, "dur":711, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\5unry87cg5k20.lump.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913233448, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913232726, "dur":951, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/wvvywx3aactf.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913233698, "dur":854, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gi7kprgxmq3m2.lump.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913234560, "dur":326, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913233694, "dur":1193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/liti4hjd2jre.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913234912, "dur":782, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\tn7us4ioczrb2.lump.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913235708, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913234909, "dur":985, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/s4g5pz45tmf5.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913235920, "dur":505, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\gzwrite.c" }}
,{ "pid":12345, "tid":1, "ts":1754309913235917, "dur":525, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/2hrw8japj449.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913236493, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\utf8_util.c" }}
,{ "pid":12345, "tid":1, "ts":1754309913236598, "dur":80, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\utf8_util.c" }}
,{ "pid":12345, "tid":1, "ts":1754309913236490, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/4gd8n32k5fcd.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913236713, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\compress_fragment.c" }}
,{ "pid":12345, "tid":1, "ts":1754309913236794, "dur":286, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\compress_fragment.c" }}
,{ "pid":12345, "tid":1, "ts":1754309913237093, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913236710, "dur":441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/tasfamdahs2m.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913237171, "dur":238, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\infback.c" }}
,{ "pid":12345, "tid":1, "ts":1754309913237169, "dur":264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/u6epnckv22u3.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913237454, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm-utils\\icalls\\mscorlib\\System.Threading\\Interlocked.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913237592, "dur":127, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm-utils\\icalls\\mscorlib\\System.Threading\\Interlocked.cpp" }}
,{ "pid":12345, "tid":1, "ts":1754309913238013, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\Internal\\PlatformEnvironment.h" }}
,{ "pid":12345, "tid":1, "ts":1754309913237727, "dur":695, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913237451, "dur":971, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/f1x7ud4u0yst.o" }}
,{ "pid":12345, "tid":1, "ts":1754309913238450, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/MemoryProfiler.png" }}
,{ "pid":12345, "tid":1, "ts":1754309913238581, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754309913248213, "dur":1082, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/MemoryProfiler.png" }}
,{ "pid":12345, "tid":1, "ts":1754309913249342, "dur":50763765, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913187989, "dur":6163, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913194153, "dur":844, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913194997, "dur":2563, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913197560, "dur":691, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913198251, "dur":11490, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913209827, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MakeLump Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/gi7kprgxmq3m0.lump.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913209975, "dur":4783, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\69uny1oywgjt0.lump.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913209960, "dur":4836, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/070a1vs90gmp.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913214816, "dur":10324, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\9yue7g3pltg00.lump.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913225149, "dur":287, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913214813, "dur":10623, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/6r2lr5l1ga10.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913225469, "dur":225, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913225704, "dur":329, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913225453, "dur":580, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/rhwioqhhhusk.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913226054, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913226221, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913226523, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\Internal\\Assert.h" }}
,{ "pid":12345, "tid":2, "ts":1754309913226409, "dur":297, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913226051, "dur":655, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/46oh6b7sko7j.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913226751, "dur":150, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913226909, "dur":256, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913226727, "dur":438, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/pxtlt82zn2dk.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913227196, "dur":133, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__21.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913227336, "dur":275, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913227184, "dur":427, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/oxc1za3qfhfy.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913227642, "dur":183, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913227834, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913227629, "dur":450, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/caw0adocgd7p.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913228112, "dur":142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__20.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913228317, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Internal\\Baselib_ReentrantLock.inl.h" }}
,{ "pid":12345, "tid":2, "ts":1754309913228262, "dur":388, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913228099, "dur":551, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/npskfvmsbfyi.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913228707, "dur":225, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__4.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913229236, "dur":167, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Baselib_Thread.h" }}
,{ "pid":12345, "tid":2, "ts":1754309913228946, "dur":477, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913228671, "dur":752, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/fp0q5k0280ii.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913229423, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913229556, "dur":219, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913229783, "dur":311, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913229524, "dur":571, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/5e9xqrnroqoz.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913230136, "dur":1022, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913231220, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913230114, "dur":1333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/6bhrlbhx52m4.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913231479, "dur":1246, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\dxvayldvmw2q0.lump.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913232743, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913231476, "dur":1510, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/xxuq1ujxh9gz.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913233022, "dur":1135, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gi7kprgxmq3m5.lump.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913234167, "dur":304, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913233019, "dur":1452, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/aw5tdcgqr99t.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913234499, "dur":1030, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\c9dj0dxeq3ww0.lump.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913235538, "dur":260, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913234495, "dur":1303, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/z5g2jba2x7wf.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913235819, "dur":178, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\trees.c" }}
,{ "pid":12345, "tid":2, "ts":1754309913235998, "dur":438, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\trees.c" }}
,{ "pid":12345, "tid":2, "ts":1754309913235817, "dur":636, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/zwt47eqak76h.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913236475, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.Remoting\\RemotingServices.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913236473, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/l8a1hzoxak17.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913236644, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\encoder_dict.c" }}
,{ "pid":12345, "tid":2, "ts":1754309913236712, "dur":1133, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\encoder_dict.c" }}
,{ "pid":12345, "tid":2, "ts":1754309913237853, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913236641, "dur":1269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/32uo2umryf6n.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913237934, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\Mono.Net.Security\\MonoTlsProviderFactory.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913238029, "dur":98, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\Mono.Net.Security\\MonoTlsProviderFactory.cpp" }}
,{ "pid":12345, "tid":2, "ts":1754309913237931, "dur":219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/3somre1voffl.o" }}
,{ "pid":12345, "tid":2, "ts":1754309913238215, "dur":96, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\WebGL\\Data\\boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754309913238896, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\Playerf3bbbec4-inputdata.json" }}
,{ "pid":12345, "tid":2, "ts":1754309913238214, "dur":829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WebGL/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754309913239822, "dur":97, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754309913240276, "dur":119113, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WebGL/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754309913366053, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754309913366048, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Assemble WebGL Data Library/Bee/artifacts/WebGL/webgl.data" }}
,{ "pid":12345, "tid":2, "ts":1754309913366360, "dur":134128, "ph":"X", "name": "Assemble",  "args": { "detail":"WebGL Data Library/Bee/artifacts/WebGL/webgl.data" }}
,{ "pid":12345, "tid":2, "ts":1754309913500769, "dur":2092, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/Build/gdex.data" }}
,{ "pid":12345, "tid":2, "ts":1754309913502863, "dur":50510257, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913187983, "dur":6165, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913194150, "dur":633, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913194783, "dur":966, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913195749, "dur":1297, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913197046, "dur":864, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913197910, "dur":11842, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913209926, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MakeLump Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/c9dj0dxeq3ww1.lump.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913210001, "dur":11543, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\c9dj0dxeq3ww1.lump.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913221553, "dur":135, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913209983, "dur":11705, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/2orklj85tnda.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913221717, "dur":655, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__10.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913222380, "dur":262, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913221704, "dur":938, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/xhy9kvqfk56v.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913222676, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913222732, "dur":55, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913222671, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/48i2bpc0us1i.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913222892, "dur":116, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__31.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913223029, "dur":277, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913222851, "dur":456, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vzck8xs7hmet.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913223347, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913223486, "dur":263, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913223332, "dur":417, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/0pf63eu44ez3.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913223779, "dur":181, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913223967, "dur":345, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913223765, "dur":548, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/bbdo0pjusj6r.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913224342, "dur":237, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913224579, "dur":117, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913224716, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913224339, "dur":621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/8mhfm0fvsuqk.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913224995, "dur":132, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913225136, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913224978, "dur":401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/nmj50u1jhdgw.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913225405, "dur":118, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913225523, "dur":144, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913225675, "dur":255, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913225403, "dur":528, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/t7agun4xb1u7.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913225971, "dur":132, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913226111, "dur":265, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913225952, "dur":424, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/e20v048v7xsv.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913226407, "dur":102, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__10.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913226515, "dur":258, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913226392, "dur":382, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/gns167wb99yn.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913226774, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913227028, "dur":140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913227174, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913227013, "dur":369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/v56idumkdp7u.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913227400, "dur":978, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TilemapModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913227397, "dur":1047, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/s6o1a004zhif.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913228476, "dur":137, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913228739, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils\\dynamic_array.h" }}
,{ "pid":12345, "tid":3, "ts":1754309913228622, "dur":392, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913228464, "dur":551, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/io45hx6hh46u.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913229038, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/y6g7ca3vs42o.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913229132, "dur":337, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913229615, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Baselib_Atomic.h" }}
,{ "pid":12345, "tid":3, "ts":1754309913229484, "dur":338, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913229118, "dur":704, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/8l9xpmr9yme0.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913229856, "dur":138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913230003, "dur":351, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913229839, "dur":515, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/k3gjy1fukpza.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913230372, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913230470, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913230622, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913230369, "dur":482, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/1af8t36e945s.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913230873, "dur":946, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\7k4m4atixraq0.lump.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913231827, "dur":75, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913230870, "dur":1032, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/560hcbdn6bcw.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913231926, "dur":863, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\ijvfoiipf0bq0.lump.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913232796, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913231923, "dur":1099, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/njt4mkla059c.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913233044, "dur":1142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\wfmot0qq52z90.lump.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913234194, "dur":280, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913233040, "dur":1434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/puf6mzsruvz2.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913234501, "dur":895, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\tn7us4ioczrb1.lump.cpp" }}
,{ "pid":12345, "tid":3, "ts":1754309913235403, "dur":151, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913234498, "dur":1056, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/pl6mor61x6ya.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913235574, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\zutil.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913235640, "dur":179, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\zutil.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913235572, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/a4yyln9lsiki.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913235859, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\inftrees.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913236053, "dur":373, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\inftrees.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913235857, "dur":583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/syg6dp0l0m8f.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913236471, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\crc32.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913236547, "dur":71, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\crc32.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913236469, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/l326nxbjv4r0.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913236653, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\encode.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913236731, "dur":1186, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\encode.c" }}
,{ "pid":12345, "tid":3, "ts":1754309913238021, "dur":208, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\entropy_encode.h" }}
,{ "pid":12345, "tid":3, "ts":1754309913237924, "dur":306, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754309913236650, "dur":1580, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/0m3dl7miwi2h.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913238245, "dur":68, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/0m3dl7miwi2h.o" }}
,{ "pid":12345, "tid":3, "ts":1754309913238314, "dur":232, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/unity-logo-light.png" }}
,{ "pid":12345, "tid":3, "ts":1754309913239541, "dur":2230, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/unity-logo-light.png" }}
,{ "pid":12345, "tid":3, "ts":1754309913241816, "dur":349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/favicon.ico" }}
,{ "pid":12345, "tid":3, "ts":1754309913242197, "dur":910, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/favicon.ico" }}
,{ "pid":12345, "tid":3, "ts":1754309913243151, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/webgl-logo.png" }}
,{ "pid":12345, "tid":3, "ts":1754309913243276, "dur":883, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/webgl-logo.png" }}
,{ "pid":12345, "tid":3, "ts":1754309913244207, "dur":50768923, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913188013, "dur":6143, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913194157, "dur":849, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913195006, "dur":2001, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913197008, "dur":820, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913197828, "dur":11962, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913209813, "dur":104, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MakeLump Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/qnaibdzad7a20.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913209998, "dur":9998, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\948vs1756vy90.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913220004, "dur":118, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913209979, "dur":10143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/lyviuxei7k4z.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913220157, "dur":2212, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913222379, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913220142, "dur":2488, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ou3llaiilfxb.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913222649, "dur":779, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913223429, "dur":542, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913223979, "dur":306, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913222647, "dur":1638, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/1lanlbfbn0u2.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913224335, "dur":121, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__38.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913224468, "dur":279, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913224320, "dur":427, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/aefmoars5g48.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913224814, "dur":485, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__40.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913225307, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913224767, "dur":775, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hm9e1b4qh314.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913225580, "dur":452, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__4.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913226041, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913225564, "dur":715, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/8o1f19n4wezk.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913226314, "dur":141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__2.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913226464, "dur":248, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913226299, "dur":414, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/m1db1kt8po6l.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913226732, "dur":128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913226729, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/gn0etlx7jeui.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913226968, "dur":201, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppReversePInvokeWrapperTable.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913227177, "dur":306, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913226952, "dur":531, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/afr3e1fwqgmx.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913227484, "dur":338, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913227842, "dur":108, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913227957, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913227826, "dur":327, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/qucm601ore0i.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913228193, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913228256, "dur":138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913228420, "dur":324, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Baselib_SourceLocation.h" }}
,{ "pid":12345, "tid":4, "ts":1754309913228401, "dur":581, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913228190, "dur":793, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/b459t7zpejgk.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913228983, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913229141, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/3xf12bjxgumy.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913229215, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913229438, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913229575, "dur":306, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913229418, "dur":463, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/p45wdx67xvlr.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913229882, "dur":278, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913230192, "dur":170, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913230368, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913230166, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/08zcn8s6p3d9.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913230642, "dur":1342, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\kc9qksjkncj61.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913231992, "dur":159, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913230639, "dur":1513, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/t5pj20nwqx9z.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913232177, "dur":371, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\a03fkmo2ly5p0.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913232174, "dur":418, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/38exn4l0k2od.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913232617, "dur":594, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gle7q0kyjq1q0.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913233219, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913232614, "dur":806, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/r6v9ma7ukgza.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913233442, "dur":513, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\kc9qksjkncj60.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913233963, "dur":78, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913233438, "dur":604, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/qmf10spsuht5.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913234065, "dur":376, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\x1psv5dxygow0.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913234449, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913234062, "dur":590, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/t442th1gp8dm.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913234674, "dur":640, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\lju2h0drtind3.lump.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913235323, "dur":171, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913234671, "dur":824, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/5ywuonhokbow.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913235521, "dur":121, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.Versioning\\VersioningHelper.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913235643, "dur":107, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.Versioning\\VersioningHelper.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913235518, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/5nasznifnb9i.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913235808, "dur":197, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\uncompr.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913235806, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/wuxl3djq3ges.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913236050, "dur":368, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\gzlib.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913236048, "dur":386, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/8hegedtcmti4.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913236480, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\extra\\krait_signal_handler.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913236478, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/bdwgc/release_WebGL_wasm/wzptneom0lq2.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913236612, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\fast_log.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913236602, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/j578qhy44m5c.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913236755, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\command.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913236834, "dur":61, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\command.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913236752, "dur":193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/nfqgpt0cr1qz.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913236964, "dur":518, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\backward_references.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913237482, "dur":401, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\backward_references.c" }}
,{ "pid":12345, "tid":4, "ts":1754309913237891, "dur":66, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913236962, "dur":996, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/w35l3u0uwxhx.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913237993, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Mono.Unity\\UnityTls.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913238068, "dur":168, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Mono.Unity\\UnityTls.cpp" }}
,{ "pid":12345, "tid":4, "ts":1754309913238244, "dur":178, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913237991, "dur":431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/84f0xivspqtm.o" }}
,{ "pid":12345, "tid":4, "ts":1754309913238439, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/progress-bar-empty-dark.png" }}
,{ "pid":12345, "tid":4, "ts":1754309913238595, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754309913261103, "dur":1195, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/progress-bar-empty-dark.png" }}
,{ "pid":12345, "tid":4, "ts":1754309913262350, "dur":50750758, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913188032, "dur":6128, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913194162, "dur":861, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913195023, "dur":1753, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913196776, "dur":690, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913197467, "dur":862, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913198329, "dur":11418, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913209819, "dur":91, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/_dummy_for_header_discovery" }}
,{ "pid":12345, "tid":5, "ts":1754309913209977, "dur":10222, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\948vs1756vy92.lump.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913220207, "dur":113, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913209962, "dur":10358, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/7sxb2jdr0u6k.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913220347, "dur":1966, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913222317, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913220337, "dur":2209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/htundjhgkf0t.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913222583, "dur":156, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913222747, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913222566, "dur":427, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/wdrzx92ihroa.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913223026, "dur":154, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__9.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913223187, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913223013, "dur":417, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/8ffol83rp5fz.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913223498, "dur":163, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913223679, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\google\\sparsehash\\sparse_hash_map.h" }}
,{ "pid":12345, "tid":5, "ts":1754309913223669, "dur":648, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913223469, "dur":848, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vxue18qbvt1t.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913224346, "dur":118, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913224472, "dur":346, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913224333, "dur":485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/i8kukxnjivx6.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913224868, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913225020, "dur":312, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913224849, "dur":483, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/l82iofo4yb9t.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913225372, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/903qwy0pli70.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913225455, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/7gnty97h5q1c.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913225570, "dur":126, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913225704, "dur":252, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913225551, "dur":406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ur7gegomergk.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913225984, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1754309913225981, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/flojz1nuktil.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913226271, "dur":745, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__9.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913227201, "dur":234, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\Handle.h" }}
,{ "pid":12345, "tid":5, "ts":1754309913227024, "dur":449, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913226258, "dur":1216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/1fgdhxrtdjw1.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913227527, "dur":121, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913227655, "dur":265, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913227494, "dur":426, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/y905vd6coc8u.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913227920, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913227978, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913228119, "dur":136, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913228262, "dur":269, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913227975, "dur":556, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/rkxlcnect8f3.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913228554, "dur":258, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__11.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913228812, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__11.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913229147, "dur":271, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils\\NonCopyable.h" }}
,{ "pid":12345, "tid":5, "ts":1754309913228950, "dur":550, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913228552, "dur":948, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hvcirndadcg3.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913229535, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/u7gyw8mw5vk3.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913229701, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/326kmsnfny9n.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913229819, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/tf51ia0k5jmp.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913229985, "dur":170, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__34.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913230163, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913229936, "dur":469, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/223cow10r0ib.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913230424, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/rgj128hwe75l.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913230508, "dur":2432, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\u9p1ge85f2pd0.lump.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913232951, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913230505, "dur":2669, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/8703trzuhio6.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913233190, "dur":617, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gi7kprgxmq3m6.lump.cpp" }}
,{ "pid":12345, "tid":5, "ts":1754309913233816, "dur":302, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913233188, "dur":931, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/dfgcfkeunpqo.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913234119, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913234332, "dur":15778, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\bdwgc\\extra\\gc.c" }}
,{ "pid":12345, "tid":5, "ts":1754309913250122, "dur":253, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754309913234328, "dur":16048, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/bdwgc/release_WebGL_wasm/rcr76u2kd2yl.o" }}
,{ "pid":12345, "tid":5, "ts":1754309913250405, "dur":323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Lib_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/GameAssembly.a" }}
,{ "pid":12345, "tid":5, "ts":1754309913250926, "dur":777, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\rsp\\8995565923215908413.rsp" }}
,{ "pid":12345, "tid":5, "ts":1754309913250764, "dur":940, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Link_WebGL_wasm Library/Bee/artifacts/WebGL/build/debug_WebGL_wasm/build.js (+wasm)" }}
,{ "pid":12345, "tid":5, "ts":1754309913251940, "dur":50478654, "ph":"X", "name": "Link_WebGL_wasm",  "args": { "detail":"Library/Bee/artifacts/WebGL/build/debug_WebGL_wasm/build.js (+wasm)" }}
,{ "pid":12345, "tid":5, "ts":1754309963733009, "dur":278938, "ph":"X", "name": "FrameworkAssembler",  "args": { "detail":"Library/Bee/artifacts/WebGL/build/debug_WebGL_wasm/build.framework.js" }}
,{ "pid":12345, "tid":5, "ts":1754309964012195, "dur":882, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/Build/gdex.framework.js" }}
,{ "pid":12345, "tid":6, "ts":1754309913188053, "dur":6111, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913194166, "dur":855, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913195021, "dur":1703, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913196724, "dur":700, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913197424, "dur":590, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913198015, "dur":11735, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913209936, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MakeLump Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/q3r8xbbbrs7b0.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913210002, "dur":4846, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\q3r8xbbbrs7b0.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913209988, "dur":4906, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/qjfzpsj86zia.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913214913, "dur":3302, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\c9dj0dxeq3ww2.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913218220, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913214911, "dur":3364, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/l0ivrx8arhz0.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913218300, "dur":218, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\bit_cost.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913218518, "dur":2458, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\bit_cost.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913218297, "dur":2715, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/h9r0idc5ll69.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913221047, "dur":1293, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913222348, "dur":279, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913221032, "dur":1595, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/nlzfvy2uo6l5.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913222672, "dur":505, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913223185, "dur":269, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913222651, "dur":803, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/3gisd8bcr5ss.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913223455, "dur":428, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913223890, "dur":277, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__16.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913224168, "dur":910, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__16.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913225087, "dur":255, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913223887, "dur":1455, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/bogvgs813xfw.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913225343, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913225404, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/lyt6mshenkw0.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913225492, "dur":141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__17.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913225641, "dur":278, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913225478, "dur":442, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/v1u3mkvww4o3.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913225957, "dur":158, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UI__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913226123, "dur":323, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913225943, "dur":503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ftwo7ye9mf2p.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913226509, "dur":542, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__14.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913227060, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913226494, "dur":813, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/p0sqg3d6w7mx.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913227364, "dur":142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__10.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913227801, "dur":478, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Baselib_Thread.h" }}
,{ "pid":12345, "tid":6, "ts":1754309913227514, "dur":775, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913227326, "dur":964, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/26fhllnmep4c.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913228304, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/aabq3ll30ysg.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913228400, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/5qy1rhytrx3l.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913228517, "dur":142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__37.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913228668, "dur":306, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913228504, "dur":470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/95u53uau1ent.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913228995, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/xviwvyu6q1j3.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913229120, "dur":321, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913229681, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\google\\sparsehash\\internal\\sparseconfig.h" }}
,{ "pid":12345, "tid":6, "ts":1754309913229454, "dur":356, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913229104, "dur":707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/gfvle5iilh9i.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913229844, "dur":294, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__29.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913230422, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils\\Il2CppHashMap.h" }}
,{ "pid":12345, "tid":6, "ts":1754309913230145, "dur":350, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913229829, "dur":666, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/orirvwf95zgx.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913230532, "dur":1095, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\tn7us4ioczrb0.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913231632, "dur":112, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913230528, "dur":1216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/m2v4mj8x1qig.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913231767, "dur":995, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\3cj6qxdubavv1.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913232769, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913231764, "dur":1224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/evjt2i9zjeu5.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913233011, "dur":352, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\hngu0jx1plce0.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913233007, "dur":395, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/t0lubk0rrsux.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913233439, "dur":737, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\ov8h1o75pjiz2.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913234185, "dur":75, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913233430, "dur":830, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/w1ohzdtbhzbo.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913234281, "dur":516, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\6ltc8ggdxjl50.lump.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913235017, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\il2cpp-sanitizers.h" }}
,{ "pid":12345, "tid":6, "ts":1754309913234805, "dur":328, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913234278, "dur":856, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/kow1jlmcfi3g.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913235172, "dur":128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\metablock.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913235300, "dur":769, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\metablock.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913236113, "dur":302, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\il2cpp-sanitizers.h" }}
,{ "pid":12345, "tid":6, "ts":1754309913236077, "dur":360, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913235169, "dur":1268, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/chfqd52fr0us.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913236459, "dur":91, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/chfqd52fr0us.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913236561, "dur":90, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\memory.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913236558, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/7jg9d3w4hxet.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913236696, "dur":208, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\compress_fragment_two_pass.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913236904, "dur":175, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\compress_fragment_two_pass.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913237087, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913236693, "dur":448, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/4x8gvpyfecy3.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913237158, "dur":251, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\common\\transform.c" }}
,{ "pid":12345, "tid":6, "ts":1754309913237156, "dur":276, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/t7iaq6pnzmlo.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913237457, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\WindowsGames\\Win32ApiWindowsGamesEmulation.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913237454, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/rcg9suf4vicp.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913237638, "dur":133, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.CompilerServices\\RuntimeHelpers.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913237771, "dur":114, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.CompilerServices\\RuntimeHelpers.cpp" }}
,{ "pid":12345, "tid":6, "ts":1754309913237895, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754309913237634, "dur":476, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/z17d6lfxk7zi.o" }}
,{ "pid":12345, "tid":6, "ts":1754309913239011, "dur":448, "ph":"X", "name": "WriteResponseFile",  "args": { "detail":"Library/Bee/artifacts/rsp/8995565923215908413.rsp" }}
,{ "pid":12345, "tid":6, "ts":1754309913239461, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/fullscreen-button.png" }}
,{ "pid":12345, "tid":6, "ts":1754309913239956, "dur":1704, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/fullscreen-button.png" }}
,{ "pid":12345, "tid":6, "ts":1754309913241742, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"PreprocessJS gdex/index.html" }}
,{ "pid":12345, "tid":6, "ts":1754309913241857, "dur":167047, "ph":"X", "name": "PreprocessJS",  "args": { "detail":"gdex/index.html" }}
,{ "pid":12345, "tid":6, "ts":1754309913408955, "dur":50604156, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913188074, "dur":6094, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913194170, "dur":975, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913195145, "dur":2082, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913197228, "dur":870, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913198098, "dur":11656, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913209983, "dur":9507, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\x9xx6k9lokjm0.lump.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913219496, "dur":78, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913209976, "dur":9598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/8125lw14idj0.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913219610, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1754309913219592, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/sqdva6so2zv9.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913219815, "dur":2504, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913222324, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913219802, "dur":2758, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/n6mlo40nvsny.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913222593, "dur":231, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913222831, "dur":262, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913222579, "dur":515, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/4tml2o8kw1cz.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913223094, "dur":225, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913223326, "dur":98, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__15.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913223425, "dur":337, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__15.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913223770, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913223323, "dur":694, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/q8c70iu4093c.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913224017, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913224094, "dur":217, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__7.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913224319, "dur":255, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913224075, "dur":499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/c7j8k07621b3.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913224602, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1754309913224599, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/pd34rr3zfh5v.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913224751, "dur":129, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__19.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913224888, "dur":275, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913224736, "dur":427, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hpi8m9ps3uh2.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913225195, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/wecv3zcuvrwo.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913225301, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913225479, "dur":409, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\debugger\\il2cpp-api-debugger.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913225889, "dur":1389, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\debugger\\il2cpp-api-debugger.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913227285, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913225476, "dur":2060, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/5bptuldaikcp.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913227574, "dur":254, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913227838, "dur":257, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913227559, "dur":536, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hcqujegcv1kd.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913228146, "dur":153, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__35.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913228308, "dur":252, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913228126, "dur":435, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/3aot8bizv2zq.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913228561, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913228708, "dur":2433, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__7.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913231149, "dur":263, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913228694, "dur":2718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/fss8g8wx6s32.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913231436, "dur":903, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\3cj6qxdubavv2.lump.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913232347, "dur":265, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913231433, "dur":1179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/heub4q73fua0.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913232613, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913232696, "dur":725, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\it4wngfjo6pk0.lump.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913233431, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913232692, "dur":976, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/hdysvmhir405.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913233692, "dur":2741, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gi7kprgxmq3m3.lump.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913236442, "dur":297, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913233689, "dur":3051, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/o1305phhyz78.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913236764, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\cluster.c" }}
,{ "pid":12345, "tid":7, "ts":1754309913236882, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913236761, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/i0m0fc6ljeao.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913236963, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\backward_references_hq.c" }}
,{ "pid":12345, "tid":7, "ts":1754309913237185, "dur":694, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\backward_references_hq.c" }}
,{ "pid":12345, "tid":7, "ts":1754309913237890, "dur":85, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913236960, "dur":1016, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/29ooy7nucm5v.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913238021, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Mono.Security.Cryptography\\KeyPairPersistence.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913238151, "dur":119, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Mono.Security.Cryptography\\KeyPairPersistence.cpp" }}
,{ "pid":12345, "tid":7, "ts":1754309913238018, "dur":270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/7j6es4o4jcow.o" }}
,{ "pid":12345, "tid":7, "ts":1754309913238332, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/progress-bar-full-dark.png" }}
,{ "pid":12345, "tid":7, "ts":1754309913238595, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754309913254676, "dur":806, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/progress-bar-full-dark.png" }}
,{ "pid":12345, "tid":7, "ts":1754309913255526, "dur":50757580, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913188091, "dur":6082, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913194174, "dur":848, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913195023, "dur":1843, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913196866, "dur":1066, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913197932, "dur":11808, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913209782, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MakeLump Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/kc9qksjkncj60.lump.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913210009, "dur":11010, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\5vuyn2a4sw7t0.lump.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913221027, "dur":176, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913209998, "dur":11205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/5nc4dvc1n9s4.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913221235, "dur":1102, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__3.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913222344, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913221224, "dur":1370, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/4v8ykmifplq5.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913222595, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913222686, "dur":164, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__15.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913222856, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913222661, "dur":387, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/9d7ymv00pew4.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913223049, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913223122, "dur":128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__23.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913223251, "dur":125, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__23.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913223383, "dur":277, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913223120, "dur":540, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/al05zzxmm77w.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913223743, "dur":140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913223905, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\Posix\\ThreadLocalValueImpl.h" }}
,{ "pid":12345, "tid":8, "ts":1754309913223890, "dur":512, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913223691, "dur":711, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/9ttea2772ovf.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913224440, "dur":145, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__16.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913224604, "dur":295, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913224425, "dur":474, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/utan24eys27n.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913224924, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__8.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913224982, "dur":140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__8.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913225129, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913224921, "dur":450, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/xmnw09l45pnm.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913225387, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/0eweq1hznd2d.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913225476, "dur":155, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__42.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913225638, "dur":272, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913225464, "dur":446, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/39wj10ymotu3.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913225934, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/6pr05jyuzg7w.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913226020, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vdz6xy1sfgsz.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913226117, "dur":116, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__33.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913226242, "dur":265, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913226105, "dur":403, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hn0o8mn3oc7r.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913226508, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913226708, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/xd5xyexly0ga.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913226874, "dur":62, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913226814, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ws76eiir7jmz.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913226999, "dur":161, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro__6.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913227168, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913226954, "dur":420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hnf2ykzgpjqy.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913227405, "dur":156, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System__1.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913227593, "dur":280, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913227392, "dur":481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/foqbqxj8jjzz.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913227911, "dur":112, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913228030, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913227897, "dur":349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/3z1tp2put9q0.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913228275, "dur":106, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913228390, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913228262, "dur":387, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/u9w2pbm55c7y.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913228706, "dur":154, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro__4.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913228868, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913228691, "dur":420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vzrxyv4kitxz.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913229155, "dur":333, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__41.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913229615, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm-utils\\Debugger.h" }}
,{ "pid":12345, "tid":8, "ts":1754309913229496, "dur":331, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913229133, "dur":695, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/21c3gliokrdo.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913229847, "dur":407, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1754309913229844, "dur":457, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/j3w01uxlhx2d.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913230355, "dur":1978, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__13.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913232341, "dur":281, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913230327, "dur":2296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/4kp20tz8tjbe.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913232647, "dur":1710, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gi7kprgxmq3m1.lump.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913234365, "dur":301, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913232643, "dur":2023, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/izi029k953ak.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913234685, "dur":1123, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\tn7us4ioczrb3.lump.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913235816, "dur":75, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754309913234683, "dur":1208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/bg8fwd5arg1v.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913235916, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\inffast.c" }}
,{ "pid":12345, "tid":8, "ts":1754309913236110, "dur":421, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\inffast.c" }}
,{ "pid":12345, "tid":8, "ts":1754309913235914, "dur":668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/n50y5guwck2g.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913236608, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\entropy_encode.c" }}
,{ "pid":12345, "tid":8, "ts":1754309913236606, "dur":212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/aw8lp1lddf45.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913236839, "dur":191, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.Remoting.Proxies\\RealProxy.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913236836, "dur":242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/wuhk6fluwchd.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913237102, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\decode.c" }}
,{ "pid":12345, "tid":8, "ts":1754309913237291, "dur":284, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\decode.c" }}
,{ "pid":12345, "tid":8, "ts":1754309913237099, "dur":518, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/k215xfbnu2x3.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913237640, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\mono\\MonoPosixHelper.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913237706, "dur":96, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\mono\\MonoPosixHelper.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913237638, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/oo4nh8tyvpag.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913237869, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\System.Threading\\Semaphore.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913237945, "dur":99, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\System.Threading\\Semaphore.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913237866, "dur":234, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/dt5wtxbs9trj.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913238128, "dur":109, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Security.Cryptography\\RNGCryptoServiceProvider.cpp" }}
,{ "pid":12345, "tid":8, "ts":1754309913238125, "dur":171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/rdkyung4tk4o.o" }}
,{ "pid":12345, "tid":8, "ts":1754309913238315, "dur":264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/unity-logo-dark.png" }}
,{ "pid":12345, "tid":8, "ts":1754309913247933, "dur":1013, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/unity-logo-dark.png" }}
,{ "pid":12345, "tid":8, "ts":1754309913248997, "dur":50764107, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913188110, "dur":6067, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913194178, "dur":857, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913195036, "dur":1983, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913197020, "dur":971, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913197991, "dur":11752, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913209862, "dur":88, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/_dummy_for_header_discovery" }}
,{ "pid":12345, "tid":9, "ts":1754309913210014, "dur":10855, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\5vuyn2a4sw7t1.lump.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913220878, "dur":105, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913210004, "dur":10980, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/86mmf3a5jp9y.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913221001, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/41n6uaorkbqr.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913221075, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ptajjs2nvfyv.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913221162, "dur":1221, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913222391, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913221147, "dur":1483, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/jg5a5c4hmxem.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913222642, "dur":69, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/jg5a5c4hmxem.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913222727, "dur":117, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913223124, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm\\PlatformInvoke.h" }}
,{ "pid":12345, "tid":9, "ts":1754309913222883, "dur":328, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913222712, "dur":499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hy0152yrz0bx.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913223212, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913223405, "dur":134, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__5.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913223735, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\FastReaderReaderWriterLock.h" }}
,{ "pid":12345, "tid":9, "ts":1754309913223842, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\Handle.h" }}
,{ "pid":12345, "tid":9, "ts":1754309913223546, "dur":411, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913223389, "dur":568, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/qjw3kbdbwbj4.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913223989, "dur":135, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__9.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913224133, "dur":337, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913223975, "dur":495, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/i2qkgj018dz5.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913224517, "dur":139, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913224663, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913224488, "dur":449, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ak9kt6k50jxa.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913224977, "dur":134, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__14.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913225119, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913224957, "dur":414, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/oy3a7lm3mqkl.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913225390, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/bho91i8qf7x4.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913225497, "dur":147, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__28.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913225651, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913225481, "dur":445, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/njyabhza2xzh.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913225926, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913226059, "dur":138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913226206, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913226039, "dur":399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/gxk7p0g1c3zx.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913226474, "dur":107, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__39.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913226588, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913226457, "dur":381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vtia4j5p84fs.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913226839, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913227021, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/uyvm6ylps9ji.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913227094, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913227233, "dur":526, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913227760, "dur":134, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913227904, "dur":256, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913227231, "dur":930, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/d9w8o8rg8a8c.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913228161, "dur":250, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913228428, "dur":128, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913228563, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913228413, "dur":396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/4o52sfrqhwkf.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913228809, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913229004, "dur":117, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__43.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913229236, "dur":173, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\il2cpp-class-internals.h" }}
,{ "pid":12345, "tid":9, "ts":1754309913229466, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\Cpp\\ReentrantLock.h" }}
,{ "pid":12345, "tid":9, "ts":1754309913229140, "dur":526, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913228986, "dur":681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/op07gadkrh6m.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913229731, "dur":132, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913229872, "dur":321, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913229689, "dur":504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/hqojh5ftlguy.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913230231, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__11.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913230366, "dur":270, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913230216, "dur":420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ix1x044cntif.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913230663, "dur":1110, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\it4wngfjo6pk2.lump.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913231782, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913230660, "dur":1335, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/37zl38frke3q.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913232020, "dur":283, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\ub4gqzsnpy2k0.lump.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913232311, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913232017, "dur":518, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/a5e132clsk9u.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913232565, "dur":1130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gi7kprgxmq3m0.lump.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913233703, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913232555, "dur":1399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/znrpwvk489ot.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913233977, "dur":874, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\ov8h1o75pjiz1.lump.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913234859, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913233974, "dur":1072, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/dqx6szs6j5px.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913235074, "dur":698, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\lju2h0drtind1.lump.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913235780, "dur":77, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913235071, "dur":787, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/zwbul7kgsfr7.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913235878, "dur":209, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\inflate.c" }}
,{ "pid":12345, "tid":9, "ts":1754309913236088, "dur":451, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\inflate.c" }}
,{ "pid":12345, "tid":9, "ts":1754309913235876, "dur":681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/9ya3001m7dhw.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913236594, "dur":90, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\literal_cost.c" }}
,{ "pid":12345, "tid":9, "ts":1754309913236685, "dur":87, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\literal_cost.c" }}
,{ "pid":12345, "tid":9, "ts":1754309913236591, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/jwwvc7rsxalf.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913236811, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\brotli_bit_stream.c" }}
,{ "pid":12345, "tid":9, "ts":1754309913236887, "dur":229, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\brotli_bit_stream.c" }}
,{ "pid":12345, "tid":9, "ts":1754309913236809, "dur":361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/bvjm4dhdtesz.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913237187, "dur":277, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\common\\dictionary.c" }}
,{ "pid":12345, "tid":9, "ts":1754309913237185, "dur":315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/n0sp7y6hy9ol.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913237519, "dur":95, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.Remoting.Activation\\ActivationServices.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913237617, "dur":95, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.Remoting.Activation\\ActivationServices.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913237719, "dur":319, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754309913237517, "dur":521, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/k5j8lq77f4l1.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913238057, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Microsoft.Win32\\NativeMethods.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913238121, "dur":85, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Microsoft.Win32\\NativeMethods.cpp" }}
,{ "pid":12345, "tid":9, "ts":1754309913238054, "dur":186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/gm8z6u7e7o0m.o" }}
,{ "pid":12345, "tid":9, "ts":1754309913238327, "dur":255, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/progress-bar-full-light.png" }}
,{ "pid":12345, "tid":9, "ts":1754309913247528, "dur":1077, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/progress-bar-full-light.png" }}
,{ "pid":12345, "tid":9, "ts":1754309913248653, "dur":50764468, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913188135, "dur":6046, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913194183, "dur":1036, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913195219, "dur":1834, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913197054, "dur":999, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913198053, "dur":11692, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913209827, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MakeLump Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/gi7kprgxmq3m1.lump.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913209900, "dur":92, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MakeLump Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/35pdww7u8h5y0.lump.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913210010, "dur":4039, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\35pdww7u8h5y0.lump.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913209994, "dur":4092, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/woy6s0sf4ptu.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913214106, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ClassRegistrationGenerator D:/Game Development/Interns/Basic Connect Wallet/Library/Bee/artifacts/WebGL/il2cppOutput/UnityClassRegistration.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913214234, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime\\RuntimeImports.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913214288, "dur":6856, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime\\RuntimeImports.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913221152, "dur":147, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913214232, "dur":7068, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/12osyuh5oyl2.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913221337, "dur":1011, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913222355, "dur":278, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913221323, "dur":1311, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/2absxpypuurw.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913222672, "dur":156, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__26.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913222839, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913222657, "dur":423, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/horyfy6fwlcf.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913223127, "dur":123, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__36.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913223258, "dur":283, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913223112, "dur":429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/oqrholwwyqba.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913223572, "dur":140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__12.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913223905, "dur":262, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm\\Il2CppHStringReference.h" }}
,{ "pid":12345, "tid":10, "ts":1754309913223719, "dur":535, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913223559, "dur":695, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/2ag66wr7f0rm.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913224292, "dur":138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913224588, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\il2cpp-object-internals.h" }}
,{ "pid":12345, "tid":10, "ts":1754309913224438, "dur":346, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913224272, "dur":513, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/owlgthq9tt4b.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913224852, "dur":136, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System.Xml.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913224999, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913224833, "dur":411, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/lxbrmxhmv9nq.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913225277, "dur":114, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913225401, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913225263, "dur":368, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/n0b05ypumh4f.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913225660, "dur":171, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__32.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913225839, "dur":331, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913225647, "dur":524, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/gfg8n764kaqc.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913226194, "dur":385, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__30.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913226579, "dur":116, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__30.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913226702, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913226192, "dur":756, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ak9vsoeafy29.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913226977, "dur":202, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__3.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913227187, "dur":252, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913226963, "dur":477, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/n9xrl096tqn1.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913227469, "dur":135, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__4.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913227610, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913227455, "dur":429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/pbcn0vwcs91o.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913227932, "dur":146, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913228085, "dur":285, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913227902, "dur":469, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/r2vuf2cg7uh1.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913228445, "dur":124, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913228577, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913228423, "dur":361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/28xbtsvdn3bd.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913228817, "dur":164, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913229237, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Baselib_ErrorState.h" }}
,{ "pid":12345, "tid":10, "ts":1754309913228993, "dur":472, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913228803, "dur":663, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/lo302h73srps.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913229466, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913229651, "dur":283, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913229941, "dur":335, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913229639, "dur":638, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vzc98fz2378t.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913230277, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913230408, "dur":134, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__6.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913230549, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913230382, "dur":389, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/bzzr0kr8yzun.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913230796, "dur":1586, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\3cj6qxdubavv3.lump.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913232389, "dur":322, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913230793, "dur":1919, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/0plma578cowq.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913232736, "dur":534, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\ov8h1o75pjiz0.lump.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913233277, "dur":73, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913232733, "dur":617, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/a7morru2np92.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913233377, "dur":788, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\gi7kprgxmq3m4.lump.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913234173, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913233374, "dur":1044, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/7cvbc4hmvttz.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913234446, "dur":1443, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\6gqnuv4awzas0.lump.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913235897, "dur":104, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913234441, "dur":1560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/9ngoh5z20f7g.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913236022, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\common\\platform.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913236113, "dur":301, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\include\\brotli\\types.h" }}
,{ "pid":12345, "tid":10, "ts":1754309913236108, "dur":306, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913236019, "dur":396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/c29igxml7uvo.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913236470, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\deflate.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913236468, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/df6kkiuhmaot.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913236601, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\histogram.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913236599, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/4d8txelhcbvo.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913236828, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\block_splitter.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913236897, "dur":138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\block_splitter.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913236825, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/7djbsqnb3plx.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913237111, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\bit_reader.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913237267, "dur":67, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\bit_reader.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913237108, "dur":251, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/7iwctuxd4d7l.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913237381, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\common\\context.c" }}
,{ "pid":12345, "tid":10, "ts":1754309913237379, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/4dkjf59nvcqy.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913237488, "dur":102, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System.Core\\System.IO.MemoryMappedFiles\\MemoryMapImpl.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913237590, "dur":217, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System.Core\\System.IO.MemoryMappedFiles\\MemoryMapImpl.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913237815, "dur":151, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913237485, "dur":481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/05da4rtempe7.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913237990, "dur":121, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Security.Policy\\Evidence.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913238114, "dur":86, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Security.Policy\\Evidence.cpp" }}
,{ "pid":12345, "tid":10, "ts":1754309913237987, "dur":265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/arrachmbeuuk.o" }}
,{ "pid":12345, "tid":10, "ts":1754309913238345, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/progress-bar-empty-light.png" }}
,{ "pid":12345, "tid":10, "ts":1754309913238591, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754309913254377, "dur":804, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/progress-bar-empty-light.png" }}
,{ "pid":12345, "tid":10, "ts":1754309913255232, "dur":50757877, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913188153, "dur":6032, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913194187, "dur":1007, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913195195, "dur":1814, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913197009, "dur":954, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913197963, "dur":11776, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913209974, "dur":10470, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\q3r8xbbbrs7b1.lump.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913220458, "dur":135, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913209964, "dur":10630, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/t2phr0hc3fz4.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913220641, "dur":2145, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913222793, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913220620, "dur":2416, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/oqs5r5e9mv44.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913223077, "dur":141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913223227, "dur":290, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913223060, "dur":457, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/d0dlg3kpy7zc.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913223534, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/10d578pncrnf.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913223595, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913223757, "dur":128, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System.Core.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913223904, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Platforms\\WebGL\\Include\\BaselibPlatformSpecificEnvironment.h" }}
,{ "pid":12345, "tid":11, "ts":1754309913223892, "dur":298, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913223723, "dur":467, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/jy3blpz3vu5v.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913224237, "dur":133, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__8.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913224378, "dur":263, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913224224, "dur":417, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/07vwwhbv9gfm.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913224642, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913224853, "dur":1193, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__6.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913226054, "dur":287, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913224836, "dur":1505, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ucpk1yi034rj.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913226404, "dur":168, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913226581, "dur":266, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913226384, "dur":464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/2k8bqpbxjtsw.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913226870, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/nsoqo2iiktmp.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913226943, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913227673, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/kzq0jatqii6w.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913227785, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\System.Configuration.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913227924, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913227772, "dur":390, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/sjwxk89i7cb7.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913228181, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ckc6cdf2o426.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913228323, "dur":308, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__12.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913228637, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913228279, "dur":605, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/j5lelklbrvzu.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913228907, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/u5qcsqg46882.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913228977, "dur":696, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro__1.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913229674, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Unity.TextMeshPro__1.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913230029, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils\\Il2CppHashMap.h" }}
,{ "pid":12345, "tid":11, "ts":1754309913229813, "dur":277, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913228974, "dur":1116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ow04ixvr8eth.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913230091, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913230182, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__22.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913230306, "dur":304, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913230160, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/9cgpfkhoi5qh.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913230614, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913230684, "dur":413, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\k929u3nvh51c0.lump.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913230681, "dur":463, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/ulbs6pvs51zw.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913231167, "dur":1246, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\qnaibdzad7a20.lump.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913232423, "dur":287, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913231163, "dur":1548, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/u32sermmnyna.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913232739, "dur":732, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\bkgsn8369b9p0.lump.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913233478, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913232736, "dur":948, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/mxywm1zyvkqq.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913233707, "dur":860, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\lju2h0drtind0.lump.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913234574, "dur":158, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913233704, "dur":1028, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/glkjftkbvl5g.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913234759, "dur":1243, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\lju2h0drtind2.lump.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913236112, "dur":301, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\Event.h" }}
,{ "pid":12345, "tid":11, "ts":1754309913236009, "dur":493, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913234755, "dur":1747, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/2fmcnfjkov71.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913236531, "dur":208, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\static_dict.c" }}
,{ "pid":12345, "tid":11, "ts":1754309913236739, "dur":313, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\static_dict.c" }}
,{ "pid":12345, "tid":11, "ts":1754309913236528, "dur":549, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/jfcqxwbjtge5.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913237099, "dur":186, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\huffman.c" }}
,{ "pid":12345, "tid":11, "ts":1754309913237285, "dur":114, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\huffman.c" }}
,{ "pid":12345, "tid":11, "ts":1754309913237096, "dur":333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/5puncitr85x2.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913237452, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\common\\constants.c" }}
,{ "pid":12345, "tid":11, "ts":1754309913237449, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/06ljt76r0ok4.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913237608, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\System.Net\\Dns.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913237677, "dur":137, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\System.Net\\Dns.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913237822, "dur":143, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754309913237606, "dur":360, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/jena8ivp9uwn.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913237988, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\Microsoft.Win32\\NativeMethods.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913238065, "dur":84, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\Microsoft.Win32\\NativeMethods.cpp" }}
,{ "pid":12345, "tid":11, "ts":1754309913237985, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/updys0bj3ftk.o" }}
,{ "pid":12345, "tid":11, "ts":1754309913238249, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/build.framework.js_mumr.info" }}
,{ "pid":12345, "tid":11, "ts":1754309913238322, "dur":249, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"PreprocessJS gdex/TemplateData/style.css" }}
,{ "pid":12345, "tid":11, "ts":1754309913239934, "dur":109068, "ph":"X", "name": "PreprocessJS",  "args": { "detail":"gdex/TemplateData/style.css" }}
,{ "pid":12345, "tid":11, "ts":1754309913349111, "dur":50664006, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913188171, "dur":6017, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913194189, "dur":833, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913195022, "dur":1753, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913196776, "dur":725, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913197501, "dur":781, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913198282, "dur":11462, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913209993, "dur":10361, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\948vs1756vy91.lump.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913220364, "dur":140, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913209977, "dur":10528, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/49mt0bfn4eqq.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913220539, "dur":1813, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\mscorlib__5.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913222359, "dur":264, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913220525, "dur":2099, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/t443gj21z2a4.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913222660, "dur":144, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913222811, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913222645, "dur":402, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/vifjih0e306a.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913223071, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__24.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913223131, "dur":122, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__24.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913223259, "dur":280, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913223068, "dur":472, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/v03tf9bkpvjg.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913223583, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913223843, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\vm\\ClassInlines.h" }}
,{ "pid":12345, "tid":12, "ts":1754309913223706, "dur":296, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913223556, "dur":447, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/4hqxdzo2ke20.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913224039, "dur":261, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__25.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913224311, "dur":319, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913224027, "dur":603, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/gg7hviqhsjvz.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913224692, "dur":158, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913224656, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/3tr78j47071d.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913224851, "dur":1214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913226070, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/jzyvcoxw0371.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913226240, "dur":174, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913226422, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913226202, "dur":434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/02ewa9wgizdb.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913226686, "dur":136, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913226831, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913226655, "dur":375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/4dwzhlsgh3gt.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913227047, "dur":351, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__18.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913227399, "dur":155, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__18.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913227687, "dur":126, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\il2cpp-string-types.h" }}
,{ "pid":12345, "tid":12, "ts":1754309913227562, "dur":384, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913227044, "dur":903, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/ghyr2t9tzs0m.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913227981, "dur":118, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913228106, "dur":266, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913227965, "dur":408, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/pugl1damikld.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913228421, "dur":462, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__44.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913228883, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\Generics__44.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913229234, "dur":238, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\gc\\WriteBarrier.h" }}
,{ "pid":12345, "tid":12, "ts":1754309913229548, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\baselib\\Include\\C\\Baselib_Timer.h" }}
,{ "pid":12345, "tid":12, "ts":1754309913229067, "dur":565, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913228418, "dur":1214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/pxko1ptivjn2.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913229633, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913229708, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913229790, "dur":145, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\Game Development\\Interns\\Basic Connect Wallet\\Library\\Bee\\artifacts\\WebGL\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913230030, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\metadata\\Il2CppSignature.h" }}
,{ "pid":12345, "tid":12, "ts":1754309913230118, "dur":239, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\utils\\StringView.h" }}
,{ "pid":12345, "tid":12, "ts":1754309913229944, "dur":526, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913229705, "dur":765, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/GameAssembly/release_WebGL_wasm/1cov59uyfrbh.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913230470, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913230529, "dur":733, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\3cj6qxdubavv0.lump.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913231269, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913230525, "dur":935, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/bc9a1abwnydb.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913231481, "dur":329, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\adler32.c" }}
,{ "pid":12345, "tid":12, "ts":1754309913231478, "dur":348, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/7meo3fmh575u.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913231850, "dur":2450, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\ph2yscarauqz0.lump.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913234307, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913231847, "dur":2690, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/9e9ehnnj0561.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913234563, "dur":1146, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"Library\\Bee\\artifacts\\WebGL\\il2cpp\\release_WebGL_wasm\\lju2h0drtind4.lump.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913235717, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309913234559, "dur":1391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/oao5u1hth59m.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913235972, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\gzread.c" }}
,{ "pid":12345, "tid":12, "ts":1754309913235970, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/djesbes10czf.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913236105, "dur":315, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\external\\zlib\\gzclose.c" }}
,{ "pid":12345, "tid":12, "ts":1754309913236103, "dur":338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/zlib/release_WebGL_wasm/z4qqe125q3ij.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913236533, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\Std\\ThreadImpl.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913236530, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/97qll30oa98t.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913236661, "dur":216, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\dictionary_hash.c" }}
,{ "pid":12345, "tid":12, "ts":1754309913236878, "dur":97, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\enc\\dictionary_hash.c" }}
,{ "pid":12345, "tid":12, "ts":1754309913236658, "dur":332, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/rpfdquwnqtop.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913237014, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\state.c" }}
,{ "pid":12345, "tid":12, "ts":1754309913237099, "dur":323, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\ClassLibraryPAL\\brotli\\dec\\state.c" }}
,{ "pid":12345, "tid":12, "ts":1754309913237011, "dur":440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/5uflpelrl7dq.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913237496, "dur":99, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\System.Runtime.Remoting.Contexts\\Context.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913237493, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/viuahx4xjl1b.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913237673, "dur":101, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\os\\Emscripten\\SocketBridge.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913237670, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/yqknachbv4zp.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913237856, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\System.IO\\FileSystemWatcher.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913237939, "dur":109, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\System\\System.IO\\FileSystemWatcher.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913237853, "dur":234, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/ict822sitb07.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913238108, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\il2cpp\\libil2cpp\\icalls\\mscorlib\\Interop.cpp" }}
,{ "pid":12345, "tid":12, "ts":1754309913238106, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_WebGL_wasm Library/Bee/artifacts/WebGL/il2cpp/release_WebGL_wasm/ukcjhpjg6qv4.o" }}
,{ "pid":12345, "tid":12, "ts":1754309913239365, "dur":2558, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/Build/gdex.loader.js" }}
,{ "pid":12345, "tid":12, "ts":1754309913241925, "dur":323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles gdex/TemplateData/webmemd-icon.png" }}
,{ "pid":12345, "tid":12, "ts":1754309913242303, "dur":1075, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/TemplateData/webmemd-icon.png" }}
,{ "pid":12345, "tid":12, "ts":1754309913243422, "dur":50489304, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754309963732772, "dur":6505, "ph":"X", "name": "CopyFiles",  "args": { "detail":"gdex/Build/gdex.wasm" }}
,{ "pid":12345, "tid":12, "ts":1754309963739280, "dur":273843, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754309964024207, "dur":2622, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 11208, "tid": 1845, "ts": 1754309964031356, "dur": 26, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964031549, "dur": 216, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964031915, "dur": 13, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964031414, "dur": 134, "ph": "X", "name": "buildprogram0.traceevents", "args": {} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964031790, "dur": 124, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964031948, "dur": 5810, "ph": "X", "name": "backend2.traceevents", "args": {} },
{ "pid": 11208, "tid": 1845, "ts": 1754309964027769, "dur": 10035, "ph": "X", "name": "Write chrome-trace events", "args": {} },
