{"PlayerBuildProgramLibrary.ClassRegistrationGenerator+Arguments": {"linkerOutput": "Library/Bee/artifacts/WebGL/ManagedStripped/UnityLinkerToEditorData.json", "editorOutput": "D:/Game Development/Interns/Basic Connect Wallet/Temp/StagingArea/Data/Managed/EditorToUnityLinkerData.json", "moduleAssetsPath": "C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WebGLSupport/modules.asset", "outputClassRegistration": "D:/Game Development/Interns/Basic Connect Wallet/Library/Bee/artifacts/WebGL/il2cppOutput/UnityClassRegistration.cpp", "enableStripping": true}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "PlayerBuildProgramLibrary.ClassRegistrationGenerator", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll", "targets": ["D:/Game Development/Interns/Basic Connect Wallet/Library/Bee/artifacts/WebGL/il2cppOutput/UnityClassRegistration.cpp"], "inputs": ["Library/Bee/artifacts/WebGL/ManagedStripped/UnityLinkerToEditorData.json", "D:/Game Development/Interns/Basic Connect Wallet/Temp/StagingArea/Data/Managed/EditorToUnityLinkerData.json", "C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WebGLSupport/modules.asset"], "targetDirectories": []}}